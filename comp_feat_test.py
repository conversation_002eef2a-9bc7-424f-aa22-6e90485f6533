"""
Comprehensive Feature Importance Test - All 4 Algorithms
Menguji fitur-fitur SHAP yang sudah teridentifikasi dengan semua algoritma:
- Logistic Regression
- Random Forest  
- Gradient Boosting
- XGBoost

Berdasarkan hasil SHAP yang sudah ada di:
- results/feature_selection/selected_features/consensus_features_*.txt
- results/feature_selection/shap_features/top_10_*_*.txt
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    logger.warning("XGBoost not available, will skip XGBoost tests")

class ComprehensiveFeatureValidator:
    """Comprehensive validator untuk menguji pentingnya fitur dengan semua algoritma"""
    
    def __init__(self, data_path, target_column='corrected_fatigue_risk'):
        """Initialize validator"""
        self.data_path = data_path
        self.target_column = target_column
        
        # Load data
        logger.info(f"Loading data from: {data_path}")
        self.data = pd.read_csv(data_path)
        
        # Prepare features and target
        self.X = self.data.drop(columns=[target_column])
        self.y = self.data[target_column]
        
        # Encode target for XGBoost (needs numeric labels)
        self.label_encoder = LabelEncoder()
        self.y_encoded = self.label_encoder.fit_transform(self.y)
        
        logger.info(f"Dataset shape: {self.X.shape}")
        logger.info(f"Target distribution: {self.y.value_counts().to_dict()}")
        
        # Setup cross-validation
        self.cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        # Define all algorithms
        self.algorithms = self._define_algorithms()
        
        # Results storage
        self.results = {}
        
    def _define_algorithms(self):
        """Define all algorithms to test"""
        algorithms = {
            'Logistic Regression': {
                'model': Pipeline([
                    ('scaler', StandardScaler()),
                    ('lr', LogisticRegression(random_state=42, max_iter=1000))
                ]),
                'use_encoded': False
            },
            'Random Forest': {
                'model': RandomForestClassifier(n_estimators=100, random_state=42),
                'use_encoded': False
            },
            'Gradient Boosting': {
                'model': GradientBoostingClassifier(n_estimators=100, random_state=42),
                'use_encoded': False
            }
        }
        
        # Add XGBoost if available
        if XGBOOST_AVAILABLE:
            algorithms['XGBoost'] = {
                'model': XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss'),
                'use_encoded': True
            }
        
        return algorithms
    
    def load_consensus_features(self):
        """Load consensus features from file"""
        consensus_files = list(Path("results/feature_selection/selected_features/").glob("consensus_features_*.txt"))
        
        if not consensus_files:
            logger.error("No consensus features file found!")
            return []
        
        consensus_file = consensus_files[0]  # Use the most recent one
        logger.info(f"Loading consensus features from: {consensus_file}")
        
        features = []
        with open(consensus_file, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    # Extract feature name (before the first space)
                    feature_name = line.split()[0].strip()
                    if feature_name in self.X.columns:
                        features.append(feature_name)
        
        logger.info(f"Found {len(features)} consensus features: {features}")
        return features
    
    def load_algorithm_specific_features(self):
        """Load top features for each algorithm"""
        algo_features = {}
        
        # Mapping algorithm names to file patterns
        algo_mapping = {
            'Logistic Regression': 'logistic_regression',
            'Random Forest': 'random_forest', 
            'Gradient Boosting': 'gradient_boosting',
            'XGBoost': 'xgboost'
        }
        
        for algo_name, file_pattern in algo_mapping.items():
            if algo_name == 'XGBoost' and not XGBOOST_AVAILABLE:
                continue
                
            feature_files = list(Path("results/feature_selection/shap_features/").glob(f"top_10_{file_pattern}_*.txt"))
            
            if feature_files:
                feature_file = feature_files[0]
                logger.info(f"Loading {algo_name} features from: {feature_file}")
                
                features = []
                with open(feature_file, 'r') as f:
                    for line in f:
                        if line.strip() and not line.startswith('#') and '.' in line:
                            # Extract feature name (between number and parenthesis)
                            parts = line.split('(')[0].strip()  # Remove SHAP value part
                            if '.' in parts:
                                feature_name = parts.split('.', 1)[1].strip()  # Remove number
                                if feature_name in self.X.columns:
                                    features.append(feature_name)
                
                algo_features[algo_name] = features
                logger.info(f"Found {len(features)} features for {algo_name}")
            else:
                logger.warning(f"No feature file found for {algo_name}")
                algo_features[algo_name] = []
        
        return algo_features
    
    def test_feature_scenarios(self):
        """Test different feature scenarios with all algorithms"""
        logger.info("Starting comprehensive feature testing with all algorithms...")
        
        # Load feature sets
        consensus_features = self.load_consensus_features()
        algo_features = self.load_algorithm_specific_features()
        
        # Filter features that exist in dataset
        consensus_features = [f for f in consensus_features if f in self.X.columns]
        
        # Define test scenarios
        scenarios = {
            'All Features': list(self.X.columns),
            'Consensus Features': consensus_features,
            'Top 5 Consensus': consensus_features[:5],
            'Top 3 Consensus': consensus_features[:3],
        }
        
        # Add algorithm-specific scenarios
        for algo_name, features in algo_features.items():
            if features:
                filtered_features = [f for f in features if f in self.X.columns]
                scenarios[f'Top 10 {algo_name}'] = filtered_features
        
        # Add random feature sets for comparison
        if len(self.X.columns) >= 10:
            np.random.seed(42)
            scenarios['Random 10 Features'] = list(np.random.choice(self.X.columns, 10, replace=False))
        if len(self.X.columns) >= 5:
            np.random.seed(43)
            scenarios['Random 5 Features'] = list(np.random.choice(self.X.columns, 5, replace=False))
        
        # Test each scenario with each algorithm
        for scenario_name, features in scenarios.items():
            if not features:
                logger.warning(f"Skipping {scenario_name} - no features available")
                continue
                
            logger.info(f"\nTesting scenario: {scenario_name} ({len(features)} features)")
            
            scenario_results = {}
            X_subset = self.X[features]
            
            for algo_name, algo_config in self.algorithms.items():
                logger.info(f"  Testing with {algo_name}...")
                
                model = algo_config['model']
                y_to_use = self.y_encoded if algo_config['use_encoded'] else self.y
                
                try:
                    # Cross-validation
                    scores = cross_val_score(model, X_subset, y_to_use, cv=self.cv, scoring='accuracy')
                    
                    scenario_results[algo_name] = {
                        'mean': scores.mean(),
                        'std': scores.std(),
                        'scores': scores.tolist()
                    }
                    
                    logger.info(f"    {algo_name}: {scores.mean():.4f} ± {scores.std():.4f}")
                    
                except Exception as e:
                    logger.error(f"    Error with {algo_name}: {str(e)}")
                    scenario_results[algo_name] = {
                        'mean': 0.0,
                        'std': 0.0,
                        'scores': [0.0] * 5
                    }
            
            self.results[scenario_name] = {
                'n_features': len(features),
                'features': features,
                'algorithm_results': scenario_results
            }
        
        logger.info("✅ Comprehensive feature testing completed!")
        return self.results
    
    def create_comprehensive_visualizations(self, save_plots=True):
        """Create comprehensive visualizations for all algorithms"""
        logger.info("Creating comprehensive visualizations...")
        
        # Create output directory
        output_dir = Path("results/comprehensive_feature_validation")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. Algorithm Performance Comparison
        self._plot_algorithm_comparison(output_dir if save_plots else None)
        
        # 2. Feature Scenario Heatmap
        self._plot_scenario_heatmap(output_dir if save_plots else None)
        
        # 3. Best Features per Algorithm
        self._plot_best_features_per_algorithm(output_dir if save_plots else None)
        
        # 4. Feature Efficiency Analysis
        self._plot_feature_efficiency(output_dir if save_plots else None)
        
        if save_plots:
            logger.info(f"✅ Comprehensive visualizations saved to {output_dir}")
    
    def _plot_algorithm_comparison(self, save_dir):
        """Plot algorithm performance comparison across scenarios"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Algorithm Performance Comparison Across Feature Scenarios', fontsize=16, fontweight='bold')
        
        # Prepare data
        scenarios = list(self.results.keys())
        algorithms = list(self.algorithms.keys())
        
        # Plot 1: Performance by scenario (line plot)
        ax1 = axes[0, 0]
        for algo in algorithms:
            means = []
            stds = []
            for scenario in scenarios:
                if algo in self.results[scenario]['algorithm_results']:
                    means.append(self.results[scenario]['algorithm_results'][algo]['mean'])
                    stds.append(self.results[scenario]['algorithm_results'][algo]['std'])
                else:
                    means.append(0)
                    stds.append(0)
            
            x = range(len(scenarios))
            ax1.errorbar(x, means, yerr=stds, marker='o', label=algo, capsize=3, linewidth=2)
        
        ax1.set_xlabel('Feature Scenarios')
        ax1.set_ylabel('Accuracy')
        ax1.set_title('Performance Across Scenarios')
        ax1.set_xticks(range(len(scenarios)))
        ax1.set_xticklabels(scenarios, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Best performance per algorithm (bar plot)
        ax2 = axes[0, 1]
        best_performances = {}
        best_scenarios = {}
        
        for algo in algorithms:
            best_perf = 0
            best_scenario = ""
            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    perf = results['algorithm_results'][algo]['mean']
                    if perf > best_perf:
                        best_perf = perf
                        best_scenario = scenario
            best_performances[algo] = best_perf
            best_scenarios[algo] = best_scenario
        
        bars = ax2.bar(algorithms, list(best_performances.values()), alpha=0.7)
        ax2.set_ylabel('Best Accuracy')
        ax2.set_title('Best Performance per Algorithm')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # Add value labels and best scenario info
        for i, (bar, algo) in enumerate(zip(bars, algorithms)):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2, height + 0.005,
                    f'{height:.3f}\n({best_scenarios[algo][:15]}...)', 
                    ha='center', va='bottom', fontsize=8)
        
        # Plot 3: Algorithm stability (std comparison)
        ax3 = axes[1, 0]
        avg_stds = {}
        for algo in algorithms:
            stds = []
            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    stds.append(results['algorithm_results'][algo]['std'])
            avg_stds[algo] = np.mean(stds) if stds else 0
        
        bars = ax3.bar(algorithms, list(avg_stds.values()), alpha=0.7, color='orange')
        ax3.set_ylabel('Average Standard Deviation')
        ax3.set_title('Algorithm Stability (Lower is Better)')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # Add value labels
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2, height + 0.001,
                    f'{height:.3f}', ha='center', va='bottom')
        
        # Plot 4: Feature count vs performance
        ax4 = axes[1, 1]
        for algo in algorithms:
            feature_counts = []
            performances = []
            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    feature_counts.append(results['n_features'])
                    performances.append(results['algorithm_results'][algo]['mean'])
            
            if feature_counts and performances:
                ax4.scatter(feature_counts, performances, label=algo, alpha=0.7, s=50)
        
        ax4.set_xlabel('Number of Features')
        ax4.set_ylabel('Accuracy')
        ax4.set_title('Feature Count vs Performance')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'algorithm_comparison.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved algorithm comparison plot")
        
        # plt.show()
    
    def _plot_scenario_heatmap(self, save_dir):
        """Plot heatmap of performance across scenarios and algorithms"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Prepare data for heatmap
        scenarios = list(self.results.keys())
        algorithms = list(self.algorithms.keys())
        
        # Performance heatmap
        perf_matrix = []
        for scenario in scenarios:
            row = []
            for algo in algorithms:
                if algo in self.results[scenario]['algorithm_results']:
                    row.append(self.results[scenario]['algorithm_results'][algo]['mean'])
                else:
                    row.append(0)
            perf_matrix.append(row)
        
        perf_df = pd.DataFrame(perf_matrix, index=scenarios, columns=algorithms)
        
        # Plot performance heatmap
        sns.heatmap(perf_df, annot=True, fmt='.3f', cmap='YlOrRd', ax=ax1, cbar_kws={'label': 'Accuracy'})
        ax1.set_title('Performance Heatmap: Scenarios vs Algorithms')
        ax1.set_xlabel('Algorithms')
        ax1.set_ylabel('Feature Scenarios')
        
        # Standard deviation heatmap
        std_matrix = []
        for scenario in scenarios:
            row = []
            for algo in algorithms:
                if algo in self.results[scenario]['algorithm_results']:
                    row.append(self.results[scenario]['algorithm_results'][algo]['std'])
                else:
                    row.append(0)
            std_matrix.append(row)
        
        std_df = pd.DataFrame(std_matrix, index=scenarios, columns=algorithms)
        
        # Plot std heatmap
        sns.heatmap(std_df, annot=True, fmt='.3f', cmap='YlOrRd_r', ax=ax2, cbar_kws={'label': 'Std Dev'})
        ax2.set_title('Stability Heatmap: Standard Deviation')
        ax2.set_xlabel('Algorithms')
        ax2.set_ylabel('Feature Scenarios')
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'scenario_heatmap.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved scenario heatmap")
        
        # plt.show()
    
    def _plot_best_features_per_algorithm(self, save_dir):
        """Plot best performing feature scenarios for each algorithm"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        algorithms = list(self.algorithms.keys())
        
        for i, algo in enumerate(algorithms):
            if i >= len(axes):
                break
                
            ax = axes[i]
            
            # Get performance for this algorithm across scenarios
            scenarios = []
            performances = []
            stds = []
            feature_counts = []
            
            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    scenarios.append(scenario)
                    performances.append(results['algorithm_results'][algo]['mean'])
                    stds.append(results['algorithm_results'][algo]['std'])
                    feature_counts.append(results['n_features'])
            
            # Sort by performance
            sorted_data = sorted(zip(scenarios, performances, stds, feature_counts), 
                               key=lambda x: x[1], reverse=True)
            
            if sorted_data:
                scenarios, performances, stds, feature_counts = zip(*sorted_data)
                
                # Create bar plot
                bars = ax.bar(range(len(scenarios)), performances, yerr=stds, 
                             capsize=3, alpha=0.7)
                
                # Color bars by feature count
                colors = plt.cm.viridis(np.array(feature_counts) / max(feature_counts))
                for bar, color in zip(bars, colors):
                    bar.set_color(color)
                
                ax.set_title(f'{algo}\nBest Feature Scenarios')
                ax.set_ylabel('Accuracy')
                ax.set_xticks(range(len(scenarios)))
                ax.set_xticklabels([s[:15] + '...' if len(s) > 15 else s for s in scenarios], 
                                  rotation=45, ha='right')
                ax.grid(True, alpha=0.3)
                
                # Add value labels
                for j, (bar, perf, std, count) in enumerate(zip(bars, performances, stds, feature_counts)):
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.005,
                           f'{perf:.3f}\n({count}f)', ha='center', va='bottom', fontsize=8)
        
        # Hide unused subplots
        for i in range(len(algorithms), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'best_features_per_algorithm.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved best features per algorithm plot")
        
        # plt.show()
    
    def _plot_feature_efficiency(self, save_dir):
        """Plot feature efficiency analysis"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # Calculate efficiency metrics
        algorithms = list(self.algorithms.keys())
        
        # Plot 1: Performance vs Feature Count
        for algo in algorithms:
            feature_counts = []
            performances = []
            
            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    feature_counts.append(results['n_features'])
                    performances.append(results['algorithm_results'][algo]['mean'])
            
            if feature_counts and performances:
                # Sort by feature count
                sorted_data = sorted(zip(feature_counts, performances))
                feature_counts, performances = zip(*sorted_data)
                
                ax1.plot(feature_counts, performances, marker='o', label=algo, linewidth=2, markersize=6)
        
        ax1.set_xlabel('Number of Features')
        ax1.set_ylabel('Accuracy')
        ax1.set_title('Performance vs Feature Count')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Efficiency Score (Performance / Feature Count)
        efficiency_data = {}
        for algo in algorithms:
            efficiencies = []
            scenario_names = []
            
            for scenario, results in self.results.items():
                if algo in results['algorithm_results'] and results['n_features'] > 0:
                    performance = results['algorithm_results'][algo]['mean']
                    feature_count = results['n_features']
                    efficiency = performance / feature_count
                    efficiencies.append(efficiency)
                    scenario_names.append(scenario)
            
            if efficiencies:
                # Find best efficiency
                best_idx = np.argmax(efficiencies)
                efficiency_data[algo] = {
                    'best_efficiency': efficiencies[best_idx],
                    'best_scenario': scenario_names[best_idx]
                }
        
        if efficiency_data:
            algos = list(efficiency_data.keys())
            efficiencies = [efficiency_data[algo]['best_efficiency'] for algo in algos]
            
            bars = ax2.bar(algos, efficiencies, alpha=0.7, color='skyblue')
            ax2.set_ylabel('Best Efficiency (Accuracy/Feature Count)')
            ax2.set_title('Feature Efficiency by Algorithm')
            ax2.tick_params(axis='x', rotation=45)
            ax2.grid(True, alpha=0.3)
            
            # Add value labels and scenario info
            for bar, algo in zip(bars, algos):
                height = bar.get_height()
                scenario = efficiency_data[algo]['best_scenario']
                ax2.text(bar.get_x() + bar.get_width()/2, height + 0.001,
                        f'{height:.3f}\n({scenario[:10]}...)', 
                        ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'feature_efficiency.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved feature efficiency plot")
        
        # plt.show()

    def generate_comprehensive_report(self):
        """Generate comprehensive validation report for all algorithms"""
        report = []
        report.append("="*80)
        report.append("COMPREHENSIVE FEATURE IMPORTANCE VALIDATION REPORT - ALL ALGORITHMS")
        report.append("="*80)

        # Dataset info
        report.append(f"\n📊 DATASET INFORMATION:")
        report.append(f"   • Total samples: {len(self.data)}")
        report.append(f"   • Features: {self.X.shape[1]}")
        report.append(f"   • Target distribution: {dict(self.y.value_counts())}")
        report.append(f"   • Algorithms tested: {', '.join(self.algorithms.keys())}")

        # Overall best performance
        report.append(f"\n🏆 OVERALL BEST PERFORMANCE:")
        best_overall = {}
        for scenario, results in self.results.items():
            for algo, algo_results in results['algorithm_results'].items():
                key = f"{scenario} + {algo}"
                best_overall[key] = {
                    'performance': algo_results['mean'],
                    'std': algo_results['std'],
                    'scenario': scenario,
                    'algorithm': algo,
                    'n_features': results['n_features']
                }

        # Sort by performance
        sorted_overall = sorted(best_overall.items(), key=lambda x: x[1]['performance'], reverse=True)

        report.append("   Top 5 combinations:")
        for i, (key, data) in enumerate(sorted_overall[:5]):
            report.append(f"   {i+1}. {data['algorithm']} + {data['scenario']}")
            report.append(f"      • Performance: {data['performance']:.4f} ± {data['std']:.4f}")
            report.append(f"      • Features: {data['n_features']}")

        # Best performance per algorithm
        report.append(f"\n🎯 BEST PERFORMANCE PER ALGORITHM:")
        for algo in self.algorithms.keys():
            best_perf = 0
            best_scenario = ""
            best_std = 0
            best_features = 0

            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    perf = results['algorithm_results'][algo]['mean']
                    if perf > best_perf:
                        best_perf = perf
                        best_scenario = scenario
                        best_std = results['algorithm_results'][algo]['std']
                        best_features = results['n_features']

            report.append(f"   • {algo}:")
            report.append(f"     - Best: {best_perf:.4f} ± {best_std:.4f}")
            report.append(f"     - Scenario: {best_scenario}")
            report.append(f"     - Features: {best_features}")

        # Algorithm stability analysis
        report.append(f"\n📊 ALGORITHM STABILITY ANALYSIS:")
        stability_scores = {}
        for algo in self.algorithms.keys():
            stds = []
            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    stds.append(results['algorithm_results'][algo]['std'])
            stability_scores[algo] = np.mean(stds) if stds else 0

        # Sort by stability (lower std = more stable)
        sorted_stability = sorted(stability_scores.items(), key=lambda x: x[1])

        report.append("   Ranking by stability (most stable first):")
        for i, (algo, avg_std) in enumerate(sorted_stability):
            stability_level = "EXCELLENT" if avg_std < 0.02 else \
                            "GOOD" if avg_std < 0.04 else \
                            "MODERATE" if avg_std < 0.06 else "POOR"
            report.append(f"   {i+1}. {algo}: {avg_std:.4f} ({stability_level})")

        # Feature efficiency analysis
        report.append(f"\n⚡ FEATURE EFFICIENCY ANALYSIS:")
        efficiency_scores = {}
        for algo in self.algorithms.keys():
            best_efficiency = 0
            best_eff_scenario = ""

            for scenario, results in self.results.items():
                if algo in results['algorithm_results'] and results['n_features'] > 0:
                    performance = results['algorithm_results'][algo]['mean']
                    efficiency = performance / results['n_features']
                    if efficiency > best_efficiency:
                        best_efficiency = efficiency
                        best_eff_scenario = scenario

            efficiency_scores[algo] = {
                'efficiency': best_efficiency,
                'scenario': best_eff_scenario
            }

        # Sort by efficiency
        sorted_efficiency = sorted(efficiency_scores.items(),
                                 key=lambda x: x[1]['efficiency'], reverse=True)

        report.append("   Ranking by feature efficiency (performance/feature_count):")
        for i, (algo, data) in enumerate(sorted_efficiency):
            report.append(f"   {i+1}. {algo}: {data['efficiency']:.4f}")
            report.append(f"      • Best scenario: {data['scenario']}")

        # SHAP vs Random comparison
        report.append(f"\n🎲 SHAP vs RANDOM FEATURES COMPARISON:")
        for algo in self.algorithms.keys():
            shap_performances = []
            random_performances = []

            for scenario, results in self.results.items():
                if algo in results['algorithm_results']:
                    perf = results['algorithm_results'][algo]['mean']
                    if 'Consensus' in scenario or 'Top 10' in scenario:
                        shap_performances.append(perf)
                    elif 'Random' in scenario:
                        random_performances.append(perf)

            if shap_performances and random_performances:
                avg_shap = np.mean(shap_performances)
                avg_random = np.mean(random_performances)
                advantage = avg_shap - avg_random

                report.append(f"   • {algo}:")
                report.append(f"     - SHAP features avg: {avg_shap:.4f}")
                report.append(f"     - Random features avg: {avg_random:.4f}")
                report.append(f"     - SHAP advantage: {advantage:.4f} ({advantage*100:.2f}%)")

        # Recommendations
        report.append(f"\n💡 RECOMMENDATIONS:")

        # Best overall combination
        best_combo = sorted_overall[0][1]
        report.append(f"   • BEST COMBINATION: {best_combo['algorithm']} + {best_combo['scenario']}")
        report.append(f"     - Performance: {best_combo['performance']:.4f}")
        report.append(f"     - Features needed: {best_combo['n_features']}")

        # Most stable algorithm
        most_stable = sorted_stability[0][0]
        report.append(f"   • MOST STABLE: {most_stable} (avg std: {sorted_stability[0][1]:.4f})")

        # Most efficient algorithm
        most_efficient = sorted_efficiency[0][0]
        report.append(f"   • MOST EFFICIENT: {most_efficient} (efficiency: {sorted_efficiency[0][1]['efficiency']:.4f})")

        # General recommendations
        report.append(f"   • For PRODUCTION: Use {best_combo['algorithm']} with {best_combo['scenario']}")
        report.append(f"   • For STABILITY: Consider {most_stable} if consistency is priority")
        report.append(f"   • For EFFICIENCY: {most_efficient} provides best performance per feature")

        # Feature selection insights
        consensus_results = self.results.get('Consensus Features', {})
        all_features_results = self.results.get('All Features', {})

        if consensus_results and all_features_results:
            report.append(f"\n🔍 FEATURE SELECTION INSIGHTS:")
            for algo in self.algorithms.keys():
                if (algo in consensus_results['algorithm_results'] and
                    algo in all_features_results['algorithm_results']):

                    consensus_perf = consensus_results['algorithm_results'][algo]['mean']
                    all_perf = all_features_results['algorithm_results'][algo]['mean']
                    perf_diff = consensus_perf - all_perf

                    consensus_features = consensus_results['n_features']
                    all_features = all_features_results['n_features']
                    feature_reduction = (all_features - consensus_features) / all_features * 100

                    report.append(f"   • {algo}:")
                    report.append(f"     - Feature reduction: {feature_reduction:.1f}% ({all_features} → {consensus_features})")
                    report.append(f"     - Performance change: {perf_diff:+.4f}")

                    if perf_diff >= -0.01:  # Within 1% of all features
                        report.append(f"     - ✅ Consensus features maintain performance")
                    else:
                        report.append(f"     - ⚠️ Notable performance drop with feature reduction")

        report.append("="*80)

        # Save and print report
        report_text = '\n'.join(report)

        output_dir = Path("results/comprehensive_feature_validation")
        output_dir.mkdir(parents=True, exist_ok=True)
        report_file = output_dir / 'comprehensive_validation_report.txt'

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)

        print(report_text)
        logger.info(f"✅ Comprehensive report saved to {report_file}")

        return report_text


def main():
    """Main function"""
    # Check for data file
    data_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Data file not found: {data_path}")
        
        # Look for alternatives
        alternatives = [
            "dataset/processed/safe_ml_bias_corrected_dataset.csv",
            "dataset/processed/safe_ml_title_only_dataset.csv",
            "dataset/processed/fatigue_risk_classified_dataset.csv"
        ]
        
        for alt_path in alternatives:
            if Path(alt_path).exists():
                print(f"✅ Using alternative: {alt_path}")
                data_path = alt_path
                break
        else:
            print("❌ No suitable data file found. Please run the main pipeline first.")
            return
    
    # Check for SHAP results
    shap_dir = Path("results/feature_selection")
    if not shap_dir.exists():
        print("❌ SHAP results not found. Please run main1.py first.")
        return
    
    print("🚀 Starting comprehensive feature importance test with all algorithms...")
    print(f"📁 Using data: {data_path}")
    
    # Initialize validator
    validator = ComprehensiveFeatureValidator(data_path)
    
    # Run comprehensive tests
    results = validator.test_feature_scenarios()
    
    if not results:
        print("❌ No results generated. Check your data and feature files.")
        return
    
    # Create comprehensive visualizations
    validator.create_comprehensive_visualizations(save_plots=True)

    # Generate comprehensive report
    validator.generate_comprehensive_report()

    print("\n🎉 Comprehensive feature test with all algorithms completed!")
    print("📁 Results saved to: results/comprehensive_feature_validation/")


if __name__ == "__main__":
    main()
