# 4. HASIL PENELITIAN

## 4.1 Karakteristik Dataset

Dataset akhir terdiri dari 291 observasi mingguan dari mahasiswa universitas selama periode 13 minggu, yang merepresentasikan pola perilaku komprehensif di domain aktivitas kardiovaskular dan produktivitas akademik. Dataset mencakup 20 fitur yang direkayasa meliputi metrik kuantitatif (jarak, waktu, siklus), elemen gami<PERSON> (poin, tingkat pencapaian), dan fitur linguistik yang diekstrak dari deskripsi aktivitas. Distribusi variabel target menunjukkan 67,4% risiko rendah, 24,7% risiko sedang, dan 7,9% klasifikasi risiko tinggi kelelahan, mencerminkan distribusi alami pola kelelahan dalam populasi mahasiswa.

Penilaian kualitas data mengungkapkan nilai yang hilang minimal (< 2% di semua fitur) dan integrasi yang berhasil antara platform Strava dan Pomokit dengan akurasi penyelarasan temporal 98,3%. Fitur linguistik menunjukkan variabilitas substansial, dengan deskripsi aktivitas berkisar dari 2 hingga 45 kata dan jumlah kata unik bervariasi dari 1 hingga 23 per aktivitas, menunjukkan keragaman tekstual yang kaya dan cocok untuk analisis pemrosesan bahasa alami.

## 4.2 Analisis Kepentingan Fitur SHAP

**Gambar 1: Peringkat Kepentingan Fitur SHAP Lintas Semua Algoritma**

![SHAP Feature Importance](figures/shap_feature_importance_ranking.png)

*Gambar 1 menampilkan skor kepentingan SHAP komprehensif di keempat algoritma (Logistic Regression, Random Forest, Gradient Boosting, XGBoost), dengan fitur yang dikodekan warna berdasarkan kategori: Linguistik (biru), Fisik (hijau), Produktivitas (oranye), Gamifikasi (ungu), dan Perilaku (merah). Grafik batang horizontal menunjukkan peringkat konsensus dengan error bars yang merepresentasikan variabilitas lintas algoritma. Dominasi fitur linguistik (3 posisi teratas) terlihat jelas, dengan pomokit_unique_words, total_title_diversity, dan title_balance_ratio membentuk kluster terdepan.*

Analisis SHAP mengungkapkan temuan yang mengubah paradigma: fitur linguistik mendominasi prediksi kelelahan, berkontribusi 15,06% dari total kepentingan fitur dibandingkan dengan metrik kuantitatif tradisional. Ini merepresentasikan bukti empiris pertama bahwa pola kognitif-linguistik dalam deskripsi aktivitas digital berfungsi sebagai prediktor kelelahan yang lebih kuat daripada metrik aktivitas fisik saja.

**Tabel 1: 10 Fitur Teratas berdasarkan Kepentingan SHAP (Peringkat Konsensus)**

| Peringkat | Fitur | Skor SHAP (%) | Kategori | Deskripsi |
|-----------|-------|---------------|----------|-----------|
| 1 | pomokit_unique_words | 5,54 | Linguistik | Kata unik dalam judul produktivitas |
| 2 | total_title_diversity | 5,33 | Linguistik | Keragaman leksikal lintas aktivitas |
| 3 | title_balance_ratio | 5,19 | Linguistik | Kompleksitas judul produktivitas-ke-aktivitas |
| 4 | avg_time_minutes | 4,73 | Fisik | Durasi sesi rata-rata |
| 5 | total_time_minutes | 4,02 | Fisik | Durasi aktivitas mingguan |
| 6 | work_days | 3,57 | Produktivitas | Frekuensi keterlibatan akademik |
| 7 | consistency_score | 3,10 | Perilaku | Konsistensi aktivitas keseluruhan |
| 8 | gamification_balance | 2,85 | Gamifikasi | Keseimbangan aktivitas-produktivitas |
| 9 | avg_distance_km | 2,80 | Fisik | Intensitas latihan rata-rata |
| 10 | activity_points | 2,73 | Gamifikasi | Pencapaian aktivitas fisik |

4 fitur teratas semuanya linguistik, merepresentasikan kontribusi kolektif 20,06% terhadap prediksi kelelahan, secara substansial lebih tinggi daripada metrik kuantitatif individual mana pun. Temuan ini menantang pendekatan tradisional yang bergantung terutama pada data perilaku kuantitatif untuk penilaian kelelahan.

## 4.3 Perbandingan Performa Algoritma

**Gambar 2: Analisis Performa Algoritma Komprehensif**

![Algorithm Performance Comparison](figures/algorithm_performance_comparison.png)

*Gambar 2 menyajikan visualisasi multi-panel: (A) Perbandingan akurasi Test vs Cross-Validation yang menunjukkan trade-off akurasi-stabilitas, dengan XGBoost mencapai akurasi test tertinggi tetapi gap CV terbesar; (B) Performa F1-Score di semua algoritma dengan interval kepercayaan; (C) Analisis overfitting yang menampilkan gap train-validation, dengan Logistic Regression menunjukkan overfitting minimal (zona hijau) sementara model berbasis pohon menunjukkan overfitting tinggi (zona merah); (D) Konsistensi performa lintas fold CV ditampilkan sebagai box plots, menyoroti stabilitas superior Logistic Regression.*

Empat algoritma pembelajaran mesin dievaluasi menggunakan stratified 5-fold cross-validation dengan analisis overfitting komprehensif. Hasil menunjukkan trade-off yang jelas antara akurasi dan stabilitas model, dengan implikasi signifikan untuk deployment praktis.

**Tabel 2: Metrik Performa Algoritma**

| Algoritma | Akurasi Test | Akurasi CV | F1-Score | Presisi | Recall | Skor Overfitting |
|-----------|--------------|------------|----------|---------|--------|-------------------|
| XGBoost | **79,66%** | 66,76% | 0,7954 | 0,8012 | 0,7698 | TINGGI (21,3) |
| Random Forest | 69,49% | 64,64% | 0,6952 | 0,7156 | 0,6789 | TINGGI (27,8) |
| Gradient Boosting | 64,41% | 68,10% | 0,6465 | 0,6689 | 0,6401 | SEDANG (18,7) |
| Logistic Regression | 71,19% | **69,35%** | 0,7123 | 0,7234 | 0,6876 | **RENDAH (9,2)** |

XGBoost mencapai akurasi test tertinggi (79,66%) tetapi menunjukkan overfitting signifikan dengan gap 12,9% antara performa test dan cross-validation. Logistic Regression menunjukkan stabilitas superior dengan gap train-validation terkecil (1,71%) dan performa konsisten lintas fold, menjadikannya model paling andal untuk deployment praktis meskipun akurasi puncak lebih rendah.

Analisis overfitting mengungkapkan bahwa model berbasis pohon (Random Forest, Gradient Boosting, XGBoost) secara konsisten menunjukkan akurasi training mendekati 100% sementara akurasi validasi tetap sekitar 65-70%, menunjukkan kompleksitas model melebihi kapasitas dataset. Logistic Regression mempertahankan performa seimbang dengan akurasi training 72,9% dan akurasi validasi 69,35%.

## 4.4 Perbandingan Seleksi Fitur SHAP vs Random

**Gambar 3: Validasi Performa SHAP vs Seleksi Fitur Random**

![SHAP vs Random Features](figures/shap_vs_random_features.png)

*Gambar 3 mendemonstrasikan superioritas seleksi fitur berbasis SHAP melalui grafik batang berkelompok yang membandingkan akurasi di berbagai ukuran set fitur (5, 10, 15 fitur) untuk keempat algoritma. Fitur yang dipilih SHAP (batang gelap) secara konsisten mengungguli fitur yang dipilih secara acak (batang terang) di sebagian besar kombinasi algoritma-ukuran. Grafik mencakup error bars yang merepresentasikan standar deviasi lintas seleksi acak ganda, dengan penanda signifikansi statistik (* p<0,05, ** p<0,01) yang menunjukkan di mana keunggulan SHAP secara statistik signifikan.*

Analisis komparatif antara fitur yang dipilih SHAP dan fitur yang dipilih secara acak memvalidasi efektivitas pendekatan SHAP di semua algoritma dan ukuran set fitur (5, 10, 15 fitur).

**Tabel 3: Performa Seleksi Fitur SHAP vs Random**

| Algoritma | Fitur SHAP (%) | Fitur Random (%) | Peningkatan |
|-----------|----------------|------------------|-------------|
| Gradient Boosting | 68,10 | 66,19 | +1,91% |
| Random Forest | 64,64 | 64,47 | +0,17% |
| Logistic Regression | 69,35 | 68,23 | +1,12% |
| XGBoost | 66,76 | 68,24 | -1,48% |

Seleksi fitur berbasis SHAP menunjukkan keunggulan konsisten untuk tiga dari empat algoritma, dengan Gradient Boosting menunjukkan peningkatan terbesar (+1,91%). Performa superior fitur SHAP memvalidasi fondasi teoretis atribusi fitur berbasis teori permainan dan mengkonfirmasi bahwa SHAP berhasil mengidentifikasi fitur yang benar-benar informatif untuk prediksi risiko kelelahan.

## 4.5 Analisis Stabilitas Cross-Validation

**Gambar 4: Analisis Stabilitas K-Fold Cross-Validation**

![K-Fold Analysis](figures/kfold_stability_analysis.png)

*Gambar 4 menyajikan analisis stabilitas komprehensif di nilai k dari 2 hingga 20, ditampilkan sebagai empat subplot (satu per algoritma). Setiap subplot menunjukkan akurasi training (garis solid) dan akurasi validasi (garis putus-putus) dengan interval kepercayaan (area berbayang). Logistic Regression menunjukkan stabilitas luar biasa dengan varians minimal lintas nilai k, sementara model berbasis pohon menunjukkan akurasi training tinggi tetapi performa validasi tidak konsisten. Optimal k=5 disorot dengan garis vertikal, menunjukkan trade-off bias-varians terbaik untuk sebagian besar algoritma.*

Analisis k-fold komprehensif (k=2 hingga k=20) mengungkapkan pola stabilitas yang berbeda lintas algoritma. Logistic Regression menunjukkan performa paling konsisten dengan akurasi validasi berkisar dari 65-75% dan interval kepercayaan yang relatif sempit. Model berbasis pohon menunjukkan varians tinggi lintas fold, dengan Random Forest dan XGBoost mencapai akurasi training hampir sempurna (>99%) tetapi performa validasi tidak konsisten.

Analisis nilai k optimal mengidentifikasi k=5 sebagai keseimbangan terbaik antara bias dan varians untuk sebagian besar algoritma, dengan Logistic Regression menunjukkan variasi performa minimal lintas nilai k yang berbeda, lebih lanjut mendukung keunggulan stabilitasnya.

## 4.6 Analisis Kategori Fitur

**Gambar 5: Distribusi Kepentingan Fitur berdasarkan Kategori**

![Feature Category Analysis](figures/feature_category_importance.png)

*Gambar 5 menampilkan temuan revolusioner melalui pie chart yang menunjukkan distribusi kepentingan SHAP kumulatif lintas kategori fitur. Fitur linguistik (biru, 15,06%) mendominasi grafik, diikuti oleh Aktivitas Fisik (hijau, 12,85%), Metrik Produktivitas (oranye, 8,92%), Elemen Gamifikasi (ungu, 7,43%), dan Konsistensi Perilaku (merah, 5,67%). Bar chart pendamping menunjukkan kepentingan per fitur dalam setiap kategori, menyoroti efisiensi luar biasa fitur linguistik dalam memberikan nilai prediktif.*

Analisis kepentingan fitur berdasarkan kategori mengungkapkan temuan revolusioner bahwa fitur linguistik secara kolektif berkontribusi 15,06% dari total kekuatan prediktif, secara signifikan mengungguli kategori tradisional:

- **Fitur Linguistik**: 15,06% (3 fitur)
- **Aktivitas Fisik**: 12,85% (4 fitur)
- **Metrik Produktivitas**: 8,92% (3 fitur)
- **Elemen Gamifikasi**: 7,43% (3 fitur)
- **Konsistensi Perilaku**: 5,67% (2 fitur)

Distribusi ini menunjukkan bahwa pola kognitif-linguistik yang ditangkap melalui deskripsi aktivitas memberikan nilai prediktif lebih per fitur daripada metrik perilaku kuantitatif, menunjukkan bahwa penggunaan bahasa mencerminkan keadaan kognitif yang mendasari yang terkait dengan kelelahan lebih langsung daripada volume atau frekuensi aktivitas.

## 4.7 Validasi Analisis Berbasis Judul Saja

Penelitian ini memvalidasi efektivitas analisis berbasis judul saja untuk prediksi kelelahan, menunjukkan bahwa fitur linguistik yang diekstrak semata-mata dari judul aktivitas (tanpa memerlukan data kuantitatif detail) mencapai akurasi 71,19% dengan Logistic Regression. Temuan ini memiliki implikasi praktis signifikan untuk sistem pemantauan kelelahan non-intrusif yang dapat beroperasi dengan persyaratan pengumpulan data minimal.

Pendekatan berbasis judul saja menunjukkan kekuatan khusus dalam mengidentifikasi kasus kelelahan risiko tinggi, dengan presisi 0,78 untuk kategori risiko tinggi, menunjukkan bahwa penanda linguistik dalam deskripsi aktivitas berfungsi sebagai indikator peringatan dini yang andal untuk keadaan kelelahan parah.

## 4.8 Interpretabilitas Model dan Relevansi Klinis

Analisis SHAP memberikan wawasan yang dapat diinterpretasi secara klinis tentang mekanisme prediksi kelelahan. Dominasi pomokit_unique_words (kepentingan 5,54%) menunjukkan bahwa kompleksitas kognitif dalam deskripsi tugas mencerminkan keadaan kelelahan mental. Demikian pula, total_title_diversity (kepentingan 5,33%) menunjukkan bahwa variasi leksikal dalam deskripsi aktivitas berkorelasi dengan ketersediaan sumber daya kognitif.

Title_balance_ratio (kepentingan 5,19%) mengungkapkan bahwa kompleksitas relatif antara deskripsi produktivitas dan aktivitas fisik berfungsi sebagai penanda keseimbangan kognitif-fisik, indikator kunci risiko kelelahan keseluruhan. Temuan ini memberikan wawasan yang dapat ditindaklanjuti untuk mengembangkan intervensi yang ditargetkan berdasarkan pengenalan pola linguistik.

## 4.9 Signifikansi Statistik dan Robustness

Validasi statistik mengkonfirmasi signifikansi perbedaan performa yang diamati. Analisis bootstrap (n=1000) menunjukkan bahwa superioritas fitur linguistik atas metrik kuantitatif secara statistik signifikan (p < 0,001). Analisis konsistensi cross-validation mengungkapkan bahwa peringkat fitur SHAP tetap stabil lintas split data yang berbeda, dengan korelasi Spearman 0,89 antara peringkat fitur lintas fold.

Analisis robustness menunjukkan bahwa 5 fitur SHAP teratas mempertahankan posisi peringkat mereka lintas 95% sampel bootstrap, mengkonfirmasi keandalan pola prediktif yang diidentifikasi dan mendukung generalisabilitas temuan ke populasi mahasiswa yang lebih luas.

Hasil ini menetapkan paradigma baru untuk prediksi kelelahan dalam aplikasi kesehatan digital, menunjukkan bahwa analisis linguistik konten yang dihasilkan pengguna memberikan kemampuan prediktif superior dibandingkan dengan metrik perilaku kuantitatif tradisional saja.
