# 🤖 Panduan Prediksi Risiko <PERSON>an

Panduan lengkap untuk menggunakan model machine learning yang telah dilatih untuk memprediksi risiko kelelahan.

## 📋 Ringkasan Model

- **Algoritma**: Random Forest
- **Akurasi**: 93.22%
- **F1-Score**: 93.21%
- **Cross-Validation**: 95.68% (±2.73%)
- **Jum<PERSON>**: 18 fitur
- **Kelas Prediksi**: `low_risk`, `medium_risk`, `high_risk`

## 🚀 Cara Cepat Memulai

### 1. Prediksi Cepat dengan Data Contoh
```bash
python simple_predict.py
```

### 2. Demo <PERSON>p
```bash
python predict_with_model.py
```

### 3. Buat Template Input
```bash
python create_prediction_template.py
```

## 📊 Fitur yang Dibutuhkan

Model membutuhkan 18 fitur berikut:

| No | Fitur | Deskripsi | Contoh |
|----|-------|-----------|--------|
| 1  | `productivity_points` | Poin produktivitas pengguna | 85.5 |
| 2  | `total_time_minutes` | Total waktu aktivitas (menit) | 1800 |
| 3  | `strava_unique_words` | Jumlah kata unik dalam judul Strava | 12 |
| 4  | `achievement_rate` | Tingkat pencapaian | 0.85 |
| 5  | `avg_cycles` | Rata-rata siklus aktivitas | 3.2 |
| 6  | `avg_distance_km` | Rata-rata jarak (km) | 5.4 |
| 7  | `total_title_diversity` | Keragaman judul aktivitas | 8.5 |
| 8  | `pomokit_title_count` | Jumlah judul Pomokit | 15 |
| 9  | `activity_points` | Poin aktivitas | 120.0 |
| 10 | `pomokit_title_length` | Panjang judul Pomokit | 25.3 |
| 11 | `title_balance_ratio` | Rasio keseimbangan judul | 0.75 |
| 12 | `strava_title_count` | Jumlah judul Strava | 8 |
| 13 | `total_distance_km` | Total jarak (km) | 45.2 |
| 14 | `strava_title_length` | Panjang judul Strava | 18.5 |
| 15 | `pomokit_unique_words` | Kata unik Pomokit | 10 |
| 16 | `weekly_efficiency` | Efisiensi mingguan | 0.82 |
| 17 | `gamification_balance` | Saldo gamifikasi | 150.0 |
| 18 | `avg_time_minutes` | Rata-rata waktu (menit) | 45.5 |

## 💻 Penggunaan Programatik

### Prediksi Single Sample

```python
import sys
sys.path.append('src')
from utils.data_utils import load_model_artifacts

# Load model
artifacts = load_model_artifacts("results/clean_production_model")
model = artifacts['pipeline']
features = artifacts['features']
label_encoder = artifacts['label_encoder']

# Siapkan data (contoh)
sample_data = {
    'productivity_points': 85.5,
    'total_time_minutes': 1800,
    'strava_unique_words': 12,
    'achievement_rate': 0.85,
    'avg_cycles': 3.2,
    'avg_distance_km': 5.4,
    'total_title_diversity': 8.5,
    'pomokit_title_count': 15,
    'activity_points': 120.0,
    'pomokit_title_length': 25.3,
    'title_balance_ratio': 0.75,
    'strava_title_count': 8,
    'total_distance_km': 45.2,
    'strava_title_length': 18.5,
    'pomokit_unique_words': 10,
    'weekly_efficiency': 0.82,
    'gamification_balance': 150.0,
    'avg_time_minutes': 45.5
}

# Convert ke DataFrame
import pandas as pd
df = pd.DataFrame([sample_data])
X = df[features]

# Prediksi
prediction = model.predict(X)[0]
probabilities = model.predict_proba(X)[0]
predicted_class = label_encoder.inverse_transform([prediction])[0]

print(f"Prediksi: {predicted_class}")
print(f"Probabilitas: {probabilities}")
```

### Menggunakan Class FatigueRiskPredictor

```python
from predict_with_model import FatigueRiskPredictor

# Initialize predictor
predictor = FatigueRiskPredictor()

# Single prediction
predicted_class, probabilities = predictor.predict_single(sample_data)
print(f"Prediksi: {predicted_class}")
print(f"Probabilitas: {probabilities}")

# Batch prediction dari CSV
results = predictor.predict_from_csv("your_data.csv", "results.csv")
```

## 📁 Format Input Data

### CSV Format
Buat file CSV dengan kolom sesuai 18 fitur yang dibutuhkan:

```csv
productivity_points,total_time_minutes,strava_unique_words,achievement_rate,avg_cycles,avg_distance_km,total_title_diversity,pomokit_title_count,activity_points,pomokit_title_length,title_balance_ratio,strava_title_count,total_distance_km,strava_title_length,pomokit_unique_words,weekly_efficiency,gamification_balance,avg_time_minutes
85.5,1800,12,0.85,3.2,5.4,8.5,15,120.0,25.3,0.75,8,45.2,18.5,10,0.82,150.0,45.5
92.3,2400,8,0.92,2.8,8.1,12.3,10,135.5,30.2,0.68,12,52.8,22.1,15,0.88,200.5,52.3
```

### Dictionary Format (untuk single prediction)
```python
data = {
    'productivity_points': 85.5,
    'total_time_minutes': 1800,
    # ... semua 18 fitur
}
```

## 📊 Output Prediksi

### Kelas Prediksi
- **`low_risk`**: Risiko kelelahan rendah
- **`medium_risk`**: Risiko kelelahan sedang  
- **`high_risk`**: Risiko kelelahan tinggi

### Probabilitas
Model memberikan probabilitas untuk setiap kelas:
```python
{
    'low_risk': 0.1234,     # 12.34%
    'medium_risk': 0.2345,  # 23.45%
    'high_risk': 0.6421     # 64.21%
}
```

## 🛠️ Troubleshooting

### Error: Model tidak ditemukan
```bash
# Pastikan model sudah dilatih
python main1.py --ml-only
```

### Error: Missing features
- Pastikan semua 18 fitur tersedia dalam data input
- Gunakan template yang disediakan: `prediction_template_empty.csv`
- Periksa nama kolom harus persis sama

### Error: Invalid data types
- Pastikan semua nilai numerik (float/int)
- Tidak ada nilai kosong (NaN)
- Binary features (0 atau 1) untuk fitur boolean

## 📈 Tips Penggunaan

1. **Kualitas Data**: Pastikan data input berkualitas dan sesuai dengan data training
2. **Normalisasi**: Model sudah include preprocessing, tidak perlu normalisasi manual
3. **Batch Processing**: Untuk data besar, gunakan `predict_batch()` atau `predict_from_csv()`
4. **Monitoring**: Pantau probabilitas prediksi untuk menilai confidence level

## 🔧 File Pendukung

- `predict_with_model.py`: Script lengkap dengan class FatigueRiskPredictor
- `simple_predict.py`: Script sederhana untuk prediksi cepat
- `create_prediction_template.py`: Membuat template input CSV
- `prediction_template.csv`: Template dengan contoh data
- `prediction_template_empty.csv`: Template kosong untuk input data

## 📞 Support

Jika mengalami masalah:
1. Pastikan model sudah dilatih dengan `python main1.py --ml-only`
2. Periksa format data input sesuai dengan template
3. Pastikan semua dependencies terinstall
4. Cek log error untuk detail masalah
