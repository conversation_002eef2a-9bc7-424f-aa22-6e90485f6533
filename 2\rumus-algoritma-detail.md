# RUMUS DAN ALGORITMA DETAIL PENELITIAN

## Daftar Isi
1. [Rumus Metrik <PERSON>](#1-rumus-metrik-evaluasi)
2. [Algoritma Machine Learning](#2-algoritma-machine-learning)
3. [SHAP (Shapley Values)](#3-shap-shapley-values)
4. [Feature Engineering](#4-feature-engineering)
5. [Cross-Validation](#5-cross-validation)

---

## 1. RUMUS METRIK EVALUASI

### 1.1 Confusion Matrix

Untuk klasifikasi multi-kelas (low_risk, medium_risk, high_risk):

```
                 Predicted
              L    M    H
Actual   L  [TL] [ML] [HL]
         M  [LM] [TM] [HM]  
         H  [LH] [MH] [TH]
```

### 1.2 Accuracy

**Rumus Umum**:
```
Accuracy = (Correct Predictions) / (Total Predictions)
Accuracy = (TL + TM + TH) / (TL + TM + TH + ML + HL + LM + HM + LH + MH)
```

**Implementasi Python**:
```python
from sklearn.metrics import accuracy_score
accuracy = accuracy_score(y_true, y_pred)
```

### 1.3 Precision (Macro)

**Rumus per Kelas**:
```
Precision_L = TL / (TL + LM + LH)
Precision_M = TM / (TM + ML + MH)  
Precision_H = TH / (TH + HL + HM)
```

**Macro Average**:
```
Precision_Macro = (Precision_L + Precision_M + Precision_H) / 3
```

**Implementasi Python**:
```python
from sklearn.metrics import precision_score
precision_macro = precision_score(y_true, y_pred, average='macro')
```

### 1.4 Recall (Macro)

**Rumus per Kelas**:
```
Recall_L = TL / (TL + ML + HL)
Recall_M = TM / (TM + LM + HM)
Recall_H = TH / (TH + LH + MH)
```

**Macro Average**:
```
Recall_Macro = (Recall_L + Recall_M + Recall_H) / 3
```

**Implementasi Python**:
```python
from sklearn.metrics import recall_score
recall_macro = recall_score(y_true, y_pred, average='macro')
```

### 1.5 F1-Score (Macro)

**Rumus per Kelas**:
```
F1_L = 2 × (Precision_L × Recall_L) / (Precision_L + Recall_L)
F1_M = 2 × (Precision_M × Recall_M) / (Precision_M + Recall_M)
F1_H = 2 × (Precision_H × Recall_H) / (Precision_H + Recall_H)
```

**Macro Average**:
```
F1_Macro = (F1_L + F1_M + F1_H) / 3
```

**Implementasi Python**:
```python
from sklearn.metrics import f1_score
f1_macro = f1_score(y_true, y_pred, average='macro')
```

---

## 2. ALGORITMA MACHINE LEARNING

### 2.1 Logistic Regression

**Fungsi Sigmoid**:
```
σ(z) = 1 / (1 + e^(-z))
```

**Linear Combination**:
```
z = β₀ + β₁x₁ + β₂x₂ + ... + βₙxₙ = β^T x
```

**Probability untuk Binary Classification**:
```
P(y=1|x) = σ(β^T x) = 1 / (1 + e^(-β^T x))
P(y=0|x) = 1 - P(y=1|x)
```

**Multinomial Logistic Regression (untuk 3 kelas)**:
```
P(y=k|x) = e^(β_k^T x) / Σⱼ e^(β_j^T x)
```

**Cost Function (Log-Likelihood)**:
```
J(β) = -Σᵢ [yᵢ log(hᵦ(xᵢ)) + (1-yᵢ) log(1-hᵦ(xᵢ))]
```

**Gradient**:
```
∂J/∂β = X^T (h - y)
```

**Implementasi dalam Proyek**:
```python
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

# Pipeline dengan scaling untuk Logistic Regression
pipeline = Pipeline([
    ('scaler', StandardScaler()),
    ('model', LogisticRegression(random_state=42, max_iter=1000))
])
```

### 2.2 Random Forest

**Ensemble Prediction**:
```
ŷ = (1/B) × Σᵦ₌₁ᴮ Tᵦ(x)
```

Dimana:
- B = jumlah trees
- Tᵦ = prediksi dari tree ke-b

**Bootstrap Sampling**:
```
Dᵦ = Bootstrap sample dari D dengan replacement
```

**Feature Randomness**:
```
Pada setiap split, pilih m = √p fitur secara random
```

**Gini Impurity**:
```
Gini(S) = 1 - Σₖ pₖ²
```

**Information Gain**:
```
IG(S,A) = Gini(S) - Σᵥ (|Sᵥ|/|S|) × Gini(Sᵥ)
```

**Feature Importance**:
```
Importance(xⱼ) = Σₜ Σₙ pₙ × ΔGini(n,xⱼ) × I(split_feature(n) = xⱼ)
```

**Implementasi dalam Proyek**:
```python
from sklearn.ensemble import RandomForestClassifier

model = RandomForestClassifier(
    random_state=42, 
    n_estimators=100,
    max_depth=10,
    min_samples_split=2,
    min_samples_leaf=1
)
```

### 2.3 Gradient Boosting

**Additive Model**:
```
F_m(x) = F_{m-1}(x) + γ_m × h_m(x)
```

**Loss Function (untuk klasifikasi)**:
```
L(y, F(x)) = log(1 + e^(-2yF(x)))
```

**Gradient**:
```
r_{im} = -[∂L(yᵢ, F(xᵢ))/∂F(xᵢ)]_{F=F_{m-1}}
```

**Optimal γ**:
```
γ_m = arg min_γ Σᵢ L(yᵢ, F_{m-1}(xᵢ) + γh_m(xᵢ))
```

**Learning Rate**:
```
F_m(x) = F_{m-1}(x) + ν × γ_m × h_m(x)
```

**Implementasi dalam Proyek**:
```python
from sklearn.ensemble import GradientBoostingClassifier

model = GradientBoostingClassifier(
    random_state=42,
    n_estimators=100,
    learning_rate=0.1,
    max_depth=3
)
```

### 2.4 XGBoost

**Objective Function**:
```
Obj = Σᵢ L(yᵢ, ŷᵢ) + Σₖ Ω(fₖ)
```

**Regularization Term**:
```
Ω(f) = γT + (1/2)λ||w||²
```

**Second-order Taylor Expansion**:
```
Obj^(t) ≈ Σᵢ [L(yᵢ, ŷᵢ^(t-1)) + gᵢfₜ(xᵢ) + (1/2)hᵢfₜ²(xᵢ)] + Ω(fₜ)
```

**Gradient dan Hessian**:
```
gᵢ = ∂L(yᵢ, ŷᵢ^(t-1))/∂ŷᵢ^(t-1)
hᵢ = ∂²L(yᵢ, ŷᵢ^(t-1))/∂(ŷᵢ^(t-1))²
```

**Optimal Weight**:
```
w*ⱼ = -Gⱼ/(Hⱼ + λ)
```

**Gain untuk Split**:
```
Gain = (1/2) × [GL²/(HL+λ) + GR²/(HR+λ) - (GL+GR)²/(HL+HR+λ)] - γ
```

**Implementasi dalam Proyek**:
```python
from xgboost import XGBClassifier

model = XGBClassifier(
    random_state=42,
    n_estimators=100,
    eval_metric='mlogloss',
    learning_rate=0.1,
    max_depth=6,
    subsample=0.8,
    colsample_bytree=0.8
)
```

---

## 3. SHAP (SHAPLEY VALUES)

### 3.1 Shapley Value Formula

**Definisi Matematika**:
```
φᵢ(f) = Σ_{S⊆N\{i}} [|S|!(|N|-|S|-1)!/|N|!] × [f(S∪{i}) - f(S)]
```

Dimana:
- φᵢ = SHAP value untuk fitur i
- N = set semua fitur
- S = subset fitur tanpa fitur i
- f = fungsi prediksi model
- |S| = ukuran subset S
- |N| = total jumlah fitur

### 3.2 Efficiency Property

**Additivity**:
```
Σᵢ φᵢ(f) = f(N) - f(∅)
```

**Symmetry**:
```
Jika f(S∪{i}) = f(S∪{j}) untuk semua S, maka φᵢ = φⱼ
```

**Dummy**:
```
Jika f(S∪{i}) = f(S) untuk semua S, maka φᵢ = 0
```

### 3.3 SHAP untuk Tree Models

**TreeSHAP Algorithm**:
```
φᵢ = Σ_{z'⊆x'} [|z'|!(M-|z'|-1)!/M!] × [fₓ(z') - fₓ(z'\i)]
```

### 3.4 Global Feature Importance

**Mean Absolute SHAP**:
```
Importance(xⱼ) = (1/n) × Σᵢ |φⱼ(xᵢ)|
```

**Implementasi dalam Proyek**:
```python
import shap

def calculate_shap_importance(model, X_train, X_test):
    # Create explainer
    explainer = shap.KernelExplainer(model.predict_proba, X_train[:50])
    
    # Calculate SHAP values
    shap_values = explainer.shap_values(X_test[:20])
    
    # Global importance
    if isinstance(shap_values, list):
        # Multi-class case
        mean_shap = np.mean([np.abs(sv) for sv in shap_values], axis=0)
    else:
        mean_shap = np.abs(shap_values)
    
    global_importance = np.mean(mean_shap, axis=0)
    
    return global_importance
```

---

## 4. FEATURE ENGINEERING

### 4.1 Fitur Linguistik

**Title Diversity**:
```
title_diversity = unique_words / total_words
```

**Title Balance Ratio**:
```
title_balance_ratio = pomokit_unique_words / (strava_unique_words + 1)
```

**Total Title Diversity**:
```
total_title_diversity = (pomokit_diversity + strava_diversity) / 2
```

### 4.2 Fitur Gamifikasi

**Activity Points**:
```
activity_points = min(100, (total_distance_km / 6) × 100)
```

**Productivity Points**:
```
productivity_points = min(100, (total_cycles / 5) × 100)
```

**Gamification Balance**:
```
gamification_balance = activity_points / (productivity_points + 1)
```

### 4.3 Fitur Konsistensi

**Consistency Score**:
```
consistency_score = 1 - |activity_days - work_days| / max(activity_days, work_days)
```

**Weekly Efficiency**:
```
weekly_efficiency = total_cycles / work_days
```

### 4.4 Implementasi Feature Engineering

```python
def create_engineered_features(data):
    """Create all engineered features"""
    
    # Linguistic features
    data['title_diversity'] = data['unique_words'] / (data['word_count'] + 1)
    data['title_balance_ratio'] = data['pomokit_unique_words'] / (data['strava_unique_words'] + 1)
    data['total_title_diversity'] = (data['pomokit_diversity'] + data['strava_diversity']) / 2
    
    # Gamification features
    data['activity_points'] = np.minimum(100, (data['total_distance_km'] / 6) * 100)
    data['productivity_points'] = np.minimum(100, (data['total_cycles'] / 5) * 100)
    data['gamification_balance'] = data['activity_points'] / (data['productivity_points'] + 1)
    
    # Consistency features
    data['consistency_score'] = 1 - np.abs(data['activity_days'] - data['work_days']) / np.maximum(data['activity_days'], data['work_days'])
    data['weekly_efficiency'] = data['total_cycles'] / (data['work_days'] + 1)
    
    return data
```

---

## 5. CROSS-VALIDATION

### 5.1 Stratified K-Fold

**Stratification**:
```
Untuk setiap fold k: P(y=c|fold=k) ≈ P(y=c)
```

**Cross-Validation Score**:
```
CV_Score = (1/K) × Σₖ Score(Model_k, Test_k)
```

**Standard Error**:
```
SE = √[(1/K) × Σₖ (Score_k - CV_Score)²]
```

### 5.2 Bootstrap Confidence Interval

**Bootstrap Sample**:
```
B* = {(x₁*, y₁*), ..., (xₙ*, yₙ*)} sampled with replacement
```

**Confidence Interval**:
```
CI = [percentile(α/2), percentile(1-α/2)]
```

**Implementasi**:
```python
def bootstrap_confidence_interval(scores, confidence=0.95):
    """Calculate bootstrap confidence interval"""
    alpha = 1 - confidence
    lower = np.percentile(scores, (alpha/2) * 100)
    upper = np.percentile(scores, (1 - alpha/2) * 100)
    return lower, upper

# Bootstrap analysis
n_bootstrap = 1000
bootstrap_scores = []

for i in range(n_bootstrap):
    # Resample with replacement
    indices = np.random.choice(len(X), size=len(X), replace=True)
    X_boot = X[indices]
    y_boot = y[indices]
    
    # Train and evaluate
    model.fit(X_boot, y_boot)
    score = model.score(X_boot, y_boot)
    bootstrap_scores.append(score)

# Calculate confidence interval
ci_lower, ci_upper = bootstrap_confidence_interval(bootstrap_scores)
```
