# ABSTRACT

**Background:** Student fatigue represents a critical challenge in higher education, significantly impacting academic performance and well-being. Traditional fatigue assessment methods rely on subjective self-reporting or intrusive physiological monitoring, limiting their practical application in educational settings. This study addresses the need for non-intrusive, interpretable approaches to student fatigue prediction using integrated digital behavioral data.

**Methods:** We conducted a cross-sectional study with longitudinal elements analyzing 291 weekly observations from Indonesian university students over 13 weeks. Data were collected from Strava (cardiovascular activity) and Pomokit (academic productivity) platforms, generating 20 engineered features across quantitative metrics, gamification elements, and novel linguistic features extracted from user-generated activity descriptions. Four machine learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, XGBoost) were evaluated using stratified 5-fold cross-validation. Feature selection was performed using SHapley Additive exPlanations (SHAP) analysis to ensure interpretability and theoretical grounding. A comprehensive bias correction framework was implemented to address platform-specific and cultural factors.

**Results:** Linguistic features dominated fatigue prediction, contributing 15.06% of total predictive power compared to traditional quantitative metrics. The top three predictors were pomokit_unique_words (5.54%), total_title_diversity (5.33%), and title_balance_ratio (5.19%), all extracted from brief activity descriptions. XGBoost achieved the highest test accuracy (79.66%) but demonstrated significant overfitting (12.9% train-validation gap). Logistic Regression showed superior stability with 71.19% test accuracy and minimal overfitting (1.71% gap), making it most suitable for practical deployment. SHAP-based feature selection outperformed random selection across three of four algorithms, with Gradient Boosting showing the largest improvement (+1.91%). Title-only analysis achieved 71.19% accuracy using minimal data collection, with 78% precision for high-risk fatigue cases.

**Conclusions:** This study establishes the first empirical evidence that cognitive-linguistic patterns in digital activity descriptions provide superior fatigue prediction compared to traditional quantitative behavioral metrics. The dominance of linguistic features represents a paradigm shift in digital health monitoring, demonstrating that meaningful health insights can be obtained through minimal, non-intrusive data collection. The SHAP-enhanced interpretability framework provides theoretically grounded explanations essential for clinical adoption. The title-only analysis approach addresses major implementation barriers including privacy concerns and infrastructure requirements, enabling widespread deployment in educational settings. These findings have immediate practical value for developing privacy-preserving student wellness monitoring systems while contributing to the broader understanding of computational linguistics applications in digital health.

**Keywords:** fatigue prediction, machine learning, SHAP analysis, linguistic features, digital health monitoring, student wellness, interpretable AI, cardiovascular activity, academic productivity
