# ABSTRACT

Student fatigue represents a critical challenge in higher education, yet traditional assessment methods rely on subjective self-reporting or intrusive physiological monitoring, limiting practical application in educational settings. This study addresses the need for non-intrusive, interpretable approaches through a cross-sectional analysis of 291 weekly observations from Indonesian university students over 13 weeks, integrating cardiovascular activity data from Strava and academic productivity data from Pomokit to generate 20 engineered features across quantitative metrics, gamification elements, and novel linguistic features extracted from user-generated activity descriptions. Four machine learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, XGBoost) were evaluated using stratified 5-fold cross-validation with SHapley Additive exPlanations (SHAP) analysis for interpretable feature selection and comprehensive bias correction. Results demonstrate that linguistic features dominated fatigue prediction, contributing 15.06% of total predictive power, with pomokit_unique_words (5.54%), total_title_diversity (5.33%), and title_balance_ratio (5.19%) emerging as top predictors—all extracted from brief activity descriptions. While XGBoost achieved the highest test accuracy (79.66%), Logistic Regression showed superior stability (71.19% accuracy, 1.71% overfitting gap) suitable for practical deployment, and SHAP-based feature selection outperformed random selection across algorithms (+1.91% improvement with Gradient Boosting). Notably, title-only analysis achieved 71.19% accuracy using minimal data collection with 78% precision for high-risk cases, establishing the first empirical evidence that cognitive-linguistic patterns in digital activity descriptions provide superior fatigue prediction compared to traditional quantitative behavioral metrics. This paradigm shift demonstrates that meaningful health insights can be obtained through minimal, non-intrusive data collection, with the SHAP-enhanced interpretability framework providing theoretically grounded explanations essential for clinical adoption and the title-only approach addressing implementation barriers including privacy concerns, enabling widespread deployment in educational settings for privacy-preserving student wellness monitoring systems.

**Keywords:** fatigue prediction, machine learning, SHAP analysis, linguistic features, digital health monitoring, student wellness, interpretable AI, cardiovascular activity, academic productivity
