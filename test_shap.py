#!/usr/bin/env python3
"""
Test script to verify SHAP ablation study is working correctly
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, 'src')

def test_shap_study():
    """Test SHAP ablation study"""
    try:
        from shap_ablation_study import SHAPAblationStudy
        print("✅ SHAP import successful")
        
        # Check for test datasets
        test_datasets = [
            'dataset/processed/safe_ml_bias_corrected_dataset.csv',
            'dataset/processed/safe_ml_fatigue_dataset.csv',
            'dataset/processed/safe_ml_title_only_dataset.csv'
        ]
        
        available_dataset = None
        for dataset_path in test_datasets:
            if Path(dataset_path).exists():
                available_dataset = dataset_path
                print(f"✅ Found test dataset: {dataset_path}")
                break
        
        if not available_dataset:
            print("❌ No test datasets found")
            return False
            
        # Test SHAP study instantiation
        target_column = 'corrected_fatigue_risk' if 'bias_corrected' in available_dataset else 'fatigue_risk'
        if 'title_only' in available_dataset:
            target_column = 'title_fatigue_risk'
            
        print(f"🎯 Using target column: {target_column}")
        
        shap_study = SHAPAblationStudy(
            data_path=available_dataset,
            target_column=target_column,
            random_state=42
        )
        print("✅ SHAP study instantiation successful")
        
        # Test data loading
        shap_study.load_data()
        print("✅ Data loading successful")
        
        # Test SHAP study execution (just a quick test)
        print("🔍 Running SHAP study...")
        study_results = shap_study.run_complete_shap_study()
        print("✅ SHAP study execution successful")
        
        # Check results structure
        print(f"📊 Results keys: {list(study_results.keys())}")
        
        if 'model_performance' in study_results:
            print("✅ model_performance found in results")
            for algo, perf in study_results['model_performance'].items():
                print(f"   • {algo}: {perf.get('accuracy', 'N/A')}")
        else:
            print("❌ model_performance not found in results")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing SHAP ablation study...")
    success = test_shap_study()
    if success:
        print("🎉 All tests passed!")
    else:
        print("💥 Tests failed!")
