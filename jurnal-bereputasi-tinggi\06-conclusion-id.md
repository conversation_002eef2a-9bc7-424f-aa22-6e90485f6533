# 6. KESIMPULAN

Penelitian ini berhasil menunjukkan bahwa fitur linguistik mendominasi prediksi kelelahan mahas<PERSON>wa, secara fundamental menantang pendekatan konvensional yang bergantung terutama pada metrik perilaku kuantitatif. Melalui analisis yang ditingkatkan SHAP terhadap 291 observasi mingguan dari mahasiswa universitas Indonesia, kami menetapkan bahwa pola kognitif-linguistik yang diekstrak dari deskripsi aktivitas singkat memberikan kekuatan prediktif superior dibandingkan dengan ukuran fisiologis dan berbasis aktivitas tradisional.

Temuan paling signifikan adalah demonstrasi empiris bahwa fitur linguistik berkontribusi 15,06% dari total kekuatan prediktif, dengan pomokit_unique_words (5,54%), total_title_diversity (5,33%), dan title_balance_ratio (5,19%) muncul sebagai prediktor terkuat. Ini merepresentasikan bukti pertama bahwa pola bahasa alami dalam konten yang dihasilkan pengguna berfungsi sebagai indikator yang lebih sensitif terhadap keadaan kelelahan daripada pengukuran fisik langsung, membuka jalan baru untuk pemantauan kesehatan digital yang non-intrusif.

Evaluasi algoritma komprehensif mengungkapkan wawasan kritis tentang trade-off akurasi-stabilitas dalam aplikasi kesehatan. Sementara XGBoost mencapai akurasi test tertinggi (79,66%), Logistic Regression menunjukkan stabilitas superior (akurasi 71,19% dengan overfitting minimal), membuatnya lebih cocok untuk deployment praktis. Framework SHAP terbukti superior dibandingkan seleksi fitur acak di beberapa algoritma, memvalidasi fondasi teoretis metode atribusi fitur berbasis teori permainan.

Validasi analisis berbasis judul saja merepresentasikan terobosan metodologis, mencapai akurasi 71,19% dengan persyaratan pengumpulan data minimal dan presisi 78% untuk kasus risiko tinggi. Pendekatan ini mengatasi hambatan implementasi utama termasuk kekhawatiran privasi dan persyaratan infrastruktur, memungkinkan deployment luas dalam pengaturan pendidikan tanpa protokol pengumpulan data yang ekstensif.

Penelitian ini berkontribusi tiga inovasi kunci untuk pemantauan kesehatan digital. Pertama, framework interpretabilitas yang ditingkatkan SHAP menyediakan penjelasan yang berdasar teoretis dan model-agnostik yang penting untuk adopsi klinis sistem AI. Kedua, metodologi ekstraksi fitur linguistik komprehensif menunjukkan bahwa pola kognitif-linguistik menawarkan kemampuan prediktif superior dibandingkan dengan metrik kuantitatif tradisional. Ketiga, framework koreksi bias memastikan performa yang robust di berbagai populasi pengguna dan konteks penggunaan.

Implikasi praktis sangat signifikan untuk teknologi pendidikan dan inovasi kesehatan digital. Institusi pendidikan dapat mengimplementasikan sistem peringatan dini non-intrusif untuk pemantauan kesejahteraan mahasiswa tanpa memerlukan perangkat keras khusus atau pengumpulan data yang ekstensif. Sifat privacy-preserving dari analisis berbasis judul saja mengatasi kekhawatiran yang berkembang tentang privasi data sambil mempertahankan akurasi prediktif yang substansial, memfasilitasi adopsi yang lebih luas dari teknologi pemantauan kesehatan dalam pengaturan pendidikan dan tempat kerja.

Beberapa keterbatasan mendefinisikan arah penting untuk penelitian masa depan. Desain cross-sectional membatasi inferensi kausal, sampel terbatas pada mahasiswa Indonesia yang melek digital, dan periode observasi 13 minggu mungkin tidak menangkap pola jangka panjang. Penelitian masa depan harus fokus pada studi longitudinal untuk menetapkan kausalitas, validasi eksternal di berbagai konteks budaya, dan integrasi dengan ukuran fisiologis objektif.

Penelitian ini menetapkan analisis linguistik sebagai komponen fundamental dari pemantauan kesehatan digital, menunjukkan bahwa pola kognitif-linguistik memberikan kemampuan prediktif superior dibandingkan dengan metrik kuantitatif tradisional saja. Temuan ini memiliki nilai praktis langsung untuk institusi pendidikan sambil berkontribusi pada pemahaman ilmiah yang lebih luas tentang aplikasi kesehatan digital. Penelitian ini menunjukkan bahwa wawasan kesehatan yang bermakna dapat diperoleh melalui pengumpulan data yang minimal dan non-intrusif, membuka jalan untuk sistem pemantauan kesehatan yang lebih mudah diakses dan menjaga privasi.
