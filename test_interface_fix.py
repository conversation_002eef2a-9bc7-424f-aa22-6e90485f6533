#!/usr/bin/env python3
"""
Test script untuk memverifikasi bahwa interface sudah diperbaiki
"""

import sys
sys.path.append('src')

def test_calculate_derived_features():
    """Test fungsi calculate_derived_features"""
    
    # Import fungsi dari simple_user_interface
    from simple_user_interface import calculate_derived_features
    
    # Test data
    user_input = {
        'strava_activities': 3,
        'total_distance_km': 12.0,
        'total_time_minutes': 180,
        'activity_days': 3,
        'pomokit_cycles': 15,
        'work_days': 5
    }
    
    print("🧪 Testing calculate_derived_features...")
    print("Input:", user_input)
    
    try:
        derived = calculate_derived_features(user_input)
        
        print("\n✅ Derived features calculated successfully!")
        print(f"Total features generated: {len(derived)}")
        
        # Check key features
        expected_features = [
            'total_distance_km', 'avg_distance_km', 'total_time_minutes', 'avg_time_minutes',
            'avg_cycles', 'weekly_efficiency', 'strava_title_count', 'strava_title_length',
            'strava_unique_words', 'pomokit_title_count', 'pomokit_title_length',
            'pomokit_unique_words', 'total_title_diversity', 'title_balance_ratio',
            'activity_points', 'productivity_points', 'achievement_rate', 'gamification_balance'
        ]
        
        missing_features = [f for f in expected_features if f not in derived]
        if missing_features:
            print(f"❌ Missing features: {missing_features}")
            return False
        
        print("\n📊 Sample derived features:")
        for i, (key, value) in enumerate(list(derived.items())[:10]):
            print(f"   {i+1:2d}. {key}: {value}")
        
        if len(derived) > 10:
            print(f"   ... and {len(derived)-10} more features")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """Test model loading"""
    
    try:
        from utils.data_utils import load_model_artifacts
        
        print("\n🤖 Testing model loading...")
        artifacts = load_model_artifacts("results/clean_production_model")
        
        print("✅ Model loaded successfully!")
        print(f"   Features: {len(artifacts['features'])}")
        print(f"   Algorithm: {artifacts['metadata'].get('algorithm_name', 'Unknown')}")
        print(f"   Accuracy: {artifacts['metadata'].get('accuracy', 0)*100:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {str(e)}")
        return False

def test_prediction_pipeline():
    """Test complete prediction pipeline"""
    
    print("\n🔮 Testing complete prediction pipeline...")
    
    try:
        from simple_user_interface import calculate_derived_features
        from utils.data_utils import load_model_artifacts
        import pandas as pd
        
        # Test input
        user_input = {
            'strava_activities': 4,
            'total_distance_km': 15.0,
            'total_time_minutes': 240,
            'activity_days': 4,
            'pomokit_cycles': 20,
            'work_days': 5
        }
        
        # Step 1: Calculate derived features
        derived_features = calculate_derived_features(user_input)
        print("✅ Step 1: Derived features calculated")
        
        # Step 2: Load model
        artifacts = load_model_artifacts("results/clean_production_model")
        model = artifacts['pipeline']
        features = artifacts['features']
        label_encoder = artifacts['label_encoder']
        print("✅ Step 2: Model loaded")
        
        # Step 3: Prepare data for prediction
        df = pd.DataFrame([derived_features])
        X = df[features]
        print("✅ Step 3: Data prepared")
        
        # Step 4: Make prediction
        prediction = model.predict(X)[0]
        probabilities = model.predict_proba(X)[0]
        predicted_class = label_encoder.inverse_transform([prediction])[0]
        print("✅ Step 4: Prediction made")
        
        # Step 5: Format results
        classes = label_encoder.classes_
        prob_dict = {cls: prob for cls, prob in zip(classes, probabilities)}
        
        print(f"\n🎯 Prediction Results:")
        print(f"   Predicted class: {predicted_class}")
        print(f"   Probabilities:")
        for cls, prob in prob_dict.items():
            print(f"     • {cls}: {prob:.4f} ({prob*100:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Prediction pipeline failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    print("🚀 TESTING INTERFACE FIXES")
    print("="*50)
    
    tests = [
        ("Derived Features Calculation", test_calculate_derived_features),
        ("Model Loading", test_model_loading),
        ("Complete Prediction Pipeline", test_prediction_pipeline)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * len(test_name))
        
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\nResult: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Interface should work correctly.")
        print("\nTo run the interface:")
        print("   streamlit run simple_user_interface.py")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
