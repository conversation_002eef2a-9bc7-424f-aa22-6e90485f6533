# EVALUASI METRIK PENELITIAN AKTIVITAS KARDIOVASKULAR

## 1. GAMBARAN UMUM METRIK

Penelitian ini menggunakan pendekatan multi-metrik yang komprehensif untuk evaluasi model pembelajaran mesin dalam prediksi risiko kelelahan mahasiswa. Metrik yang digunakan mencakup evaluasi performa model, analisis interpretabilitas, dan validasi statistik.

## 2. METRIK EVALUASI MODEL MACHINE LEARNING

### 2.1 Accuracy (Akurasi)

**Definisi**: Proporsi prediksi yang benar dari total prediksi yang dibuat.

**Rumus**:
```
Accuracy = (TP + TN) / (TP + TN + FP + FN)
```

**Keterangan**:
- TP = True Positive (prediksi benar untuk kelas positif)
- TN = True Negative (prediksi benar untuk kelas negatif)  
- FP = False Positive (prediksi salah untuk kelas positif)
- FN = False Negative (prediksi salah untuk kelas negatif)

**<PERSON><PERSON>**:
- XGBoost: 79.66% (test), 66.76% (CV)
- Random Forest: 69.49% (test), 64.64% (CV)
- Gradient Boosting: 64.41% (test), 68.10% (CV)
- Logistic Regression: 71.19% (test), 69.35% (CV)

### 2.2 F1-Score (Macro)

**Definisi**: Rata-rata harmonik dari precision dan recall, dihitung untuk setiap kelas kemudian dirata-ratakan.

**Rumus**:
```
F1-Score = 2 × (Precision × Recall) / (Precision + Recall)

F1-Macro = (1/n) × Σ F1-Score_i
```

**Hasil Penelitian**:
- XGBoost: 0.7954
- Random Forest: 0.6952
- Gradient Boosting: 0.6465
- Logistic Regression: 0.7123

### 2.3 Precision (Macro)

**Definisi**: Proporsi prediksi positif yang benar dari semua prediksi positif.

**Rumus**:
```
Precision = TP / (TP + FP)

Precision-Macro = (1/n) × Σ Precision_i
```

**Hasil Penelitian**:
- XGBoost: 0.8012
- Random Forest: 0.7156
- Gradient Boosting: 0.6689
- Logistic Regression: 0.7234

### 2.4 Recall (Macro)

**Definisi**: Proporsi kasus positif yang berhasil diidentifikasi dengan benar.

**Rumus**:
```
Recall = TP / (TP + FN)

Recall-Macro = (1/n) × Σ Recall_i
```

**Hasil Penelitian**:
- XGBoost: 0.7698
- Random Forest: 0.6789
- Gradient Boosting: 0.6401
- Logistic Regression: 0.6876

### 2.5 Overfitting Score

**Definisi**: Mengukur seberapa besar model mengalami overfitting dengan menghitung selisih performa training dan validation.

**Rumus**:
```
Overfitting Score = |Accuracy_train - Accuracy_validation|
```

**Kategori**:
- RENDAH: < 10%
- SEDANG: 10-20%
- TINGGI: > 20%

**Hasil Penelitian**:
- Logistic Regression: RENDAH (9.2)
- Gradient Boosting: SEDANG (18.7)
- Random Forest: TINGGI (27.8)
- XGBoost: TINGGI (21.3)

## 3. METRIK SHAP (SHAPLEY ADDITIVE EXPLANATIONS)

### 3.1 SHAP Values

**Definisi**: Nilai yang menunjukkan kontribusi setiap fitur terhadap prediksi individual berdasarkan teori permainan.

**Rumus Shapley Value**:
```
φ_i = Σ_{S⊆N\{i}} [|S|!(|N|-|S|-1)!/|N|!] × [f(S∪{i}) - f(S)]
```

**Keterangan**:
- φ_i = Shapley value untuk fitur i
- N = set semua fitur
- S = subset fitur tanpa fitur i
- f(S) = nilai prediksi menggunakan subset S

### 3.2 Global Feature Importance

**Definisi**: Kepentingan fitur global dihitung sebagai rata-rata nilai SHAP absolut.

**Rumus**:
```
Global_Importance_i = (1/n) × Σ |SHAP_i,j|
```

**Top 10 Fitur Berdasarkan SHAP**:
1. pomokit_unique_words: 5.54%
2. total_title_diversity: 5.33%
3. title_balance_ratio: 5.19%
4. avg_time_minutes: 4.73%
5. total_time_minutes: 4.02%
6. work_days: 3.57%
7. consistency_score: 3.10%
8. gamification_balance: 2.85%
9. avg_distance_km: 2.80%
10. activity_points: 2.73%

### 3.3 Feature Category Analysis

**Distribusi Kepentingan per Kategori**:
- Fitur Linguistik: 15.06% (3 fitur)
- Aktivitas Fisik: 12.85% (4 fitur)
- Metrik Produktivitas: 8.92% (3 fitur)
- Elemen Gamifikasi: 7.43% (3 fitur)
- Konsistensi Perilaku: 5.67% (2 fitur)

## 4. METRIK CROSS-VALIDATION

### 4.1 Stratified K-Fold Cross-Validation

**Definisi**: Teknik validasi yang mempertahankan proporsi kelas di setiap fold.

**Implementasi**: k=5 dengan stratifikasi berdasarkan target variable

**Rumus Stratified Sampling**:
```
Proporsi_kelas_i_fold_j = Proporsi_kelas_i_dataset
```

### 4.2 Cross-Validation Metrics

**CV Accuracy Mean & Standard Deviation**:
```
CV_Mean = (1/k) × Σ Accuracy_i
CV_Std = √[(1/k) × Σ(Accuracy_i - CV_Mean)²]
```

**Hasil CV Analysis**:
- Logistic Regression: 69.35% ± 2.1%
- Random Forest: 64.64% ± 3.8%
- Gradient Boosting: 68.10% ± 2.9%
- XGBoost: 66.76% ± 4.2%

## 5. ALGORITMA MACHINE LEARNING YANG DIGUNAKAN

### 5.1 Logistic Regression

**Definisi**: Model linear untuk klasifikasi yang menggunakan fungsi logistik.

**Rumus**:
```
P(y=1|x) = 1 / (1 + e^(-(β₀ + β₁x₁ + β₂x₂ + ... + βₙxₙ)))

Log-odds = ln(P/(1-P)) = β₀ + β₁x₁ + β₂x₂ + ... + βₙxₙ
```

**Parameter Penelitian**:
- max_iter=1000
- random_state=42
- Menggunakan StandardScaler untuk preprocessing

**Kelebihan**:
- Interpretabilitas tinggi
- Tidak mudah overfitting
- Efisien secara komputasional

**Hasil**: 71.19% accuracy, overfitting rendah

### 5.2 Random Forest

**Definisi**: Ensemble method yang menggabungkan multiple decision trees.

**Rumus Prediksi**:
```
ŷ = (1/B) × Σ T_b(x)
```

**Keterangan**:
- B = jumlah trees
- T_b = prediksi dari tree ke-b
- Menggunakan bootstrap sampling dan random feature selection

**Parameter Penelitian**:
- n_estimators=100
- max_depth=10
- random_state=42

**Feature Importance**:
```
Importance_i = Σ_{t∈trees} p(t) × Δimpurity(t,i)
```

**Hasil**: 69.49% accuracy, overfitting tinggi

### 5.3 Gradient Boosting

**Definisi**: Sequential ensemble yang memperbaiki kesalahan model sebelumnya.

**Rumus Update**:
```
F_m(x) = F_{m-1}(x) + γ_m × h_m(x)

γ_m = argmin_γ Σ L(y_i, F_{m-1}(x_i) + γh_m(x_i))
```

**Keterangan**:
- F_m = model pada iterasi m
- h_m = weak learner pada iterasi m
- γ_m = learning rate
- L = loss function

**Parameter Penelitian**:
- n_estimators=100
- learning_rate=0.1
- random_state=42

**Hasil**: 64.41% accuracy, overfitting sedang

### 5.4 XGBoost (Extreme Gradient Boosting)

**Definisi**: Optimized gradient boosting dengan regularization.

**Objective Function**:
```
Obj = Σ L(y_i, ŷ_i) + Σ Ω(f_k)

Ω(f) = γT + (1/2)λ||w||²
```

**Keterangan**:
- L = loss function
- Ω = regularization term
- T = jumlah leaves
- λ = L2 regularization
- γ = minimum loss reduction

**Parameter Penelitian**:
- n_estimators=100
- eval_metric='mlogloss'
- random_state=42

**Hasil**: 79.66% accuracy (tertinggi), overfitting tinggi

## 6. METRIK VALIDASI STATISTIK

### 6.1 Bootstrap Analysis

**Implementasi**: n=1000 bootstrap samples

**Rumus Confidence Interval**:
```
CI = [θ̂ - z_{α/2} × SE, θ̂ + z_{α/2} × SE]
```

**Hasil**: p < 0.001 untuk superioritas fitur linguistik

### 6.2 Spearman Correlation

**Untuk konsistensi ranking SHAP**:
```
ρ = 1 - (6Σd_i²)/(n(n²-1))
```

**Hasil**: 0.89 korelasi antar fold

### 6.3 Statistical Significance Testing

**Paired t-test untuk perbandingan model**:
```
t = (x̄₁ - x̄₂) / (s_p × √(2/n))
```

**Multiple Comparison Correction**: Bonferroni correction

## 7. METRIK KUALITAS DATA

### 7.1 Missing Values Rate

**Rumus**:
```
Missing_Rate = (Jumlah_Missing / Total_Observasi) × 100%
```

**Hasil**: < 2% untuk semua fitur

### 7.2 Temporal Alignment Accuracy

**Rumus**:
```
Alignment_Accuracy = (Matched_Records / Total_Records) × 100%
```

**Hasil**: 98.3% antara platform Strava dan Pomokit

## 8. DISTRIBUSI TARGET VARIABLE

### 8.1 Class Distribution

**Klasifikasi Risiko Kelelahan**:
- Low Risk: 67.4% (196 observasi)
- Medium Risk: 24.7% (72 observasi)
- High Risk: 7.9% (23 observasi)

### 8.2 Class Imbalance Ratio

**Rumus**:
```
Imbalance_Ratio = Max_Class_Count / Min_Class_Count
```

**Hasil**: 8.5:1 (high imbalance)

## 9. KESIMPULAN EVALUASI METRIK

### 9.1 Kelebihan Metodologi

1. **Komprehensif**: Multiple metrics untuk evaluasi menyeluruh
2. **Interpretable**: SHAP values memberikan explainability
3. **Robust**: Cross-validation dan bootstrap validation
4. **Statistik**: Validasi signifikansi yang ketat

### 9.2 Temuan Utama

1. **Fitur Linguistik Dominan**: 15.06% total kepentingan
2. **Trade-off Accuracy-Stability**: XGBoost akurat tapi overfitting
3. **Logistic Regression Stabil**: Gap train-validation terkecil
4. **SHAP Konsisten**: Ranking fitur stabil across algorithms

### 9.3 Limitasi

1. **Dataset Kecil**: 291 observasi
2. **Class Imbalance**: Rasio 8.5:1
3. **Temporal Aspect**: Kurang metrik time-series
4. **Domain-Specific**: Kurang metrik khusus fatigue

### 9.4 Rekomendasi

1. Perbesar dataset untuk generalisasi yang lebih baik
2. Implementasikan stratified sampling untuk class balance
3. Tambahkan metrik domain-specific untuk fatigue
4. Gunakan time-series metrics untuk data longitudinal
5. Implementasikan external validation dengan dataset independen
