# IMPLEMENTASI KODE PROYEK PENELITIAN

## Daftar Isi
1. [Struktur Utama Pipeline](#1-struktur-utama-pipeline)
2. [Evaluasi Model](#2-evaluasi-model)
3. [SHAP Implementation](#3-shap-implementation)
4. [Feature Engineering](#4-feature-engineering)
5. [Cross-Validation](#5-cross-validation)

---

## 1. STRUKTUR UTAMA PIPELINE

### 1.1 Main Pipeline Class (main5.py)

```python
class BiasCorrectedSHAPAnalysisPipeline:
    """
    Complete analysis pipeline for bias-corrected fatigue prediction with SHAP feature selection
    """

    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()

        if include_ml:
            self.bias_corrected_classifier = BiasCorrectedTitleClassifier()
            self.feature_filter = FeatureFilter2()

        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def run_bias_corrected_prediction(self, processed_data=None):
        """Run bias corrected fatigue prediction with SHAP feature filtering and model accuracy"""
        logger.info("="*60)
        logger.info("PHASE 2: BIAS CORRECTED FATIGUE PREDICTION WITH SHAP FEATURE SELECTION & ML MODELS")
        logger.info("="*60)

        # Step 1: Bias Corrected Classification
        logger.info("Step 1: Running bias corrected fatigue classification...")
        classified_data = self.bias_corrected_classifier.process_bias_corrected_classification(
            'dataset/processed/weekly_merged_dataset_with_gamification.csv'
        )

        # Step 2: Feature Filtering for ML Safety
        logger.info("Step 2: Applying bias correction feature filtering to prevent data leakage...")
        safe_output = "dataset/processed/safe_ml_bias_corrected_dataset.csv"

        safe_path = self.feature_filter.create_safe_dataset_for_bias_corrected_classifier(
            classified_output,
            safe_output,
            target_column='corrected_fatigue_risk'
        )

        # Step 3: SHAP ML Pipeline with Safe Dataset
        logger.info("Step 4: Running SHAP ML pipeline with bias correction safe features...")
        
        shap_study = SHAPAblationStudy(
            data_path=selected_dataset['path'],
            target_column=selected_dataset['target'],
            random_state=42
        )
        shap_study.load_data()
        study_results = shap_study.run_complete_shap_study()

        return study_results
```

### 1.2 Algorithm Configuration

```python
# Algorithm configurations dari SHAPAblationStudy
self.algorithms = {
    'logistic_regression': {
        'name': 'Logistic Regression',
        'model': LogisticRegression(random_state=random_state, max_iter=1000),
        'explainer_type': 'linear'
    },
    'random_forest': {
        'name': 'Random Forest',
        'model': RandomForestClassifier(random_state=random_state, n_estimators=100),
        'explainer_type': 'tree'
    },
    'gradient_boosting': {
        'name': 'Gradient Boosting',
        'model': GradientBoostingClassifier(random_state=random_state, n_estimators=100),
        'explainer_type': 'tree'
    }
}

# Add XGBoost if available
if XGBOOST_AVAILABLE:
    self.algorithms['xgboost'] = {
        'name': 'XGBoost',
        'model': XGBClassifier(random_state=random_state, n_estimators=100, eval_metric='mlogloss'),
        'explainer_type': 'tree'
    }
```

---

## 2. EVALUASI MODEL

### 2.1 Metrics Calculation (src/utils/evaluation_utils.py)

```python
def _calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """Calculate standard classification metrics."""
    return {
        'accuracy': accuracy_score(y_true, y_pred),
        'f1_macro': f1_score(y_true, y_pred, average='macro'),
        'precision_macro': precision_score(y_true, y_pred, average='macro'),
        'recall_macro': recall_score(y_true, y_pred, average='macro')
    }

def evaluate_model_cv(model: Any,
                     X: np.ndarray,
                     y: np.ndarray,
                     cv_folds: int = 5,
                     random_state: int = 42,
                     scoring: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Evaluate model using cross-validation.
    """
    if scoring is None:
        scoring = ['accuracy', 'f1_macro', 'precision_macro', 'recall_macro']
    
    # Setup cross-validation
    cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
    
    # Perform cross-validation
    cv_results = cross_validate(
        model, X, y,
        cv=cv,
        scoring=scoring,
        return_train_score=True,
        n_jobs=-1
    )
    
    # Compile results
    results = {
        'cv_folds': cv_folds,
        'scoring_metrics': scoring,
        'test_scores': {},
        'train_scores': {},
        'mean_scores': {},
        'std_scores': {}
    }
    
    # Extract scores for each metric
    for metric in scoring:
        test_key = f'test_{metric}'
        train_key = f'train_{metric}'
        
        if test_key in cv_results:
            results['test_scores'][metric] = cv_results[test_key]
            results['mean_scores'][f'{metric}_test_mean'] = cv_results[test_key].mean()
            results['std_scores'][f'{metric}_test_std'] = cv_results[test_key].std()
        
        if train_key in cv_results:
            results['train_scores'][metric] = cv_results[train_key]
            results['mean_scores'][f'{metric}_train_mean'] = cv_results[train_key].mean()
            results['std_scores'][f'{metric}_train_std'] = cv_results[train_key].std()
    
    return results
```

### 2.2 Overfitting Detection

```python
def evaluate_model_holdout(model: Any,
                          X_train: np.ndarray,
                          X_test: np.ndarray,
                          y_train: np.ndarray,
                          y_test: np.ndarray) -> Dict[str, Any]:
    """
    Evaluate model on holdout test set.
    """
    # Train model
    model.fit(X_train, y_train)
    
    # Make predictions
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)
    
    # Calculate metrics
    results = {
        'train_metrics': _calculate_metrics(y_train, y_train_pred),
        'test_metrics': _calculate_metrics(y_test, y_test_pred),
        'overfitting': {}
    }
    
    # Calculate overfitting scores
    for metric in results['train_metrics']:
        if metric in results['test_metrics']:
            overfitting = results['train_metrics'][metric] - results['test_metrics'][metric]
            results['overfitting'][metric] = overfitting
    
    return results
```

---

## 3. SHAP IMPLEMENTATION

### 3.1 SHAP Explainer Creation (src/shap_ablation_study.py)

```python
def create_shap_explainers(self):
    """Create SHAP explainers for each model"""
    logger.info("Creating SHAP explainers...")

    for algo_key, model_info in self.models.items():
        config = model_info['config']
        pipeline = model_info['pipeline']

        logger.info(f"Creating SHAP explainer for {config['name']}...")

        try:
            # Use KernelExplainer for all models (more robust but slower)
            # Sample background data for efficiency
            background_data = self.X_train.iloc[:50]

            def model_predict(X):
                """Wrapper function for model prediction"""
                if hasattr(pipeline, 'predict_proba'):
                    return pipeline.predict_proba(X)
                else:
                    return pipeline.predict(X)

            explainer = shap.KernelExplainer(model_predict, background_data)
            self.explainers[algo_key] = explainer
            logger.info(f"✅ SHAP KernelExplainer created for {config['name']}")

        except Exception as e:
            logger.error(f"❌ Failed to create SHAP explainer for {config['name']}: {str(e)}")
            continue
```

### 3.2 SHAP Values Calculation

```python
def calculate_shap_values(self):
    """Calculate SHAP values for each model"""
    logger.info("Calculating SHAP values...")

    # Use a small subset for SHAP calculation (KernelExplainer is slow)
    X_shap = self.X_test.iloc[:20] if len(self.X_test) > 20 else self.X_test

    for algo_key, explainer in self.explainers.items():
        config = self.models[algo_key]['config']
        logger.info(f"Calculating SHAP values for {config['name']}...")

        try:
            # KernelExplainer works with original data
            shap_values = explainer.shap_values(X_shap)

            # Validate SHAP values
            if shap_values is None:
                logger.warning(f"SHAP values are None for {config['name']}")
                continue

            # Handle different SHAP value formats
            if isinstance(shap_values, list):
                if len(shap_values) == 0:
                    logger.warning(f"Empty SHAP values list for {config['name']}")
                    continue
                # Check if any of the arrays in the list are empty
                if any(sv.size == 0 for sv in shap_values):
                    logger.warning(f"Empty SHAP value arrays for {config['name']}")
                    continue
            else:
                if shap_values.size == 0:
                    logger.warning(f"Empty SHAP values array for {config['name']}")
                    continue

            self.shap_values[algo_key] = {
                'values': shap_values,
                'data': X_shap,
                'feature_names': self.feature_names
            }

            logger.info(f"✅ SHAP values calculated for {config['name']}")

        except Exception as e:
            logger.error(f"❌ Failed to calculate SHAP values for {config['name']}: {str(e)}")
            continue
```

### 3.3 Global Feature Importance Analysis

```python
def analyze_global_importance(self) -> Dict:
    """Analyze global feature importance using SHAP values"""
    logger.info("Analyzing global feature importance...")
    
    global_importance = {}
    
    for algo_key, shap_data in self.shap_values.items():
        config = self.models[algo_key]['config']
        shap_vals = shap_data['values']
        
        # Handle multi-class SHAP values
        if isinstance(shap_vals, list):
            # For multi-class, take mean absolute SHAP values across classes
            if len(shap_vals) > 0:
                mean_shap = np.mean([np.abs(sv) for sv in shap_vals], axis=0)
            else:
                logger.warning(f"Empty SHAP values list for {config['name']}")
                continue
        else:
            mean_shap = np.abs(shap_vals)

        # Ensure mean_shap is not empty
        if mean_shap.size == 0:
            logger.warning(f"Empty SHAP values for {config['name']}")
            continue

        # Calculate global importance (mean absolute SHAP value)
        global_imp = np.mean(mean_shap, axis=0)
        
        # Create feature importance ranking
        feature_importance = []
        for i, feature in enumerate(self.feature_names):
            # Ensure shap_importance is a scalar value
            importance_val = global_imp[i]

            # Handle different numpy array shapes and types
            if isinstance(importance_val, np.ndarray):
                if importance_val.size == 1:
                    importance_val = float(importance_val.item())  # Single element array
                elif importance_val.size > 1:
                    importance_val = float(np.mean(importance_val))  # Multi-element array, take mean
                else:
                    importance_val = 0.0  # Empty array
            elif hasattr(importance_val, 'item'):
                try:
                    importance_val = float(importance_val.item())  # Numpy scalar
                except ValueError:
                    importance_val = float(np.mean(importance_val))  # If item() fails, take mean
            else:
                importance_val = float(importance_val)  # Regular Python number

            feature_importance.append({
                'feature': feature,
                'shap_importance': importance_val,
                'rank': 0  # Will be set after sorting
            })

        # Sort by importance (now safe since all values are scalars)
        feature_importance.sort(key=lambda x: x['shap_importance'], reverse=True)
        
        # Update ranks
        for i, item in enumerate(feature_importance):
            item['rank'] = i + 1
        
        global_importance[algo_key] = {
            'algorithm': config['name'],
            'feature_importance': feature_importance,
            'total_features': len(self.feature_names)
        }
        
        logger.info(f"Global importance calculated for {config['name']}")
    
    return global_importance
```

---

## 4. FEATURE ENGINEERING

### 4.1 Data Processing Pipeline (src/data_processor.py)

```python
def create_linguistic_features(self, data):
    """Create linguistic features from activity descriptions"""
    
    # Basic text features
    data['title_length'] = data['title'].str.len().fillna(0)
    data['word_count'] = data['title'].str.split().str.len().fillna(0)
    data['unique_words'] = data['title'].apply(
        lambda x: len(set(str(x).split())) if pd.notna(x) else 0
    )
    
    # Advanced linguistic features
    data['title_diversity'] = data['unique_words'] / (data['word_count'] + 1)
    data['title_balance_ratio'] = data['pomokit_unique_words'] / (data['strava_unique_words'] + 1)
    data['total_title_diversity'] = (
        data.get('pomokit_title_diversity', 0) + 
        data.get('strava_title_diversity', 0)
    ) / 2
    
    return data

def create_gamification_features(self, data):
    """Create gamification features"""
    
    # Activity points based on WHO guidelines (150 min/week = 25km/week)
    data['activity_points'] = np.minimum(100, (data['total_distance_km'] / 6) * 100)
    
    # Productivity points based on Pomodoro technique (25 cycles/week target)
    data['productivity_points'] = np.minimum(100, (data['total_cycles'] / 5) * 100)
    
    # Balance between activity and productivity
    data['gamification_balance'] = data['activity_points'] / (data['productivity_points'] + 1)
    
    # Achievement rate
    data['achievement_rate'] = (data['activity_points'] + data['productivity_points']) / 200
    
    return data

def create_consistency_features(self, data):
    """Create behavioral consistency features"""
    
    # Overall consistency between physical and academic activities
    data['consistency_score'] = 1 - np.abs(data['activity_days'] - data['work_days']) / np.maximum(
        data['activity_days'], data['work_days']
    ).replace(0, 1)
    
    # Weekly efficiency
    data['weekly_efficiency'] = data['total_cycles'] / (data['work_days'] + 1)
    
    # Physical consistency (regularity of exercise)
    data['physical_consistency'] = data['activity_days'] / 7
    
    # Work consistency (regularity of academic work)
    data['work_consistency'] = data['work_days'] / 7
    
    return data
```

### 4.2 Target Variable Construction

```python
def create_fatigue_risk_labels(self, data):
    """Create fatigue risk labels based on domain knowledge"""
    
    def classify_fatigue_risk(row):
        # Normalize metrics to 0-1 scale
        activity_norm = min(1.0, row['total_distance_km'] / 25)  # 25km/week target
        productivity_norm = min(1.0, row['total_cycles'] / 25)   # 25 cycles/week target
        consistency_norm = row['consistency_score']
        
        # Calculate composite risk score
        balance_score = 1 - abs(activity_norm - productivity_norm)
        overall_score = (activity_norm + productivity_norm + consistency_norm + balance_score) / 4
        
        # Classify based on thresholds
        if overall_score >= 0.7:
            return 'low_risk'
        elif overall_score >= 0.4:
            return 'medium_risk'
        else:
            return 'high_risk'
    
    data['fatigue_risk'] = data.apply(classify_fatigue_risk, axis=1)
    return data
```

---

## 5. CROSS-VALIDATION

### 5.1 K-Fold Analysis (kfold_overfitting.py)

```python
def analyze_kfold_stability(self, k_values=None):
    """Analyze model stability across different k values"""
    
    if k_values is None:
        k_values = range(2, 21)  # k from 2 to 20
    
    results = {}
    
    for model_name, model in self.models.items():
        logger.info(f"Analyzing K-fold stability for {model_name}")
        
        model_results = {
            'k_values': [],
            'train_accuracies': [],
            'val_accuracies': [],
            'train_stds': [],
            'val_stds': []
        }
        
        for k in k_values:
            try:
                # Stratified K-Fold
                cv = StratifiedKFold(n_splits=k, shuffle=True, random_state=42)

                # Use encoded labels for XGBoost, original for others
                y_to_use = self.y_encoded if 'XGBoost' in model_name else self.y

                # Cross-validation with train and validation scores
                cv_results = cross_validate(
                    model, self.X, y_to_use,
                    cv=cv,
                    scoring='accuracy',
                    return_train_score=True,
                    n_jobs=-1
                )
                
                # Store results
                model_results['k_values'].append(k)
                model_results['train_accuracies'].append(cv_results['train_score'].mean())
                model_results['val_accuracies'].append(cv_results['test_score'].mean())
                model_results['train_stds'].append(cv_results['train_score'].std())
                model_results['val_stds'].append(cv_results['test_score'].std())
                
            except Exception as e:
                logger.warning(f"Failed K-fold analysis for {model_name} with k={k}: {str(e)}")
                continue
        
        results[model_name] = model_results
    
    return results
```

### 5.2 Model Training dengan Cross-Validation

```python
def train_models(self):
    """Train models for SHAP analysis with cross-validation"""
    logger.info("Training models for SHAP analysis with cross-validation...")

    # Split data for training
    X_train, X_test, y_train, y_test = train_test_split(
        self.X, self.y_encoded, test_size=0.2,
        random_state=self.random_state, stratify=self.y_encoded
    )

    self.X_train, self.X_test = X_train, X_test
    self.y_train, self.y_test = y_train, y_test

    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)

    # Train each model
    for algo_key, config in self.algorithms.items():
        logger.info(f"Training {config['name']} with cross-validation...")

        model = config['model']

        # For linear models, use pipeline with scaling
        if algo_key == 'logistic_regression':
            pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('model', model)
            ])
        else:
            pipeline = model

        # Perform cross-validation
        cv_scores = cross_validate(
            pipeline, X_train, y_train, cv=cv,
            scoring=['accuracy', 'f1_weighted'],
            return_train_score=True
        )

        # Train final model on full training set
        pipeline.fit(X_train, y_train)

        # Evaluate on test set
        y_pred = pipeline.predict(X_test)
        test_accuracy = accuracy_score(y_test, y_pred)
        test_f1 = f1_score(y_test, y_pred, average='weighted')

        logger.info(f"{config['name']} - CV Accuracy: {cv_scores['test_accuracy'].mean():.4f} (±{cv_scores['test_accuracy'].std():.4f})")
        logger.info(f"{config['name']} - Test Accuracy: {test_accuracy:.4f}, Test F1: {test_f1:.4f}")

        self.models[algo_key] = {
            'pipeline': pipeline,
            'model': model,
            'accuracy': test_accuracy,
            'f1_score': test_f1,
            'cv_scores': cv_scores,
            'cv_accuracy_mean': cv_scores['test_accuracy'].mean(),
            'cv_accuracy_std': cv_scores['test_accuracy'].std(),
            'cv_f1_mean': cv_scores['test_f1_weighted'].mean(),
            'cv_f1_std': cv_scores['test_f1_weighted'].std(),
            'config': config
        }
```

---

## 6. HASIL IMPLEMENTASI

### 6.1 Performa yang Dicapai

```python
# Hasil dari implementasi kode di atas:
results = {
    'XGBoost': {
        'test_accuracy': 0.7966,
        'cv_accuracy_mean': 0.6676,
        'f1_score': 0.7954,
        'overfitting_score': 21.3
    },
    'Random Forest': {
        'test_accuracy': 0.6949,
        'cv_accuracy_mean': 0.6464,
        'f1_score': 0.6952,
        'overfitting_score': 27.8
    },
    'Gradient Boosting': {
        'test_accuracy': 0.6441,
        'cv_accuracy_mean': 0.6810,
        'f1_score': 0.6465,
        'overfitting_score': 18.7
    },
    'Logistic Regression': {
        'test_accuracy': 0.7119,
        'cv_accuracy_mean': 0.6935,
        'f1_score': 0.7123,
        'overfitting_score': 9.2
    }
}
```

### 6.2 Top SHAP Features

```python
# Top 10 fitur berdasarkan SHAP importance
top_shap_features = [
    {'feature': 'pomokit_unique_words', 'importance': 0.0554},
    {'feature': 'total_title_diversity', 'importance': 0.0533},
    {'feature': 'title_balance_ratio', 'importance': 0.0519},
    {'feature': 'avg_time_minutes', 'importance': 0.0473},
    {'feature': 'total_time_minutes', 'importance': 0.0402},
    {'feature': 'work_days', 'importance': 0.0357},
    {'feature': 'consistency_score', 'importance': 0.0310},
    {'feature': 'gamification_balance', 'importance': 0.0285},
    {'feature': 'avg_distance_km', 'importance': 0.0280},
    {'feature': 'activity_points', 'importance': 0.0273}
]
```

Implementasi kode ini menunjukkan pendekatan yang komprehensif dan metodologis untuk prediksi kelelahan mahasiswa menggunakan data multi-modal dengan analisis SHAP yang mendalam.
