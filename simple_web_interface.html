<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Fatigue Risk Predictor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            display: flex;
            min-height: 600px;
        }
        
        .sidebar {
            width: 300px;
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }
        
        .model-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .model-info h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .main-content {
            flex: 1;
            padding: 30px;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #6c757d;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .predict-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
            margin-top: 30px;
        }
        
        .predict-btn:hover {
            transform: translateY(-2px);
        }
        
        .results {
            margin-top: 30px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .risk-card {
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .risk-low { background: linear-gradient(135deg, #28a745, #20c997); }
        .risk-medium { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .risk-high { background: linear-gradient(135deg, #dc3545, #e83e8c); }
        
        .prob-bars {
            margin-top: 20px;
        }
        
        .prob-bar {
            margin-bottom: 15px;
        }
        
        .prob-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .prob-progress {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .prob-fill {
            height: 100%;
            transition: width 0.5s ease;
        }
        
        .recommendations {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
        }
        
        .recommendations h3 {
            margin-bottom: 15px;
        }
        
        .recommendations ul {
            list-style: none;
        }
        
        .recommendations li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .recommendations li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        
        @media (max-width: 768px) {
            .content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Fatigue Risk Predictor</h1>
            <p>Sistem Prediksi Risiko Kelelahan dengan Machine Learning</p>
        </div>
        
        <div class="content">
            <div class="sidebar">
                <div class="model-info">
                    <h3>ℹ️ Model Information</h3>
                    <div class="info-item">
                        <span>Algorithm:</span>
                        <strong>Random Forest</strong>
                    </div>
                    <div class="info-item">
                        <span>Accuracy:</span>
                        <strong>93.22%</strong>
                    </div>
                    <div class="info-item">
                        <span>Features:</span>
                        <strong>18</strong>
                    </div>
                    <div class="info-item">
                        <span>Classes:</span>
                        <strong>3</strong>
                    </div>
                </div>
                
                <div class="model-info">
                    <h3>📖 Panduan</h3>
                    <ol style="padding-left: 20px;">
                        <li>Isi semua field input data</li>
                        <li>Klik tombol 'Prediksi Risiko'</li>
                        <li>Lihat hasil dan rekomendasi</li>
                        <li>Gunakan untuk pengambilan keputusan</li>
                    </ol>
                </div>
            </div>
            
            <div class="main-content">
                <form id="predictionForm">
                    <div class="tabs">
                        <button type="button" class="tab active" onclick="showTab('productivity')">🎯 Produktivitas</button>
                        <button type="button" class="tab" onclick="showTab('time')">⏰ Waktu</button>
                        <button type="button" class="tab" onclick="showTab('strava')">🏃 Strava</button>
                        <button type="button" class="tab" onclick="showTab('pomokit')">📚 Pomokit</button>
                        <button type="button" class="tab" onclick="showTab('other')">📊 Lainnya</button>
                    </div>
                    
                    <!-- Tab Produktivitas -->
                    <div id="productivity" class="tab-content active">
                        <h3>🎯 Data Produktivitas</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productivity_points">Productivity Points (0-200)</label>
                                <input type="number" id="productivity_points" name="productivity_points" 
                                       min="0" max="200" step="0.1" value="85.5" required>
                            </div>
                            <div class="form-group">
                                <label for="activity_points">Activity Points (0-500)</label>
                                <input type="number" id="activity_points" name="activity_points" 
                                       min="0" max="500" step="0.1" value="120.0" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tab Waktu -->
                    <div id="time" class="tab-content">
                        <h3>⏰ Data Waktu & Efisiensi</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="total_time_minutes">Total Time (minutes)</label>
                                <input type="number" id="total_time_minutes" name="total_time_minutes" 
                                       min="0" max="10000" value="1800" required>
                            </div>
                            <div class="form-group">
                                <label for="avg_time_minutes">Average Time (minutes)</label>
                                <input type="number" id="avg_time_minutes" name="avg_time_minutes" 
                                       min="0" max="10000" value="45" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="weekly_efficiency">Weekly Efficiency (0.0-1.0)</label>
                            <input type="range" id="weekly_efficiency" name="weekly_efficiency" 
                                   min="0" max="1" step="0.01" value="0.82" required>
                            <span id="weekly_efficiency_value">0.82</span>
                        </div>
                    </div>
                    
                    <!-- Tab Strava -->
                    <div id="strava" class="tab-content">
                        <h3>🏃 Data Strava</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="strava_title_count">Strava Title Count</label>
                                <input type="number" id="strava_title_count" name="strava_title_count" 
                                       min="0" max="100" value="8" required>
                            </div>
                            <div class="form-group">
                                <label for="strava_unique_words">Strava Unique Words</label>
                                <input type="number" id="strava_unique_words" name="strava_unique_words" 
                                       min="0" max="50" value="12" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="strava_title_length">Strava Title Length</label>
                            <input type="number" id="strava_title_length" name="strava_title_length" 
                                   min="0" max="100" step="0.1" value="18.5" required>
                        </div>
                    </div>
                    
                    <!-- Tab Pomokit -->
                    <div id="pomokit" class="tab-content">
                        <h3>📚 Data Pomokit</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pomokit_title_count">Pomokit Title Count</label>
                                <input type="number" id="pomokit_title_count" name="pomokit_title_count" 
                                       min="0" max="100" value="15" required>
                            </div>
                            <div class="form-group">
                                <label for="pomokit_unique_words">Pomokit Unique Words</label>
                                <input type="number" id="pomokit_unique_words" name="pomokit_unique_words" 
                                       min="0" max="50" value="10" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="pomokit_title_length">Pomokit Title Length</label>
                            <input type="number" id="pomokit_title_length" name="pomokit_title_length" 
                                   min="0" max="100" step="0.1" value="25.3" required>
                        </div>
                    </div>
                    
                    <!-- Tab Lainnya -->
                    <div id="other" class="tab-content">
                        <h3>📊 Data Lainnya</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="achievement_rate">Achievement Rate (0.0-1.0)</label>
                                <input type="range" id="achievement_rate" name="achievement_rate" 
                                       min="0" max="1" step="0.01" value="0.85" required>
                                <span id="achievement_rate_value">0.85</span>
                            </div>
                            <div class="form-group">
                                <label for="avg_cycles">Average Cycles</label>
                                <input type="number" id="avg_cycles" name="avg_cycles" 
                                       min="0" max="20" step="0.1" value="3.2" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="avg_distance_km">Average Distance (km)</label>
                                <input type="number" id="avg_distance_km" name="avg_distance_km" 
                                       min="0" max="100" step="0.1" value="5.4" required>
                            </div>
                            <div class="form-group">
                                <label for="total_distance_km">Total Distance (km)</label>
                                <input type="number" id="total_distance_km" name="total_distance_km" 
                                       min="0" max="1000" step="0.1" value="45.2" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="total_title_diversity">Title Diversity</label>
                                <input type="number" id="total_title_diversity" name="total_title_diversity" 
                                       min="0" max="50" step="0.1" value="8.5" required>
                            </div>
                            <div class="form-group">
                                <label for="title_balance_ratio">Title Balance Ratio (0.0-2.0)</label>
                                <input type="range" id="title_balance_ratio" name="title_balance_ratio" 
                                       min="0" max="2" step="0.01" value="0.75" required>
                                <span id="title_balance_ratio_value">0.75</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="gamification_balance">Gamification Balance</label>
                            <input type="number" id="gamification_balance" name="gamification_balance" 
                                   min="0" max="1000" step="0.1" value="150.0" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="predict-btn">🔮 Prediksi Risiko Kelelahan</button>
                </form>
                
                <div id="results" class="results">
                    <div id="riskCard" class="risk-card">
                        <div id="riskLevel">Medium Risk</div>
                        <div style="font-size: 16px; margin-top: 10px;">
                            Confidence: <span id="confidence">75.4%</span>
                        </div>
                    </div>
                    
                    <div class="prob-bars">
                        <div class="prob-bar">
                            <div class="prob-label">
                                <span>🟢 Low Risk</span>
                                <span id="prob-low">12.3%</span>
                            </div>
                            <div class="prob-progress">
                                <div id="fill-low" class="prob-fill" style="background: #28a745; width: 12.3%;"></div>
                            </div>
                        </div>
                        
                        <div class="prob-bar">
                            <div class="prob-label">
                                <span>🟡 Medium Risk</span>
                                <span id="prob-medium">75.4%</span>
                            </div>
                            <div class="prob-progress">
                                <div id="fill-medium" class="prob-fill" style="background: #ffc107; width: 75.4%;"></div>
                            </div>
                        </div>
                        
                        <div class="prob-bar">
                            <div class="prob-label">
                                <span>🔴 High Risk</span>
                                <span id="prob-high">12.3%</span>
                            </div>
                            <div class="prob-progress">
                                <div id="fill-high" class="prob-fill" style="background: #dc3545; width: 12.3%;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="recommendations" class="recommendations">
                        <h3>💡 Rekomendasi</h3>
                        <ul id="recommendationList">
                            <li>Perhatikan pola istirahat dan aktivitas</li>
                            <li>Lakukan aktivitas ringan untuk recovery</li>
                            <li>Monitor gejala kelelahan</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        // Update slider values
        document.getElementById('weekly_efficiency').addEventListener('input', function() {
            document.getElementById('weekly_efficiency_value').textContent = this.value;
        });
        
        document.getElementById('achievement_rate').addEventListener('input', function() {
            document.getElementById('achievement_rate_value').textContent = this.value;
        });
        
        document.getElementById('title_balance_ratio').addEventListener('input', function() {
            document.getElementById('title_balance_ratio_value').textContent = this.value;
        });
        
        // Form submission (demo - replace with actual API call)
        document.getElementById('predictionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Simulate prediction (replace with actual API call)
            setTimeout(() => {
                // Demo results
                const predictions = ['low_risk', 'medium_risk', 'high_risk'];
                const randomPrediction = predictions[Math.floor(Math.random() * predictions.length)];
                
                const probabilities = {
                    'low_risk': Math.random() * 0.4,
                    'medium_risk': Math.random() * 0.6,
                    'high_risk': Math.random() * 0.4
                };
                
                // Normalize probabilities
                const total = Object.values(probabilities).reduce((a, b) => a + b, 0);
                Object.keys(probabilities).forEach(key => {
                    probabilities[key] = probabilities[key] / total;
                });
                
                displayResults(randomPrediction, probabilities);
            }, 1000);
        });
        
        function displayResults(prediction, probabilities) {
            const resultsDiv = document.getElementById('results');
            const riskCard = document.getElementById('riskCard');
            const riskLevel = document.getElementById('riskLevel');
            const confidence = document.getElementById('confidence');
            
            // Update risk level display
            const riskLabels = {
                'low_risk': 'Risiko Rendah',
                'medium_risk': 'Risiko Sedang',
                'high_risk': 'Risiko Tinggi'
            };
            
            const riskClasses = {
                'low_risk': 'risk-low',
                'medium_risk': 'risk-medium',
                'high_risk': 'risk-high'
            };
            
            riskLevel.textContent = riskLabels[prediction];
            confidence.textContent = (probabilities[prediction] * 100).toFixed(1) + '%';
            
            // Update card styling
            riskCard.className = 'risk-card ' + riskClasses[prediction];
            
            // Update probability bars
            document.getElementById('prob-low').textContent = (probabilities.low_risk * 100).toFixed(1) + '%';
            document.getElementById('prob-medium').textContent = (probabilities.medium_risk * 100).toFixed(1) + '%';
            document.getElementById('prob-high').textContent = (probabilities.high_risk * 100).toFixed(1) + '%';
            
            document.getElementById('fill-low').style.width = (probabilities.low_risk * 100) + '%';
            document.getElementById('fill-medium').style.width = (probabilities.medium_risk * 100) + '%';
            document.getElementById('fill-high').style.width = (probabilities.high_risk * 100) + '%';
            
            // Update recommendations
            const recommendations = {
                'low_risk': [
                    'Kondisi baik, lanjutkan aktivitas normal',
                    'Tetap jaga pola istirahat yang sehat',
                    'Monitor kondisi secara berkala'
                ],
                'medium_risk': [
                    'Perhatikan pola istirahat dan aktivitas',
                    'Lakukan aktivitas ringan untuk recovery',
                    'Monitor gejala kelelahan'
                ],
                'high_risk': [
                    'Segera istirahat dan kurangi aktivitas berat',
                    'Monitor kondisi kesehatan secara berkala',
                    'Pertimbangkan konsultasi dengan profesional kesehatan'
                ]
            };
            
            const recommendationList = document.getElementById('recommendationList');
            recommendationList.innerHTML = '';
            recommendations[prediction].forEach(rec => {
                const li = document.createElement('li');
                li.textContent = rec;
                recommendationList.appendChild(li);
            });
            
            // Show results
            resultsDiv.style.display = 'block';
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
