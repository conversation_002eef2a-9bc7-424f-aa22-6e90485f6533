================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Features: 20
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 153, 'low_risk': 115, 'high_risk': 23}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.7119
     - Test F1-Score: 0.7123
     - CV Accuracy: 0.6935 (±0.0446)
     - CV F1-Score: 0.6809 (±0.0545)
   • Random Forest:
     - Test Accuracy: 0.6949
     - Test F1-Score: 0.6952
     - CV Accuracy: 0.6464 (±0.0190)
     - CV F1-Score: 0.6413 (±0.0209)
   • Gradient Boosting:
     - Test Accuracy: 0.6441
     - Test F1-Score: 0.6465
     - CV Accuracy: 0.6810 (±0.0532)
     - CV F1-Score: 0.6692 (±0.0509)
   • XGBoost:
     - Test Accuracy: 0.7966
     - Test F1-Score: 0.7954
     - CV Accuracy: 0.6676 (±0.0642)
     - CV F1-Score: 0.6616 (±0.0638)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. ⭐ pomokit_unique_words: 0.0543
      2. ⭐ total_title_diversity: 0.0530
      3. ⭐ title_balance_ratio: 0.0513
      4. 🔸 avg_time_minutes: 0.0470
      5. 🔸 total_time_minutes: 0.0400
      6. 🔸 work_days: 0.0352
      7. 🔸 consistency_score: 0.0310
      8. 🔸 gamification_balance: 0.0288
      9. 🔸 avg_distance_km: 0.0284
     10. 🔸 activity_points: 0.0270
     11. 🔸 achievement_rate: 0.0226
     12. ▫️ total_distance_km: 0.0111
     13. ▫️ productivity_points: 0.0074
     14. ▫️ strava_title_count: 0.0026
     15. ▫️ strava_unique_words: 0.0021
     16. ▫️ avg_cycles: 0.0000
     17. ▫️ activity_days: 0.0000
     18. ▫️ total_cycles: 0.0000
     19. ▫️ pomokit_title_count: 0.0000
     20. ▫️ weekly_efficiency: 0.0000

   📊 Random Forest:
      1. ⭐ pomokit_unique_words: 0.0554
      2. ⭐ total_title_diversity: 0.0537
      3. ⭐ title_balance_ratio: 0.0522
      4. 🔸 avg_time_minutes: 0.0476
      5. 🔸 total_time_minutes: 0.0405
      6. 🔸 work_days: 0.0358
      7. 🔸 consistency_score: 0.0301
      8. 🔸 gamification_balance: 0.0282
      9. 🔸 avg_distance_km: 0.0280
     10. 🔸 activity_points: 0.0275
     11. 🔸 achievement_rate: 0.0227
     12. ▫️ total_distance_km: 0.0113
     13. ▫️ productivity_points: 0.0066
     14. ▫️ strava_title_count: 0.0026
     15. ▫️ strava_unique_words: 0.0022
     16. ▫️ avg_cycles: 0.0000
     17. ▫️ activity_days: 0.0000
     18. ▫️ total_cycles: 0.0000
     19. ▫️ pomokit_title_count: 0.0000
     20. ▫️ weekly_efficiency: 0.0000

   📊 Gradient Boosting:
      1. ⭐ pomokit_unique_words: 0.0553
      2. ⭐ total_title_diversity: 0.0536
      3. ⭐ title_balance_ratio: 0.0521
      4. 🔸 avg_time_minutes: 0.0473
      5. 🔸 total_time_minutes: 0.0404
      6. 🔸 work_days: 0.0358
      7. 🔸 consistency_score: 0.0305
      8. 🔸 gamification_balance: 0.0287
      9. 🔸 avg_distance_km: 0.0283
     10. 🔸 activity_points: 0.0268
     11. 🔸 achievement_rate: 0.0220
     12. ▫️ total_distance_km: 0.0113
     13. ▫️ productivity_points: 0.0070
     14. ▫️ strava_unique_words: 0.0029
     15. ▫️ strava_title_count: 0.0028
     16. ▫️ total_cycles: 0.0000
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ activity_days: 0.0000
     19. ▫️ pomokit_title_count: 0.0000
     20. ▫️ weekly_efficiency: 0.0000

   📊 XGBoost:
      1. ⭐ pomokit_unique_words: 0.0554
      2. ⭐ total_title_diversity: 0.0530
      3. ⭐ title_balance_ratio: 0.0518
      4. 🔸 avg_time_minutes: 0.0472
      5. 🔸 total_time_minutes: 0.0399
      6. 🔸 work_days: 0.0349
      7. 🔸 consistency_score: 0.0309
      8. 🔸 avg_distance_km: 0.0286
      9. 🔸 gamification_balance: 0.0285
     10. 🔸 activity_points: 0.0273
     11. 🔸 achievement_rate: 0.0218
     12. ▫️ total_distance_km: 0.0122
     13. ▫️ productivity_points: 0.0076
     14. ▫️ strava_title_count: 0.0028
     15. ▫️ strava_unique_words: 0.0021
     16. ▫️ total_cycles: 0.0000
     17. ▫️ pomokit_title_count: 0.0000
     18. ▫️ avg_cycles: 0.0000
     19. ▫️ activity_days: 0.0000
     20. ▫️ weekly_efficiency: 0.0000

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • pomokit_unique_words: 4/4 algorithms (100.0%)
     • total_title_diversity: 4/4 algorithms (100.0%)
     • title_balance_ratio: 4/4 algorithms (100.0%)
     • avg_time_minutes: 4/4 algorithms (100.0%)
     • total_time_minutes: 4/4 algorithms (100.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: XGBoost (0.7966 accuracy)
   • Top 5 SHAP features for production:
     1. pomokit_unique_words (SHAP: 0.0554)
     2. total_title_diversity (SHAP: 0.0530)
     3. title_balance_ratio (SHAP: 0.0518)
     4. avg_time_minutes (SHAP: 0.0472)
     5. total_time_minutes (SHAP: 0.0399)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 20
   • Best accuracy achieved: 0.7966
   • Analysis timestamp: 2025-07-22T03:03:08.417878