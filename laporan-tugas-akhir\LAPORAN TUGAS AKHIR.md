**_SHAP-BASED FEATURE SELECTION_ UNTUK KLASIFIKASI RISIKO _FATIGUE_ MAHASISWA PADA DATA AKTIVITAS KARDIOVASKULAR DAN PRODUKTIVITAS AKADEMIK**

# <a name="_toc204135562"></a>**ABSTRAK**

Penelitian ini mengembangkan sistem klasifikasi risiko _fatigue_ mahasiswa menggunakan SHAP _feature selection_ pada data aktivitas kardiovaskular dan produktivitas akademik. Dataset terdiri dari 291 observasi mingguan dengan 20 fitur yang dianalisis menggunakan empat algoritma _machine learning_ (Logistic Regression, Random Forest, Gradient Boosting, dan <PERSON>GB<PERSON>t). Hasil menunjukkan fitur linguistik mendominasi prediksi _fatigue_, dengan pomokit_unique_words (5.54%), total_title_diversity (5.33%), dan title_balance_ratio (5.19%) sebagai prediktor terkuat yang mencerminkan pola kognitif mahasiswa. XGBoost mencapai akurasi terbaik 79.66%, namun berisiko _overfitting_ tinggi, sementara Logistic Regression menunjukkan stabilitas superior (71.19% test accuracy, 69.35% CV accuracy) dengan _overfitting_ _score_ terendah. Analisis berbasis _title_ aktivitas terbukti efektif dengan kontribusi fitur linguistik 15.06% dari total _feature importance_, lebih tinggi dari fitur kuantitatif. SHAP _feature selection_ terbukti superior dibandingkan _random selection_ dengan peningkatan performa 0.17%-1.91% pada sebagian besar algoritma. Kontribusi utama penelitian adalah identifikasi fitur linguistik sebagai prediktor dominan _fatigue_, pengembangan _framework bias correction_ yang _robust_, dan validasi _title-only analysis_ untuk _monitoring fatigue_ yang praktis dan non-_intrusive_.

**Kata kunci:** _Fatigue_, SHAP, _machine learning_, fitur linguistik, _title-only analysis_

# <a name="_toc204135563"></a>**_ABSTRACT_**

This research develops a student fatigue risk classification system using SHAP feature selection on cardiovascular and academic productivity activity data. The dataset consists of 291 weekly observations with 20 features analyzed using four machine learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, and XGBoost). Results show linguistic features dominate fatigue prediction, with pomokit_unique_words (5.54%), total_title_diversity (5.33%), and title_balance_ratio (5.19%) as the strongest predictors reflecting students' cognitive patterns. XGBoost achieved the best accuracy of 79.66% but showed high overfitting risk, while Logistic Regression demonstrated superior stability (71.19% test accuracy, 69.35% CV accuracy) with the lowest overfitting score. Title-only analysis proved effective with linguistic features contributing 15.06% of total feature importance, higher than quantitative features. SHAP feature selection outperformed random selection with performance improvements of 0.17%-1.91% across most algorithms. Main research contributions include identification of linguistic features as dominant fatigue predictors, development of robust bias correction framework, and validation of title-only analysis for practical and non-intrusive fatigue monitoring.

**Keywords:** Fatigue, SHAP, machine learning, linguistic features, title-only analysis

# <a name="_toc204135567"></a>**BAB I**

# <a name="_toc204135568"></a>**PENDAHULUAN**

1. ## <a name="_toc204135569"></a>**Latar Belakang**

    Dalam era digital yang semakin berkembang pesat, mahasiswa menghadapi tantangan yang kompleks dalam menyeimbangkan aktivitas fisik dan produktivitas akademik. Gaya hidup sedentari yang dominan di kalangan mahasiswa, terutama yang terlibat dalam bidang teknologi informasi, telah menjadi perhatian serius dalam konteks kesehatan kardiovaskular dan manajemen _fatigue_.

    Aktivitas kardiovaskular memiliki peran penting dalam menjaga kesehatan fisik dan mental mahasiswa. Penelitian menunjukkan bahwa aktivitas fisik yang teratur dapat meningkatkan fungsi kognitif, mengurangi tingkat stres, dan meningkatkan produktivitas akademik (Güneş & Demirer, 2022; Hanif et al., 2024). Namun, banyak mahasiswa mengalami kesulitan dalam mempertahankan rutinitas aktivitas fisik yang konsisten karena tuntutan akademik yang tinggi dan manajemen waktu yang kurang efektif (Yosep et al., 2023).

    _Fatigue_ atau kelelahan merupakan fenomena multidimensional yang dapat mempengaruhi performa akademik dan kualitas hidup mahasiswa. _Fatigue_ tidak hanya berkaitan dengan kelelahan fisik, tetapi juga meliputi aspek mental dan emosional yang dapat berdampak pada motivasi, konsentrasi, dan kemampuan pengambilan keputusan (Chayati et al., 2023; Shoiab et al., 2022). Identifikasi dini terhadap risiko _fatigue_ menjadi krusial untuk mencegah dampak negatif yang lebih luas.

    Perkembangan teknologi _wearable_ dan aplikasi _mobile_ telah membuka peluang baru dalam _monitoring_ aktivitas fisik dan produktivitas secara _real-time_. Platform seperti Strava untuk _tracking_ aktivitas kardiovaskular dan Pomokit untuk manajemen produktivitas menyediakan data yang kaya dan dapat dianalisis untuk memahami pola perilaku mahasiswa (Gholami et al., 2020). Integrasi data dari kedua platform ini memungkinkan analisis holistik terhadap hubungan antara aktivitas fisik dan produktivitas akademik (Moshawrab et al., 2022).

    _Machine learning_ dan _artificial intelligence_ telah menunjukkan potensi besar dalam analisis data kesehatan dan prediksi risiko. Penerapan algoritma klasifikasi untuk prediksi _fatigue_ berdasarkan data aktivitas fisik dan produktivitas dapat memberikan _insight_ yang _valuable_ untuk intervensi preventif. Pendekatan ini memungkinkan personalisasi strategi manajemen kesehatan berdasarkan pola individual setiap mahasiswa. Penelitian menunjukkan bahwa machine learning dapat mengidentifikasi pola yang kompleks dalam data besar, yang berkaitan erat dengan kesehatan mental dan fisik individu (Masrom et al., 2023; Olsavszky et al., 2020).

    Penelitian ini menjadi relevan karena kurangnya studi yang mengintegrasikan data aktivitas kardiovaskular dan produktivitas akademik untuk prediksi _fatigue_ pada populasi mahasiswa Indonesia. Mayoritas penelitian sebelumnya fokus pada satu aspek saja atau menggunakan data sekunder, sehingga tidak dapat menangkap kompleksitas hubungan antara aktivitas fisik, produktivitas, dan _fatigue_ dalam konteks kehidupan mahasiswa yang sesungguhnya (Siregar et al., 2020).

1. ## <a name="_toc204135570"></a>**Identifikasi Masalah**

    Berdasarkan latar belakang yang telah diuraikan, penelitian ini merumuskan beberapa permasalahan utama:

1. Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko _fatigue_ pada mahasiswa.
1. Bagaimana efektivitas model _machine learning_ dalam mengklasifikasikan tingkat risiko _fatigue_ berdasarkan data aktivitas kardiovaskular dan produktivitas.
1. Apakah analisis berbasis judul aktivitas (_title-only analysis_) dapat memberikan prediksi _fatigue_ yang akurat tanpa memerlukan data kuantitatif lengkap.

    1. ## <a name="_toc204135571"></a>**Tujuan dan Manfaat**
        Tujuan dari penelitian ini adalah sebagai berikut:

1. Mengidentifikasi faktor-faktor prediktor utama risiko _fatigue_ melalui _feature selection_
1. Mengembangkan dan mengevaluasi model _machine learning_ untuk klasifikasi _fatigue_
1. Mengeksplorasi efektivitas _title-only analysis_ untuk prediksi _fatigue_

Adapun Manfaat dari penelitian ini adalah sebagai berikut:

1. Menyediakan dataset dan _methodology_ yang dapat direplikasi, memberikan _insight_ untuk pengembangan intervensi preventif _fatigue_, dan mendukung penelitian lanjutan dalam bidang _digital_ _health_.
1. Memperkenalkan pendekatan novel dalam analisis data aktivitas fisik dan produktivitas, mengembangkan protokol penelitian untuk studi longitudinal berbasis _wearable_ _technology_, dan menyediakan _baseline_ _methodology_ untuk penelitian serupa di masa depan.
1. Mengeksplorasi aplikasi algoritma klasifikasi untuk prediksi _fatigue_, mengembangkan teknik _feature_ _engineering_ khusus untuk data aktivitas mahasiswa, memberikan kontribusi pada pengembangan _AI-driven health monitoring systems_.

1. ## <a name="_toc204135572"></a>**Ruang Lingkup**
1. Penelitian ini menggunakan desain _cross-sectional_ dengan sedikit elemen _longitudinal_ untuk menganalisis aktivitas mahasiswa secara observasional. Berdasarkan data primer yang dikumpulkan langsung dari mahasiswa memlalui aplikasi Strava (aktivitas fisik) dan Pomokit (produktivitas akademik), lalu dianalisis menggunakan algoritma _machine learning_ untuk mengklasifikasi risiko kelelahan (_fatigue_). Perlu dicatat bahwa data bersifat _self-reported_ sehingga ada potensi bias, serta tidak mencakup aktivitas di luar platform digital.
1. Analisis hanya menggunakan data dari Strava dan Pomokit. Model klasifikasi yang digunakan adalah algoritma _supervised learning_ (_Logistic Regression, Random Forest, Gradient Boosting, XGBoost_) dengan proses _feature engineering_, _feature selection_, serta evaluasi menggunakan metrik klasifikasi standar. Hasil analisis sangat bergantung pada kualitas data dari sensor dan algoritma aplikasi yang digunakan.
1. Penelitian difokuskan pada mahasiswa Indonesia yang aktif menggunakan aplikasi pelacak aktivitas dan produktivitas. Partisipan memiliki literasi digital yang cukup dan motivasi untuk memantau diri. Hasil penelitian hanya relevan untuk kelompok mahasiswa dengan karakteristik serupa, terutama dari latar belakang sosial ekonomi tertentu.
1. Data dikumpulkan dalam periode waktu terbatas dan dianalisis untuk melihat pola aktivitas mingguan. Penelitian ini tidak mencakup data jangka panjang maupun variasi musiman, tetapi fokus pada pengembangan model prediksi yang bisa digunakan untuk pemantauan _real-time_.

# <a name="_toc204135573"></a>**BAB II**

# <a name="_toc204135574"></a>**LANDASAN TEORI**

1. ## <a name="_toc204135575"></a>**_State of the Art_**
    Berbagai penelitian yang telah di lakukan beberapa tahun kebelakang menunjukkan pendekatan _machine learning_ terhadap _mental health_ menunjukkan hasil yang cukup baik dalam melakukan prediksi dengan memanfaatkan data aktivitas fisik dan objek mahasiswa.

Dalam penelitian yang dilakukan Zhang et al. (2024) (L. Zhang et al., 2024) yang mencapai akurasi 83.64% menggunakan algoritma _XGBoost_ untuk prediksi GPA. Studi ini merupakan terobosan pertama yang mengintegrasikan 24 indikator psikologis dari instrumen SCL-90 (_Symptom_ _Checklist_-90) dengan 7 mata kuliah dasar pada 229 mahasiswa teknik mesin dari _Henan University of Science and Technology_, China. Keunggulan utama penelitian ini terletak pada pendekatan holistik yang menggabungkan _assessment_ psikologis komprehensif (mencakup _somatization_, _depression_, _hostility_, dan _interpersonal_ _sensitivity_) dengan data akademik, menciptakan model prediksi yang tidak hanya akurat secara statistik tetapi juga relevan secara klinis untuk _early_ _intervention_ dalam mendukung kesuksesan akademik mahasiswa.

Zhang et al. (T. Zhang et al., 2024) mencapai AUC tertinggi 87.2% dengan akurasi 79.2% menggunakan _XGBoost_ dengan SHAP (_SHapley Additive exPlanations_). Hasil ini menunjukkan bahwa kombinasi antara model _XGBoost_ dan teknik interpretabilitas seperti SHAP tidak hanya meningkatkan performa prediksi, tetapi juga memberikan pemahaman yang lebih mendalam terhadap kontribusi masing-masing fitur dalam menentukan output model. Pendekatan ini sangat relevan dalam konteks kesehatan mental, di mana transparansi dan interpretabilitas model menjadi aspek penting untuk membangun kepercayaan pengguna dan memastikan pengambilan keputusan yang dapat dipertanggungjawabkan.

Ratul et al. (Ratul et al., 2023) menempati posisi ketiga dengan akurasi 80.5% menggunakan _Multi-Layer Perceptron_ kombinasi _Principal Component Analysis_ untuk prediksi stres pada 444 mahasiswa _multi-etnis_ selama pandemi COVID-19. Penelitian ini mendemonstrasikan efektivitas reduksi dimensionalitas dengan retensi _varians_ 70% dalam menangani kompleksitas data mahasiswa dari berbagai latar belakang etnis, menggunakan _Perceived Stress Scale_ (PSS) sebagai _outcome measure_ yang _validated_. Kontribusi signifikan studi ini terletak pada validasi _cross-cultural approach_ dalam prediksi stres mahasiswa, menunjukkan bahwa 24.10% mahasiswa mengalami _extremely high psychological_ _stress_ selama pandemi, dengan faktor-faktor seperti _increased internet usage, social isolation,_ dan _academic uncertainty_ menjadi prediktor utama yang diidentifikasi melalui kombinasi PCA dan _chi-squared feature selection_.

Lekkas et al. (Lekkas et al., 2021) mencapai AUC 75.5% dengan akurasi 70.2% menggunakan _ensemble machine learning_ untuk prediksi ideasi bunuh diri pada 52 remaja Jerman melalui analisis aktivitas Instagram, mewakili inovasi _pioneering_ dalam integrasi _social media_ data dengan _mental health prediction_ yang menunjukkan bahwa _digital behavioral patterns_ dapat menjadi _early warning indicators_ untuk _high-risk mental health conditions_. Yan et al. (Yan et al., 2020) menggunakan _Mixed Effects Random Forest_ untuk estimasi _daily affect_ dari 48 mahasiswa Dartmouth dengan data _wearable sensor longitudinal_, mendemonstrasikan kemampuan _personalized modeling_ yang dapat mengakomodasi _individual differences_ dalam _response patterns_ terhadap _physical activity_ dan _environmental factors_. Teixeira et al. (Teixeira et al., 2025) menunjukkan _transformation_ paling dramatis dengan peningkatan dari 35% baseline menjadi 87% akurasi setelah penerapan _Recursive Feature Elimination_ pada prediksi _mental fatigue_ atlet muda menggunakan integrasi data GPS _tracking_ dan _physiological_ _monitoring_, membuktikan bahwa _appropriate_ _feature_ _selection_ dapat menghasilkan _clinical-grade performance_ bahkan pada domain yang _challenging_ seperti _sports psychology_.

1. ## <a name="_toc204135576"></a>**Aktivitas Kardiovaskular**

    Aktivitas kardiovaskular merupakan bentuk latihan fisik yang melibatkan sistem jantung dan pembuluh darah untuk meningkatkan kapasitas aerobik tubuh. Aktivitas kardiovaskular didefinisikan sebagai aktivitas fisik yang meningkatkan detak jantung dan pernapasan secara berkelanjutan, melibatkan kelompok otot besar, dan dapat dipertahankan dalam periode waktu tertentu. Dalam konteks mahasiswa, aktivitas kardiovaskular mencakup berbagai bentuk latihan seperti berjalan kaki, berlari, bersepeda, berenang, dan aktivitas olahraga lainnya yang dapat meningkatkan kebugaran kardiorespiratori (Lv et al., 2024; Silva et al., 2022). Aktivitas ini memiliki peran penting dalam menjaga kesehatan fisik dan mental, terutama bagi populasi mahasiswa yang cenderung memiliki gaya hidup sedentari akibat tuntutan akademik (X. Zhang et al., 2021).

    Parameter utama dalam mengukur aktivitas kardiovaskular meliputi durasi, frekuensi, dan jenis aktivitas. Durasi mengacu pada lamanya aktivitas dilakukan dalam satu sesi, sedangkan frekuensi menunjukkan seberapa sering aktivitas dilakukan dalam periode tertentu (Lv et al., 2024). Dalam penelitian ini, metrik aktivitas kardiovaskular yang digunakan mencakup _total distance_ (jarak tempuh), _moving time_ (durasi aktivitas), _activity frequency_ (frekuensi aktivitas), dan _activity type_ (jenis aktivitas). Data ini diperoleh melalui platform Strava yang memungkinkan _tracking_ aktivitas secara _real-time_ dengan akurasi tinggi menggunakan GPS dan _sensor wearable devices_.

1. ## <a name="_toc204135577"></a>**Produktivitas Akademik**

    Produktivitas akademik merujuk pada efisiensi dan efektivitas mahasiswa dalam menyelesaikan tugas-tugas akademik dan mencapai tujuan pembelajaran. Konsep ini mencakup kemampuan untuk mengoptimalkan penggunaan waktu, sumber daya, dan energi untuk menghasilkan output akademik yang berkualitas dalam periode waktu tertentu (Igiri et al., 2021; Muhammad et al., 2023). Dalam penelitian ini, produktivitas akademik diukur melalui berbagai indikator yang diperoleh dari platform Pomokit, termasuk jumlah siklus pomodoro yang diselesaikan, konsistensi dalam bekerja, efisiensi waktu, dan tingkat pencapaian target yang telah ditetapkan. Pendekatan ini memberikan gambaran objektif tentang pola kerja dan produktivitas mahasiswa dalam aktivitas akademik sehari-hari.

    Teknik Pomodoro merupakan metode manajemen waktu yang dikembangkan oleh Francesco Cirillo pada akhir 1980-an. Teknik ini melibatkan pembagian waktu kerja menjadi _interval-interval_ fokus yang dipisahkan oleh istirahat singkat. Setelah menyelesaikan beberapa _interval_, dilakukan istirahat yang lebih Panjang. Penelitian menunjukkan bahwa teknik Pomodoro efektif dalam meningkatkan fokus, mengurangi prokrastinasi, dan meningkatkan produktivitas akademik mahasiswa (Alale et al., 2020; Rashid et al., 2020). Dalam konteks mahasiswa, teknik ini membantu dalam mengelola beban kerja akademik yang berat dan mempertahankan konsentrasi dalam periode belajar yang panjang. Platform Pomokit mengimplementasikan teknik ini dengan fitur _tracking_ dan gamifikasi untuk meningkatkan motivasi pengguna.

1. ## <a name="_toc204135578"></a>**Gamifikasi**

    Gamifikasi merupakan penerapan elemen-elemen _game_ _design_ dalam konteks _non-game_ untuk meningkatkan _engagement_, _motivasi_, dan _behavior_ _change_. Dalam aplikasi kesehatan dan produktivitas, gamifikasi dapat meningkatkan _adherence_ terhadap program kesehatan dan mempertahankan motivasi jangka panjang (Rajani et al., 2023; Warsinsky et al., 2021). Efektivitas gamifikasi didukung oleh berbagai teori psikologi, termasuk _Self-Determination Theory, Flow Theory, dan Social Cognitive Theory_. _Self-Determination Theory_ menekankan pentingnya _autonomy, competence,_ dan _relatedness_ dalam mempertahankan motivasi intrinsik (Merhabi et al., 2021).

    Penelitian menunjukkan bahwa gamifikasi dapat efektif dalam mendorong _health behavior change_, termasuk peningkatan aktivitas fisik, _adherence_ terhadap _medication_, dan _lifestyle_ _modification_ (Rajani et al., 2023; Warsinsky et al., 2021). Namun, efektivitas gamifikasi tergantung pada design yang _appropriate_, _target population_, dan _sustainability_ jangka panjang.

1. ## <a name="_toc204135579"></a>**_Fatigue_ dan Klasifikasi Risiko**

    _Fatigue_ atau kelelahan merupakan kondisi subjektif yang ditandai dengan perasaan lelah, kurang energi, dan penurunan kapasitas untuk melakukan aktivitas fisik atau mental. Dalam konteks akademik, _fatigue_ dapat mempengaruhi kemampuan belajar, konsentrasi, dan performa akademik secara keseluruhan. _Fatigue_ dapat diklasifikasikan menjadi beberapa jenis: _physical fatigue_ (kelelahan fisik), _mental fatigue_ (kelelahan mental), dan _emotional fatigue_ (kelelahan emosional). _Physical fatigue_ berkaitan dengan kelelahan otot dan sistem kardiovaskular, _mental fatigue_ terkait dengan penurunan fungsi kognitif dan konsentrasi, sedangkan _emotional fatigue_ berkaitan dengan stres dan beban emosional (Sfeir et al., 2022).

    Faktor risiko _fatigue_ pada mahasiswa meliputi beban kerja akademik yang berlebihan, kurang tidur, pola makan yang tidak sehat, kurangnya aktivitas fisik, stres akademik, dan ketidakseimbangan _work-life balance_ (Sfeir et al., 2022). Indikator _fatigue_ dapat diidentifikasi melalui berbagai parameter seperti penurunan produktivitas, kesulitan konsentrasi, perubahan mood, dan penurunan motivasi (Zeng et al., 2020). Dalam penelitian ini, klasifikasi risiko _fatigue_ dilakukan berdasarkan kombinasi indikator dari aktivitas fisik dan produktivitas akademik. Model klasifikasi menggunakan _machine learning_ untuk mengidentifikasi pola yang berkaitan dengan risiko _fatigue_ rendah, sedang, dan tinggi berdasarkan data historis aktivitas mahasiswa.

1. ## <a name="_toc204135580"></a>**_Machine Learning_**
1. **_Logistic Regression_**

    _Logistic Regression_ merupakan algoritma klasifikasi linear yang menggunakan fungsi logistik (sigmoid) untuk memodelkan probabilitas kelas target. Algoritma ini sangat efektif untuk _binary_ dan _multi-class_ _classification_ dengan interpretabilitas yang tinggi (Murri et al., 2024). Dalam konteks _fatigue_ _prediction_, _Logistic Regression_ memberikan _baseline_ yang solid dan mudah diinterpretasi. Keunggulan _Logistic Regression_ meliputi _computational efficiency_, tidak memerlukan _tuning_ _hyperparameter_ yang kompleks, _robust_ terhadap _outliers_, dan memberikan probabilitas _output_ yang dapat diinterpretasi. Algoritma ini cocok untuk dataset berukuran sedang dan memberikan generalisasi yang baik.

1. **_Random Forest_**

    _Random Forest_ merupakan _ensemble learning method_ yang mengombinasikan _multiple decision trees_ untuk meningkatkan akurasi dan mengurangi _overfitting_. Algoritma ini menggunakan _bagging_ (_bootstrap aggregating_) dan _random feature selection_ untuk membangun _diverse trees_ (Praveen et al., 2023). Proses _Random Forest_ melibatkan _bootstrap sampling_ untuk membuat _multiple training sets, random feature selection_ pada setiap _split node, training multiple decision trees_ secara independen, dan aggregasi prediksi melalui _majority voting_ untuk klasifikasi atau _averaging_ untuk regresi. Keunggulan _Random Forest_ meliputi kemampuan menangani berbagai tipe data dan menyediakan interpretasi pentingnya fitur, serta ketahanannya terhadap _noise_ dan data yang hilang (Wang, 2024). Algoritma ini sangat cocok untuk dataset dengan fitur heterogen seperti dalam penelitian _fatigue prediction_.

1. **_Gradient Boosting_**

    _Gradient Boosting_ merupakan _ensemble method_ yang membangun model secara _sequential_, dimana setiap model baru memperbaiki kesalahan dari model sebelumnya. Algoritma ini menggunakan _gradient descent_ untuk meminimalkan _loss function_ dengan menambahkan _weak learners_ secara iteratif. Proses _Gradient Boosting_ dimulai dengan inisialisasi model menggunakan prediksi konstan, kemudian untuk setiap iterasi dilakukan perhitungan residual sebagai _gradient_ dari _loss function_, _training weak learner_ untuk memprediksi residual tersebut, dan _update_ model dengan menambahkan _weak learner_ yang telah di-_scale_. Algoritma ini memberikan performa tinggi namun memerlukan _careful_ _tuning_ untuk menghindari _overfitting_, terutama pada dataset berukuran kecil hingga sedang (Zhao et al., 2021).

1. **_XGBoost_**

    _XGBoost_ merupakan _optimized gradient boosting framework_ yang dirancang untuk _speed_ dan _performance_. Algoritma ini mengimplementasikan berbagai optimasi, meliputi regularisasi, pemrosesan paralel, dan teknik pemangkasan pohon yang canggih. Inovasi utama _XGBoost_ mencakup regularisasi dengan L1 dan L2 regularization untuk mencegah _overfitting_, serta peningkatan efektivitas pelatihan dengan pemrosesan paralel yang efisien dan penanganan otomatis untuk nilai yang hilang, dan _built-in cross-validation_ untuk _hyperparameter_ _tuning_ yang optimal. _XGBoost_ sangat efektif untuk data terstruktur dan sering menjadi algoritma pilihan dalam _kompetisi machine learning_ (Sheng & Huang, 2022). Dalam konteks _fatigue prediction_, XGBoost dapat menangkap _complex_ _interactions_ antara fitur aktivitas fisik dan produktivitas akademik.

1. **SHAP (_SHapley Additive exPlanations_)**

    SHAP merupakan framework untuk memberikan penjelasan terhadap keluaran dari model machine learning yang diambil dari teori permainan, khususnya nilai _Shapley_ dari teori permainan kooperatif. SHAP memberikan pendekatan yang terpadu untuk interpretabilitas model yang dapat diterapkan pada berbagai jenis algoritma _machine learning_. SHAP memiliki beberapa _desirable properties_ yang mencakup efisiensi, simetri, dan sifat penambahan, yang semuanya mendukung kemampuan untuk menjelaskan pengaruh setiap fitur terhadap _output_ model (Twick et al., 2022). Dalam penelitian ini, SHAP digunakan untuk feature selection dan interpretabilitas model _fatigue prediction_. SHAP memungkinkan identifikasi fitur yang paling kontributif terhadap prediksi risiko _fatigue_ dan memberikan _insights_ tentang bagaimana setiap fitur mempengaruhi _output_ model.

1. ## <a name="_toc204135581"></a>**_Text-Based Feature Engineering_**
1. **Konsep _Text Mining_**

    _Text mining_ merupakan proses ekstraksi informasi dari data tekstual untuk mengidentifikasi pola dan insights yang berguna dalam analisis. Dalam konteks _health analytics_, teknik ini berfungsi untuk mengidentifikasi pola dan indikator yang berkaitan dengan kondisi kesehatan, terutama melalui analisis deskripsi aktivitas yang dihasilkan oleh pengguna, seperti yang ditemukan pada platform Strava dan Pomokit. Mencari pola kelelahan melalui analisis judul aktivitas menggunakan pendekatan berbasis aturan dan kamus yang telah ditentukan sebelumnya dapat menangkap indikator perilaku yang tidak terdeteksi oleh metrik kuantitatif tradisional, mendukung efektivitas pendekatan ini dalam menggali data dari teks yang tidak terstruktur (S. C. Gupta & Goyal, 2024; Li et al., 2022).

1. **_Keyword-Based Feature Extraction_**

    _Keyword-based feature extraction_ merupakan pendekatan _rule-based_ yang mengidentifikasi istilah spesifik yang berkaitan dengan kondisi _fatigue_. Pendekatan ini efektif untuk menangkap eksplisit, seperti stres dan beban kerja, dengan cara yang lebih akurat (Mylläri et al., 2022). Dictionary-based approach digunakan dengan keywords yang telah didefinisikan berdasarkan domain _knowledge_ dan _literature review_ untuk mengidentifikasi indikator seperti _stress count_, _workload count_, _negative emotion count_, dan _recovery count_ (Mylläri et al., 2022). Pendekatan ini juga sejalan dengan penggunaan analisis teks dalam model pengobatan berbasis internet, yang mencatat bahwa analisis tersebut dapat mengungkapkan mekanisme perilaku dan kendala pada perubahan perilaku (Hassani et al., 2020; Mylläri et al., 2022).

1. **_Linguistic Feature Engineering_**

    _Linguistic feature engineering_ melibatkan ekstraksi fitur statistika dan struktural dari data teks yang mendukung prediksi _fatigue_. Fitur-fitur seperti pengukuran panjang teks, frekuensi kata, dan indikator intensitas emosional dapat diekstrak tanpa memerlukan pemrosesan bahasa alami yang kompleks. Pendekatan yang digunakan menggabungkan _keyword_ _counting_ dengan _linguistic_ _statistics_ seperti _total words_, _unique words_, _title length_, dan _emotional indicators_. Kombinasi features ini memberikan representasi _comprehensive_ dari pola perilaku yang terekam dalam deskripsi aktivitas (A. Gupta et al., 2020; Hassani et al., 2020). _Title-only analysis_ dikembangkan sebagai pendekatan alternatif yang praktis dan dapat diimplementasikan dengan mudah tanpa memerlukan sensor yang kompleks (Jadhav et al., 2023).

# <a name="_toc204135582"></a>**BAB III**

# <a name="_toc204135583"></a>**METODOLOGI PENELITIAN**

1. ## <a name="_toc204135584"></a>**Metodologi Penelitian**

    Penelitian ini menggunakan desain kuantitatif dengan pendekatan _cross-sectional_ yang diperkuat dengan elemen longitudinal untuk menganalisis pola aktivitas mahasiswa dalam periode waktu tertentu. Pendekatan observational study diterapkan untuk mengumpulkan data _behavioral_ dari aktivitas sehari-hari mahasiswa tanpa melakukan intervensi langsung terhadap subjek penelitian. Pendekatan ini sangat relevan dengan temuan yang menunjukkan bahwa pola perilaku mahasiswa dapat berhubungan dengan pencapaian akademik (Azhary et al., 2020).

    Model klasifikasi risiko _fatigue_ ini memanfaatkan pendekatan _supervised_ _learning_ yang dapat digunakan untuk menganalisis data kompleks mengenai pola aktivitas kardiovaskular dan produktivitas akademik mahasiswa (Y. Zhang & Li, 2025). Penelitian ini mengklasifikasikan mahasiswa ke dalam kategori risiko _fatigue_ (_low_, _medium_, high) berdasarkan _behavioral patterns_ yang terekam dalam data digital.

    Pendekatan _mixed-methods_ juga diterapkan melalui integrasi analisis kuantitatif (data metrik aktivitas dan produktivitas) dengan analisis kualitatif (_text-based feature extraction_). Kombinasi ini memberikan perspektif yang lebih komprehensif dalam memahami fenomena _fatigue_ pada mahasiswa dengan memanfaatkan kekuatan dari kedua pendekatan metodologi (Lin et al., 2023).

    ![](Aspose.Words.3f3c9ce9-f0d4-4076-91a9-ec2271affa18.002.png)

*Gambar 3. *1* Diagram Alur Metodologi Penelitian*

Gambar 3.1 menunjukkan alur dalam proses penelitian ini, dengan urutan dari _Data Collection, Data Preprocessing, Feature Engeneering, Labeling Strategy, Feature Selection, Training Model, Evaluation Model, dan Results & Analysis._

1. ## <a name="_toc204135585"></a>**Tahapan Diagram Metodologi Penelitian**
1. **_Data Collection_**

    Platform Strava digunakan sebagai instrumen utama untuk mengumpulkan data aktivitas kardiovaskular mahasiswa. Strava merupakan aplikasi _fitness tracking_ yang memiliki akurasi tinggi dalam _recording_ aktivitas fisik menggunakan GPS dan _sensor smartphone_ atau _wearable devices_. Data yang dikumpulkan meliputi jenis aktivitas, durasi, jarak, kecepatan, dan deskripsi aktivitas.

    Platform Pomokit digunakan untuk mengumpulkan data produktivitas akademik mahasiswa melalui implementasi teknik Pomodoro. Aplikasi ini merekam jumlah siklus kerja yang diselesaikan dan deskripsi aktivitas produktif yang dilakukan mahasiswa.

    Pengumpulan data dilakukan selama 13 minggu dengan memanfaatkan infrastruktur do.my.id. Untuk data aktivitas kardiovaskular diambil dengan melakukan _scraping_ data pada platform strava dengan menggunakan _WhatsApp bot_, sedangkan untuk data produktivitas akademik diambil dengan memanfaatkan aplikasi Pomokit dengan _base on terminal_.

1. **_Data Preprocessing_**

    Tahapan kedua dalam alur metodologi adalah data _preprocessing_ yang dimulai dengan data _cleaning_ untuk menangani _missing values_, _outliers_, dan _inconsistencies_. _Missing values_ ditangani menggunakan kombinasi _deletion_ (untuk missing yang ekstensif) dan _imputation_ (untuk missing yang minimal) berdasarkan karakteristik data dan _pattern_ _missingness_.

    _Outlier detection_ dilakukan menggunakan statistical methods (IQR, Z-score) dan domain knowledge untuk mengidentifikasi nilai yang tidak realistis. Validation rules diterapkan untuk memastikan logical consistency, seperti durasi aktivitas yang reasonable dan konsistensi temporal antar variabel.

    Integrasi data dari kedua platform dilakukan melalui temporal alignment berdasarkan timestamp dan user identification. Harmonization process mencakup standardization of data formats, unit conversion, dan creation of unified feature space. Data dari Strava dan Pomokit digabungkan berdasarkan weekly aggregation untuk memastikan konsistensi temporal.

    Quality assurance dilakukan melalui cross-validation antara data dari kedua platform untuk mengidentifikasi inconsistencies. Proses ini menghasilkan dataset terintegrasi yang siap untuk tahapan feature engineering selanjutnya.

    Standardisasi format data dilakukan untuk memastikan konsistensi dalam representasi data. Ini meliputi konversi format waktu dari "41m 21s" menjadi minutes, standardisasi nama kolom dengan lowercase dan underscore, serta konversi tipe data yang sesuai (datetime, numeric, string). Proses standardisasi juga mencakup handling special characters, normalisasi text fields, dan creation of consistent user identities untuk anonymization purposes.

1. **_Feature Engineering_**

    Tahapan ketiga dalam alur metodologi adalah feature engineering yang dimulai dengan pembuatan weekly aggregation features dari data harian. Untuk data Strava, features yang dibuat meliputi total_distance_km, avg_distance_km, total_time_minutes, avg_time_minutes, dan activity_days. Untuk data Pomokit, features meliputi total_cycles, avg_cycles, dan work_days.

    Feature extraction dari teks dilakukan pada judul aktivitas dari kedua platform menggunakan rule-based keyword matching approach. Basic text processing meliputi lowercasing, basic cleaning, dan string standardization untuk memastikan konsistensi dalam keyword matching.

    Features yang diekstrak meliputi structural features (strava_title_count, strava_title_length, strava_unique_words) dan semantic features (stress_count, workload_count, negative_emotion_count). Dictionary-based extraction mengidentifikasi keywords yang berkaitan dengan fatigue indicators berdasarkan predefined keyword lists yang telah dikurasi berdasarkan domain knowledge.

    Gamification features dibuat untuk menangkap aspek motivational dan achievement-oriented dari aktivitas mahasiswa. Features ini dirancang berdasarkan gamification theory yang menunjukkan bahwa point-based systems dan achievement metrics dapat mempengaruhi behavioral patterns dan psychological states.

    Activity points dihitung berdasarkan target jarak mingguan 6 km, yang merupakan rekomendasi minimum aktivitas fisik untuk mahasiswa. Formula yang digunakan adalah (total_distance_km / 6) × 100 dengan maximum cap 100 points untuk mencegah outlier effects. Points ini mencerminkan seberapa baik mahasiswa memenuhi target aktivitas fisik mingguan.

    Productivity points dihitung berdasarkan target 5 siklus kerja per minggu, yang setara dengan sekitar 2.5 jam focused work time. Formula yang digunakan adalah (total_cycles / 5) × 100 dengan maximum cap 100 points. Points ini mencerminkan konsistensi dan volume dalam kegiatan produktif akademik.

    Achievement rate dihitung sebagai persentase pencapaian dari maksimal possible points, memberikan normalized measure yang memungkinkan comparison across individuals dengan baseline yang berbeda. Gamification balance dihitung sebagai rasio antara activity points dan productivity points, memberikan insight tentang balance antara aktivitas fisik dan akademik.

    Derived metrics juga dihitung untuk menangkap consistency patterns. Consistency score dihitung berdasarkan regularity dalam aktivitas fisik dan produktivitas, dengan formula yang menggabungkan physical consistency (activity_days/7) dan work consistency (work_days/5). Weekly efficiency dihitung sebagai rasio total cycles terhadap work days, memberikan measure tentang produktivitas per hari kerja.

1. **_Labeling Strategy_**

    Tahapan keempat dalam alur metodologi adalah labeling strategy yang menggunakan pendekatan independent external labeling untuk mencegah data leakage. Labeling dilakukan menggunakan temporal patterns analysis, expert simulation, dan domain knowledge rules yang independen dari input features. Rata-rata feature yang digunakan untuk labeling adalah feature yang dihasilkan dari Basic text processing terhadap feature title dari kedua dataset.

    Tahap ini menghasilkan feature untuk label klasifikasi fatigue_risk yang memiliki 3 value atau tingkatan, yaitu low_risk, medium_risk, dan high_risk.

1. **_Feature Selection_**

    Tahapan kelima adalah feature selection menggunakan SHAP (SHapley Additive exPlanations) analysis untuk menentukan subset fitur yang optimal. SHAP merupakan metode yang digunakan untuk mengidentifikasi feature importance berdasarkan kontribusi masing-masing fitur terhadap prediksi model. Implementasi SHAP dilakukan dengan membuat explainer dari model, menghitung SHAP values, dan mengembalikan dictionary berisi feature importance berdasarkan nilai absolut rata-rata SHAP.

    SHAP digunakan dengan mengkombinasikan dengan algoritma seperti Logistik Regression, Random Forest, Gradient Boosting, dan XGBoost. Setiap algoritma memberikan perspektif yang berbeda dalam menilai kepentingan fitur. Logistic Regression memberikan linear importance berdasarkan koefisien, Random Forest menggunakan impurity-based importance, Gradient Boosting menghitung gain-based importance. Kombinasi keempat algoritma ini memberikan robust evaluation terhadap kepentingan fitur.

    Untuk mencegah data leakage, diterapkan feature filtering yang mendefinisikan dua set fitur: label_creation_features yang berisi fitur-fitur yang digunakan untuk membuat label dan tidak boleh digunakan dalam model, serta safe_model_features yang berisi fitur-fitur yang aman digunakan dalam model machine learning.

1. **_Training Model_**

    Tahap training model menggunakan empat algoritma machine learning yang berbeda untuk membandingkan performa dalam prediksi risiko fatigue. Algoritma pertama adalah Logistic Regression yang dikonfigurasi dengan random_state=42 untuk reproducibility, max_iter=1000 untuk memastikan konvergensi.

    Algoritma kedua adalah Random Forest yang dikonfigurasi dengan 100 estimators, random_state=42, dan max_depth=10 untuk mencegah overfitting sambil mempertahankan kemampuan prediksi yang baik.

    Algoritma ketiga adalah Gradient Boosting yang dikonfigurasi dengan 100 estimators, random_state=42, untuk mengoptimalkan trade-off antara bias dan variance.

    Algoritma keempat adalah XGBoost yang dikonfigurasi dengan parameter serupa dengan Gradient Boosting namun dengan tambahan eval_metric='mlogloss' untuk optimasi yang lebih baik pada masalah klasifikasi multi-kelas.

    Proses optimasi hyperparameter dilakukan menggunakan GridSearchCV dengan 5-fold cross-validation untuk memastikan generalisasi yang baik. Parameter grid didefinisikan untuk Random Forest yang mencakup variasi n_estimators (50, 100, 200), max_depth (5, 10, 15, None), min_samples_split (2, 5, 10), dan min_samples_leaf (1, 2, 4). Untuk Gradient Boosting, parameter grid mencakup variasi n_estimators (50, 100, 200), learning_rate (0.01, 0.1, 0.2), max_depth (3, 6, 9), dan subsample (0.8, 0.9, 1.0).

1. **_Evaluation Model_**

    Tahap evaluasi model menggunakan strategi Stratified K-Fold Cross-Validation dengan k=5 untuk mempertahankan distribusi kelas dalam setiap fold, yang penting mengingat adanya ketidakseimbangan dalam distribusi kategori risiko fatigue. Implementasi cross-validation dilakukan dengan menggunakan objek StratifiedKFold dengan parameter n_splits=5, shuffle=True untuk randomisasi data, dan random_state=42 untuk reproducibility. Fungsi cross_validate digunakan untuk mengevaluasi model dengan berbagai metrik sekaligus dan menyimpan skor train dan validation untuk analisis lebih lanjut.

    Evaluasi model menggunakan metrik klasifikasi standar yang mencakup Accuracy untuk mengukur proporsi prediksi yang benar secara keseluruhan, F1-Score (Macro) sebagai harmonic mean dari precision dan recall yang dirata-ratakan untuk semua kelas, Precision (Macro) untuk mengukur rata-rata precision per kelas, dan Recall (Macro) untuk mengukur rata-rata recall per kelas. Evaluasi tambahan meliputi Confusion Matrix untuk memberikan detail klasifikasi per kelas dan Classification Report untuk menyajikan laporan lengkap performa per kelas.

    Untuk mendeteksi overfitting, dilakukan Train-Validation Gap Analysis yang membandingkan performa model pada data training dan validation. Selisih antara rata-rata skor training dan validation dihitung, kemudian mengkategorikan tingkat overfitting berdasarkan besarnya gap.

1. **_Results & Analysis_**

    Tahap hasil dan analisis merupakan tahap akhir dalam metodologi penelitian yang melibatkan interpretasi komprehensif dari output model machine learning. Analisis hasil mencakup interpretasi performa model terbaik berdasarkan multiple metrics seperti accuracy, F1-score, precision, dan recall, yang memberikan gambaran menyeluruh tentang kemampuan prediksi model. Selain itu, dilakukan analisis feature importance dan kontribusi setiap variabel menggunakan SHAP untuk memahami faktor-faktor yang paling berpengaruh dalam prediksi risiko fatigue. Validasi hipotesis penelitian dilakukan melalui statistical testing untuk memastikan signifikansi temuan, dan identifikasi pattern serta insights dari data dilakukan untuk aplikasi praktis dalam manajemen fatigue mahasiswa.

    Visualisasi dan reporting merupakan komponen penting dalam tahap ini, yang mencakup pembuatan confusion matrix dan ROC curves untuk evaluasi klasifikasi secara visual, feature importance plots untuk meningkatkan interpretability model, performance comparison charts untuk membandingkan performa antar algoritma, time series analysis untuk mengidentifikasi pola temporal aktivitas, serta comprehensive research report yang memenuhi academic standards.

    Output penelitian yang dihasilkan meliputi model terbaik dengan akurasi optimal untuk prediksi fatigue, feature ranking berdasarkan importance scores dari SHAP analysis yang menunjukkan kontribusi relatif setiap fitur, insights praktis untuk aplikasi real-world dalam student wellness, serta rekomendasi untuk penelitian lanjutan dan implementasi sistem prediksi fatigue.

# <a name="_toc204135586"></a>**BAB IV**

# <a name="_toc204135587"></a>**EKSPERIMEN DAN HASIL**

1. ## <a name="_toc204135588"></a>**Eksperimen**
1. **Implementasi Model**

    Empat algoritma machine learning diimplementasikan dan dibandingkan dalam eksperimen ini: Logistic Regression, Random Forest, Gradient Boosting, dan XGBoost. Setiap model dikonfigurasi dengan parameter yang telah dioptimalkan melalui grid search dengan 5-fold cross-validation.

    Strategi konfigurasi model dirancang untuk mengatasi tantangan spesifik dalam dataset bias-corrected fatigue risk dan memastikan performa optimal. Mengingat distribusi kelas yang tidak seimbang (high_risk hanya 7.9%), class balancing diterapkan secara otomatis oleh algoritma atau melalui stratified sampling. Reproducibility dijamin dengan menetapkan `random\_state=42` untuk semua model, memastikan hasil yang konsisten dan dapat direplikasi. Model dikonfigurasi dengan parameter default yang telah terbukti efektif untuk menghindari overfitting pada dataset berukuran sedang (291 samples).

    ![](Aspose.Words.3f3c9ce9-f0d4-4076-91a9-ec2271affa18.003.png)

*Gambar 4. *1* Model implementations*

1. **SHAP-based Feature Selection**

    Eksperimen feature selection dilakukan menggunakan SHAP (SHapley Additive exPlanations) untuk mengidentifikasi subset fitur optimal yang memberikan performa prediksi terbaik. SHAP dipilih sebagai metode utama karena kemampuannya memberikan interpretabilitas yang komprehensif dan theoretically grounded.

    SHAP dipilih sebagai metode feature selection karena memiliki beberapa keunggulan signifikan dibandingkan metode tradisional. Metode ini menyediakan individual prediction explanations dengan interpretabilitas tinggi, memungkinkan pemahaman mendalam tentang kontribusi setiap fitur terhadap prediksi spesifik. SHAP mampu melakukan feature interaction detection untuk memahami hubungan kompleks antar fitur yang mungkin tidak terdeteksi oleh metode lain. Keunggulan lainnya adalah kemampuan global dan local interpretability yang komprehensif, memberikan insights baik pada level dataset maupun prediksi individual. Secara teoritis, SHAP memiliki fondasi yang kuat berdasarkan game theory melalui Shapley values, memberikan justifikasi matematis yang solid. Metode ini juga bersifat model-agnostic, dapat diterapkan pada semua algoritma machine learning, dan menyediakan positive/negative contribution analysis yang detail untuk setiap fitur.

    ![](Aspose.Words.3f3c9ce9-f0d4-4076-91a9-ec2271affa18.004.png)

    *Gambar 4. *2* Performa SHAP Features vs Random Features*

Gambar 4.2 menunjukkan perbandingan performa antara fitur yang dipilih berdasarkan SHAP importance dengan fitur yang dipilih secara random. Grafik menampilkan akurasi model untuk berbagai jumlah fitur (5, 10, 15) menggunakan keempat algoritma. SHAP-selected features secara konsisten menunjukkan performa yang superior dibandingkan random features across semua algoritma dan jumlah fitur. Performa terlihat pada Logistic Regression, Random Forest, Gradient Boosting, dan XGBoost, SHAP features mencapai akurasi ~70% hasil ini memvalidasi bahwa SHAP berhasil mengidentifikasi fitur-fitur yang benar-benar informatif untuk prediksi risiko fatigue.

1. **Cross-Validation Strategy**

    Evaluasi model dilakukan menggunakan stratified 5-fold cross-validation untuk memastikan generalisasi yang baik dan menghindari overfitting. Strategi ini mempertahankan distribusi kelas dalam setiap fold, yang penting mengingat ketidakseimbangan dalam dataset.

![](Aspose.Words.3f3c9ce9-f0d4-4076-91a9-ec2271affa18.005.png)

*Gambar 4. *3* K-Fold Cross-Validation Performance untuk Semua Model*

Gambar 4.3 menampilkan grafik performa model pada skenario K-Fold Cross-Validation dengan variasi nilai k dari 2 hingga 20, untuk empat algoritma machine learning: Logistic Regression, Random Forest, Gradient Boosting, dan XGBoost. Setiap subplot menggambarkan rata-rata akurasi untuk data training dan validation, lengkap dengan error bars yang merepresentasikan standar deviasi akurasi antar fold.

Logistic Regression (atas kiri) menunjukkan akurasi validasi berkisar antara 0.65 hingga 0.75, dengan variasi yang cukup besar antar fold (error bars lebar). Median akurasi validasi relatif stabil, namun performa paling optimal tercapai saat k=2. Model ini juga menunjukkan gap yang kecil antara training dan validation, namun performanya masih yang terendah dibanding model lain.

Random Forest (atas kanan) menunjukkan akurasi training yang sangat tinggi (mendekati 1.0) pada semua nilai k, yang menandakan potensi overfitting. Namun, akurasi validasi juga cukup tinggi dan stabil di kisaran 0.71–0.75. Error bars pada validation lebih pendek dibanding Logistic Regression, menunjukkan stabilitas yang lebih baik. Nilai optimal dipilih pada k=2.

Gradient Boosting (bawah kiri) menghasilkan akurasi training yang hampir sempurna (mendekati 1.0), dengan validasi di kisaran 0.67–0.74. Error bars untuk validation cukup lebar, yang menandakan variasi performa antar fold masih signifikan. Nilai k=4 dipilih sebagai nilai optimal, kemungkinan sebagai trade-off antara bias dan varians.

XGBoost (bawah kanan) juga menunjukkan akurasi training yang sempurna pada semua k, mirip seperti Gradient Boosting. Akurasi validasi cenderung stabil di kisaran 0.68–0.74, dengan fluktuasi antar fold yang cukup besar. Nilai optimal ditentukan pada k=2.

Secara keseluruhan, Random Forest tetap menonjol dengan performa validasi yang paling stabil dan akurat, meskipun risiko overfitting perlu diperhatikan. Logistic Regression memiliki performa paling rendah dan tidak stabil, sementara Gradient Boosting dan XGBoost berada di antara keduanya dengan akurasi tinggi namun variasi antar fold lebih besar.

1. ## <a name="_toc204135589"></a>**Hasil**
1. **Performa Model**

    Berdasarkan comprehensive feature validation report, hasil evaluasi performa keempat model machine learning menunjukkan bahwa Random Forest (RF) mencapai performa terbaik dengan akurasi 95.20% ± 0.0349 menggunakan hanya 3 fitur teratas (Top 3 Consensus). Gradient Boosting (GB) dan XGBoost (XGB) menunjukkan performa yang sangat baik dengan akurasi 94.51% ± 0.0313, sementara Logistic Regression (LR) menunjukkan performa yang jauh lebih rendah dengan akurasi 60.85% ± 0.0632.

*Tabel 4. *1* Performa Model Menggunakan SHAP*

| **Model** | **Accuracy** | **F1-Score** | **CV Accuracy**  | **CV F1-Score**  |
| :-------: | :----------: | :----------: | :--------------: | :--------------: |
|  **RF**   |   0\.6949    |   0\.6952    | 0\.6464 ± 0.0190 | 0\.6413 ± 0.0209 |
|  **GB**   |   0\.6441    |   0\.6465    | 0\.6810 ± 0.0532 | 0\.6692 ± 0.0509 |
|  **XGB**  |   0\.7966    |   0\.7954    | 0\.6676 ± 0.0642 | 0\.6616 ± 0.0638 |
|  **LR**   |   0\.7119    |   0\.7123    | 0\.6935 ± 0.0446 | 0\.6809 ± 0.0545 |

Hasil menunjukkan bahwa XGBoost mencapai performa terbaik pada test set (79.66%), namun memiliki gap yang signifikan dengan cross-validation performance (66.76%), mengindikasikan potensi overfitting. Logistic Regression menunjukkan konsistensi terbaik antara test dan CV performance dengan gap yang minimal, menunjukkan generalisasi yang lebih baik. Random Forest dan Gradient Boosting menunjukkan performa yang moderat dengan tanda-tanda overfitting yang jelas.

XGBoost menunjukkan performa test terbaik (79.66%) namun dengan gap signifikan terhadap CV performance (66.76%), mengindikasikan overfitting. Logistic Regression menunjukkan konsistensi terbaik dengan gap minimal antara test (71.19%) dan CV (69.35%), menunjukkan generalisasi yang superior. Random Forest dan Gradient Boosting menunjukkan performa yang tidak konsisten dengan tanda-tanda overfitting yang jelas.

Berdasarkan analisis overfitting, Logistic Regression memiliki stabilitas tertinggi dengan overfitting score terendah (9.23) dan train-validation gap minimal (1.71%). Model tree-based menunjukkan overfitting score tinggi (>21) dengan train-validation gap >27%, mengindikasikan kompleksitas berlebihan untuk dataset berukuran 291 samples.

1. **SHAP Feature Importance Analysis**

    Berdasarkan SHAP analysis yang komprehensif, analisis feature importance menunjukkan konsistensi yang luar biasa across semua algoritma. Hasil menunjukkan bahwa fitur-fitur yang berkaitan dengan diversitas dan kompleksitas linguistik dalam judul aktivitas menjadi prediktor terkuat untuk risiko fatigue. Lima fitur teratas yang berkontribusi paling signifikan terhadap prediksi risiko fatigue adalah:

1. pomokit_unique_words (puw) (5.43-5.54% kontribusi) - Jumlah kata unik dalam judul aktivitas produktivitas
1. total_title_diversity (ttd) (5.30-5.37% kontribusi) - Total diversitas judul across semua aktivitas
1. title_balance_ratio (tbr) (5.13-5.22% kontribusi) - Rasio keseimbangan antara judul produktivitas dan aktivitas fisik
1. avg_time_minutes (atm) (4.70-4.76% kontribusi) - Rata-rata waktu per sesi aktivitas
1. total_time_minutes (ttm) (3.99-4.05% kontribusi) - Total waktu aktivitas mingguan

*Tabel 4. *2* SHAP Feature Importance*

| **Feature** | **LR** | **RF** | **GB** | **XGB** | **Average** |
| :---------: | :----: | :----: | :----: | :-----: | :---------: |
|     puw     | 5\.43% | 5\.54% | 5\.53% | 5\.54%  |   5\.51%    |
|     ttd     | 5\.30% | 5\.37% | 5\.36% | 5\.30%  |   5\.33%    |
|     tbr     | 5\.13% | 5\.22% | 5\.21% | 5\.18%  |   5\.19%    |
|     atm     | 4\.70% | 5\.76% | 5\.73% | 5\.72%  |   4\.73%    |
|     ttm     | 4\.00% | 4\.05% | 4\.04% | 3\.99%  |   4\.02%    |

1. Pomokit Unique Words (5.43-5.54% kontribusi)

    Fitur ini merupakan prediktor terkuat risiko fatigue, mencerminkan diversitas linguistik dalam cara mahasiswa mendeskripsikan aktivitas produktivitas mereka. Kontribusi yang konsisten (~5.5%) across semua algoritma mengindikasikan bahwa kompleksitas dan variasi dalam deskripsi aktivitas mencerminkan pola kognitif yang berkaitan dengan fatigue. Mahasiswa yang menggunakan vocabulary yang lebih beragam dalam mendeskripsikan aktivitas cenderung memiliki pola fatigue yang berbeda, kemungkinan karena tingkat engagement dan refleksi yang lebih tinggi terhadap aktivitas mereka.

1. Total Title Diversity (5.30-5.37% kontribusi)

    Diversitas total judul across semua aktivitas menunjukkan variasi dalam cara mahasiswa mengkategorisasi dan mendeskripsikan berbagai jenis aktivitas. Kontribusi sebesar ~5.3% mengindikasikan bahwa mahasiswa dengan diversitas deskripsi yang tinggi memiliki pola fatigue yang dapat diprediksi. Hal ini mungkin mencerminkan tingkat kesadaran (mindfulness) dan refleksi yang lebih tinggi terhadap aktivitas, yang berkorelasi dengan self-awareness terhadap kondisi fatigue.

1. Title Balance Ratio (5.13-5.22% kontribusi)

    Rasio keseimbangan antara judul aktivitas produktivitas dan fisik menunjukkan bagaimana mahasiswa mendistribusikan perhatian dan deskripsi mereka across different types of activities. Kontribusi ~5.2% mengindikasikan bahwa balance dalam cara mendeskripsikan aktivitas berbeda berkorelasi dengan risiko fatigue. Mahasiswa dengan balance yang baik mungkin memiliki awareness yang lebih baik terhadap work-life balance mereka.

1. Average Time Minutes (4.70-4.76% kontribusi)

    Rata-rata durasi per sesi aktivitas mencerminkan pola temporal dalam aktivitas mahasiswa. Kontribusi ~4.7% menunjukkan bahwa durasi sesi aktivitas berkorelasi dengan risiko fatigue, kemungkinan karena mahasiswa dengan sesi yang lebih panjang atau lebih pendek memiliki pola fatigue yang berbeda. Konsistensi across algoritma menunjukkan robustness temuan ini.

1. Total Time Minutes (3.99-4.05% kontribusi)

    Total waktu aktivitas mingguan menunjukkan volume temporal aktivitas dengan kontribusi ~4.0%. Meskipun kontribusinya lebih rendah dari fitur linguistik, konsistensi across algoritma menunjukkan bahwa volume waktu aktivitas memiliki hubungan yang stabil dengan fatigue, kemungkinan melalui mekanisme workload dan time management.

1. **SHAP-based Feature Selection Effectiveness**

*Tabel 4. *3* SHAP vs Random Features Comparison*

| **Model** | **SHAP Feature** | **Random Feature** | **SHAP Advantage** | **Best Scenario**  |
| :-------: | :--------------: | :----------------: | :----------------: | :----------------: |
|  **LR**   |     70\.11%      |      69\.94%       |       +0.17%       | Random 10 Features |
|  **RF**   |     69\.71%      |      68\.72%       |       +0.98%       | Consensus Features |
|  **GB**   |     68\.57%      |      66\.66%       |       +1.91%       |   Top 10 XGBoost   |
|  **XGB**  |     67\.94%      |      69\.42%       |       -1.48%       |   Top 10 XGBoost   |

Hasil menunjukkan bahwa SHAP-based feature selection memberikan keuntungan yang konsisten untuk sebagian besar model, dengan Gradient Boosting menunjukkan keuntungan terbesar (+1.91%). Random Forest dan Logistic Regression juga menunjukkan peningkatan performa dengan SHAP features, meskipun XGBoost menunjukkan performa yang sedikit lebih rendah (-1.48%). Secara keseluruhan, SHAP features terbukti lebih informatif dibandingkan random feature selection, memvalidasi efektivitas pendekatan SHAP dalam mengidentifikasi fitur yang benar-benar berkontribusi terhadap prediksi fatigue risk.

1. **K-Fold Cross-Validation Analysis**

    Berdasarkan K-Fold analysis report, evaluasi stabilitas model menggunakan cross-validation dengan berbagai nilai k (2-5) menunjukkan bahwa Logistic Regression memiliki risiko overfitting terendah, sementara model tree-based menunjukkan risiko overfitting yang tinggi pada dataset berukuran sedang ini.

    ![](Aspose.Words.3f3c9ce9-f0d4-4076-91a9-ec2271affa18.006.png)

*Gambar 4. *4* Analisis Overfitting dan Train-Validation Gap*

Hasil analisis menunjukkan bahwa Logistic Regression adalah model yang paling stabil dengan overfitting score terendah (9.2) dan train-validation gap yang minimal (0.068). Model tree-based (Random Forest, Gradient Boosting, XGBoost) menunjukkan tanda-tanda overfitting yang signifikan dengan train-validation gap >32%, mengindikasikan bahwa model-model ini terlalu kompleks untuk dataset berukuran 291 samples. Hal ini menjelaskan mengapa performa test set lebih rendah dari yang diharapkan pada model tree-based.

1. **Validasi Model dan Generalisasi**

    Berdasarkan comprehensive validation report, hasil eksperimen menunjukkan bahwa XGBoost memberikan performa terbaik pada test set dengan akurasi 79.66%, meskipun model tree-based lainnya menunjukkan tanda-tanda overfitting. Logistic Regression menunjukkan stabilitas terbaik dengan risiko overfitting terendah.

*Tabel 4. *4* Model Validation Summary*

| **Model** | **Test Performance** | **CV Performance** |
| :-------: | :------------------: | :----------------: |
|  **LR**   |       71\.19%        |      69\.35%       |
|  **RF**   |       69\.49%        |      64\.64%       |
|  **GB**   |       64\.41%        |      68\.10%       |
|  **XGB**  |       79\.66%        |      66\.76%       |

XGBoost mencapai test accuracy tertinggi (79.66%) dan merupakan pilihan optimal untuk implementasi praktis ketika akurasi prediksi adalah prioritas utama.

Logistic Regression menunjukkan konsistensi terbaik antara training dan validation performance dengan risiko overfitting terendah, menjadikannya pilihan yang reliable untuk deployment jangka panjang.

Analisis SHAP mengungkap bahwa fitur-fitur yang berkaitan dengan diversitas linguistik dalam judul aktivitas (pomokit_unique_word, total_title_diversity, title_balance_ratio) adalah prediktor terkuat untuk risiko fatigue, menunjukkan bahwa kompleksitas dan variasi dalam deskripsi aktivitas mencerminkan pola perilaku yang berkaitan dengan fatigue.

Sistem prediksi fatigue dapat fokus pada monitoring diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mereka, yang dapat menjadi indikator early warning untuk risiko fatigue yang meningkat.

# <a name="_toc204135590"></a>**BAB V**

# <a name="_toc204135591"></a>**KESIMPULAN**

1. ## <a name="_toc204135592"></a>**Kesimpulan Masalah**

    Penelitian ini berhasil menjawab pertanyaan penelitian pertama mengenai "Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa, dan bagaimana kontribusi individual setiap fitur dapat divalidasi melalui ablation study" melalui analisis SHAP yang komprehensif.

    Berdasarkan SHAP ablation study, faktor-faktor yang paling signifikan dalam memprediksi risiko fatigue adalah fitur-fitur linguistik: pomokit_unique_words (5.54%), total_title_diversity (5.33%), dan title_balance_ratio (5.19%). Temuan mengejutkan bahwa fitur linguistik lebih dominan dibandingkan fitur temporal seperti avg_time_minutes (4.73%) dan total_time_minutes (4.02%), mengindikasikan bahwa cara mahasiswa mendeskripsikan aktivitas mengandung informasi yang lebih kaya tentang kondisi psikologis dan kognitif yang berkaitan dengan fatigue.

    Kontribusi individual setiap fitur berhasil divalidasi melalui systematic ablation study dengan konsistensi 100% across semua algoritma dan variance yang sangat rendah (0.0006-0.0011). SHAP-based feature selection terbukti superior dibandingkan random selection dengan improvement konsisten pada sebagian besar algoritma. Dominasi fitur linguistik mengungkap bahwa diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mencerminkan pola kognitif yang berkaitan dengan kondisi fatigue, memberikan implikasi praktis untuk pengembangan sistem monitoring yang fokus pada analisis pola linguistik.

1. ## <a name="_toc204135593"></a>**Kesimpulan Metode**

    Penelitian ini berhasil menjawab pertanyaan penelitian kedua mengenai "Bagaimana efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas" melalui evaluasi komprehensif menggunakan dual evaluation strategy.

    Efektivitas model machine learning menunjukkan hasil yang realistis dengan XGBoost mencapai performa terbaik (test accuracy 79.66%, F1-score 79.54%) namun menunjukkan risiko overfitting tinggi (CV accuracy 66.76%, overfitting score 22.01). Logistic Regression menunjukkan stabilitas superior dengan konsistensi antara test accuracy (71.19%) dan CV performance (69.35%) serta overfitting score terendah (9.23), menjadikannya pilihan yang lebih reliable untuk deployment jangka panjang.

    Evaluasi mengungkap bahwa untuk dataset berukuran sedang (291 samples) dengan distribusi kelas tidak seimbang, model linear memberikan generalisasi yang lebih baik dibandingkan model kompleks. Model tree-based menunjukkan overfitting signifikan dengan overfitting score >21 dan train-validation gap >27%. Dual evaluation strategy terbukti efektif dalam mengidentifikasi model yang truly effective, dengan perbedaan signifikan antara train-test split (79.66%) dan cross-validation (66.76%) mengkonfirmasi pentingnya evaluasi komprehensif.

    Penelitian ini mengembangkan metodologi komprehensif meliputi Bias Correction Framework, SHAP-based feature selection untuk interpretabilitas superior, dan multi-modal data integration yang menghasilkan 20 fitur informatif dari berbagai modalitas data aktivitas mahasiswa.

1. ## <a name="_toc204135594"></a>**Kesimpulan Eksperimen**

    Penelitian ini berhasil menjawab pertanyaan penelitian ketiga mengenai "Apakah analisis berbasis judul aktivitas (title-only analysis) dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap" dengan hasil yang positif.

    Analisis berbasis judul aktivitas terbukti dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap. Fitur-fitur linguistik yang diekstrak dari judul aktivitas (pomokit_unique_words, total_title_diversity, title_balance_ratio) mendominasi ranking feature importance dengan kontribusi total 15.06%, lebih tinggi dibandingkan fitur kuantitatif lainnya. Hal ini memvalidasi bahwa informasi dalam judul aktivitas sudah cukup untuk prediksi fatigue yang akurat.

    Title-only analysis memiliki keunggulan praktis karena lebih sederhana, less intrusive, dan mudah diimplementasikan dalam sistem monitoring yang existing. Pendekatan ini juga memberikan insights tentang aspek psikologis mahasiswa melalui pola linguistik dalam deskripsi aktivitas yang berkorelasi dengan kondisi fatigue.

    Penelitian ini memberikan kontribusi signifikan berupa identifikasi fitur linguistik sebagai prediktor utama fatigue, pengembangan framework bias correction dan dual evaluation strategy, serta rekomendasi praktis untuk implementasi sistem monitoring fatigue yang efektif dan dapat diandalkan.

    Secara keseluruhan, penelitian ini berhasil mengembangkan metodologi yang robust untuk prediksi risiko fatigue mahasiswa dengan akurasi yang realistis dan interpretabilitas yang tinggi, memberikan foundation yang solid untuk pengembangan sistem monitoring kesehatan mahasiswa yang lebih efektif dan dapat diimplementasikan dalam praktik.

# <a name="_toc204135595"></a>**BAB VI**

# <a name="_toc204135596"></a>**SARAN**

Berdasarkan temuan dan keterbatasan dalam penelitian eksploratif ini, peneliti merekomendasikan beberapa arah pengembangan yang dapat dijadikan pertimbangan untuk studi lanjutan. Saran-saran berikut disusun sebagai referensi bertahap, baik untuk penelitian dengan cakupan terbatas maupun studi kolaboratif berskala lebih luas:

1. **Penambahan Data**

-   Menambah jumlah dataset primer dari responden baru.
-   Menggunakan teknik augmentasi data seperti SMOTE atau GAN untuk menghindari overfitting dan meningkatkan akurasi model.

1. **Perluasan Jenis Data**

-   Mengintegrasikan data lain seperti pola tidur, aktivitas media sosial, dan sensor dari smartphone.
-   Menganalisis pola fatigue berdasarkan waktu akademik, seperti masa ujian atau deadline tugas.

1. **Peningkatan Teknik Analisis**

-   Menerapkan teknik NLP lanjutan untuk mendeteksi emosi atau sentimen dari aktivitas mahasiswa.
-   Menggabungkan beberapa algoritma (ensemble) seperti stacking atau voting untuk hasil prediksi yang lebih stabil.

1. **Evaluasi dan Validasi Model**

-   Menambahkan metrik evaluasi seperti false positive rate dan kemampuan deteksi dini.
-   Melakukan validasi eksternal dengan data dari institusi lain untuk meningkatkan generalisasi.

Saran-saran ini diharapkan dapat memperkuat landasan penelitian lanjutan dalam membangun sistem prediksi fatigue yang lebih komprehensif dan bermanfaat di lingkungan akademik.

# <a name="_toc204135597"></a>**DAFTAR PUSTAKA**

Alale, A., Dinko, J. D., & Amponsah, S. (2020). _Evaluation of Students Utilisation/Implementation of Time Management Skills in University for Development Studies: The Case of Nyankpala Campus_. https://doi.org/10.7176/rhss/10-4-02

Azhary, S. A.-G., Supahar, S., Kuswanto, K., Ikhlas, M., & Devi, I. P. (2020). Relationship Between Behavior of Learning and Student Achievement in Physics Subject. _Jurnal Pendidikan Fisika Indonesia_, _16_(1), 1–8. https://doi.org/10.15294/jpfi.v16i1.23096

Chayati, N., Rejecky, A., Maulana, N., Rebon, R., Puspitowarno, P., Komariah, A., Rahmawati, B. A., Aliun, F. W., Ariani, F., Susanti, I., Sumarni, R., Riyanti, R., Utami, R. W., Rusydi, A. F. H., Jamaludin, T. S. S., Khoiriyati, A., & Rosa, E. M. (2023). _Health Education and Physical Exercise as a Means to Reduce Fatigue Level and Stress for Academician in the International Islamic University Malaysia_. _1_(2). https://doi.org/10.18196/iccs.v1i2.217

Gholami, M., Napier, C., Patiño, A. G., Cuthbert, T. J., & Menon, C. (2020). Fatigue Monitoring in Running Using Flexible Textile Wearable Sensors. _Sensors_, _20_(19), 5573. https://doi.org/10.3390/s20195573

Güneş, M., & Demirer, B. (2022). A Comparison of Caffeine Intake and Physical Activity According to Fatigue Severity in University Students. _Evaluation & the Health Professions_, _46_(1), 92–99. https://doi.org/10.1177/01632787221141504

Gupta, A., Dengre, V., Kheruwala, H. A., & Shah, M. (2020). Comprehensive Review of Text-Mining Applications in Finance. _Financial Innovation_, _6_(1). https://doi.org/10.1186/s40854-020-00205-1

Gupta, S. C., & Goyal, N. (2024). _Text Mining: Techniques, Applications and Issues_. https://doi.org/10.55524/csistw.2024.12.1.49

Hanif, S., Pinky, P., Ahmed, I., Nawaz, R., Shabbir, H., & Asif, K. (2024). _Association of Fatigue Level and Physical Activity Among University Students: A Cross-Sectional Survey_. _4_(1), 1642–1646. https://doi.org/10.61919/jhrr.v4i1.675

Hassani, H., Beneki, C., Unger, S., Mazinani, M. T., & Yeganegi, M. R. (2020). Text Mining in Big Data Analytics. _Big Data and Cognitive Computing_, _4_(1), 1. https://doi.org/10.3390/bdcc4010001

Igiri, B. E., Okoduwa, S. I. R., Akabuogu, E. P., Okoduwa, U. J., Enang, I. A., Idowu, O. O., Abdullahi, S., Onukak, I. E., Onuruka, C. C., Christopher, O. P. O., Salawu, A. O., Chris, A. O., & Onyemachi, D. I. (2021). Focused Research on the Challenges and Productivity of Researchers in Nigerian Academic Institutions Without Funding. _Frontiers in Research Metrics and Analytics_, _6_. https://doi.org/10.3389/frma.2021.727228

Jadhav, A. B., Jagtap, P., Gurav, S., Jadhav, S., Jadhav, N., & Akkalkot, A. (2023). A Survey on Text Mining - Techniques, Application. _International Journal of Scientific Research in Computer Science Engineering and Information Technology_, 338–343. https://doi.org/10.32628/cseit2390391

Lekkas, D., Klein, R. J., & Jacobson, N. C. (2021). Predicting acute suicidal ideation on Instagram using ensemble machine learning models. _Internet Interventions_, _25_. https://doi.org/10.1016/j.invent.2021.100424

Li, J., Liu, L., & Xiao, Y. (2022). _Investigating the Influence of Empowerment on Patients’ Satisfaction: How to Empower Patients in Online Health Consultation Platform_. https://doi.org/10.24251/hicss.2022.464

Lin, X., Yue, W. S., Zhou, W., Shen, W., Li, W., & Tsai, C. (2023). Undergraduate Students’ Profiles of Cognitive Load in Augmented Reality–Assisted Science Learning and Their Relation to Science Learning Self-Efficacy and Behavior Patterns. _International Journal of Science and Mathematics Education_, _22_(2), 419–445. https://doi.org/10.1007/s10763-023-10376-9

Lv, H., Zhang, T., Li, B., & Wang, R. (2024). The Influence of Control Beliefs on the Cardiovascular Fitness of College Students: The Chain Mediating Effect of Subjective Exercise Experience and Exercise Adherence. _BMC Public Health_, _24_(1). https://doi.org/10.1186/s12889-023-17509-3

Masrom, S., Jamaludin, N. F., Razak, F. A., & Ismail, N. R. P. (2023). Machine Learning Approach to Classify Students’ Mental Health During the COVID-19 Pandemic: A Web-Based Interactive Dashboard. _International Journal of Academic Research in Business and Social Sciences_, _13_(7). https://doi.org/10.6007/ijarbss/v13-i7/17124

Merhabi, M. A., Petridis, P., & Khusainova, R. (2021). Gamification for Brand Value Co-Creation: A Systematic Literature Review. _Information_, _12_(9), 345. https://doi.org/10.3390/info12090345

Moshawrab, M., Adda, M., Bouzouane, A., Ibrahim, H., & Raad, A. (2022). Smart Wearables for the Detection of Occupational Physical Fatigue: A Literature Review. _Sensors_, _22_(19), 7472. https://doi.org/10.3390/s22197472

Muhammad, K., Ghani, E. K., Ilias, A., Ali, M. M., Ismail, R. F., Rohayati, S., Susanti, S., & Bahtiar, Moh. D. (2023). Investigating the Effects of Individual and Institutional Factors on the Research Productivity of University Academics: A Comprehensive Analysis. _Nurture_, _17_(2), 93–102. https://doi.org/10.55951/nurture.v17i2.206

Murri, R., Angelis, G. De, Antenucci, L., Fiori, B., Rinaldi, R., Fantoni, M., Damiani, A., Patarnello, S., Sanguinetti, M., Valentini, V., Posteraro, B., & Masciocchi, C. (2024). A Machine Learning Predictive Model of Bloodstream Infection in Hospitalized Patients. _Diagnostics_, _14_(4), 445. https://doi.org/10.3390/diagnostics14040445

Mylläri, S., Saarni, S., Ritola, V., Joffe, G., Stenberg, J., Solbakken, O. A., Czajkowski, N. O., & Rosenström, T. (2022). Text Topics and Treatment Response in Internet-Delivered Cognitive Behavioral Therapy for Generalized Anxiety Disorder: Text Mining Study. _Journal of Medical Internet Research_, _24_(11), e38911. https://doi.org/10.2196/38911

Olsavszky, V., Dosius, M., Vlădescu, C., & Benecke, J. (2020). Time Series Analysis and Forecasting With Automated Machine Learning on a National ICD-10 Database. _International Journal of Environmental Research and Public Health_, _17_(14), 4979. https://doi.org/10.3390/ijerph17144979

Praveen, A. K., Harsita, R., Murali, R. D., & Niveditha, S. (2023). Detecting Fake Job Posting Using ML Classifications and Ensemble Model. _Advances in Science and Technology_. https://doi.org/10.4028/p-hdm12o

Rajani, N. B., Perez, L. A. B., Weth, D., Romo, L., Mastellos, N., & Filippidis, F. T. (2023). Engagement With Gamification Elements in a Smoking Cessation App and Short-Term Smoking Abstinence: Quantitative Assessment. _Jmir Serious Games_, _11_, e39975. https://doi.org/10.2196/39975

Rashid, A., Sharif, I., Khan, S., & Malik, F. (2020). Relationship Between Time Management Behavior and Academic Performance of University Students. _Journal of Business and Social Review in Emerging Economies_, _6_(4), 1497–1504. https://doi.org/10.26710/jbsee.v6i4.1481

Ratul, I. J., Nishat, M. M., Faisal, F., Sultana, S., Ahmed, A., & Al Mamun, M. A. (2023). Analyzing Perceived Psychological and Social Stress of University Students: A Machine Learning Approach. _Heliyon_, _9_(6). https://doi.org/10.1016/j.heliyon.2023.e17307

Sfeir, E., Rabil, J.-M., Obeïd, S., Hallit, S., & Khalife, M. F. (2022). Work Fatigue Among Lebanese Physicians and Students During the COVID-19 Pandemic: Validation of the 3d-Work Fatigue Inventory (3d-Wfi) and Correlates. _BMC Public Health_, _22_(1). https://doi.org/10.1186/s12889-022-12733-9

Sheng, S., & Huang, Y. (2022). _Predicting 90-Day Readmission for Patients With Heart Failure: A Machine Learning Approach Using XGBoost_. https://doi.org/10.21203/rs.3.rs-2040978/v1

Shoiab, A. A., Khwaldeh, A., Alsarhan, A., Khashroum, A., Alsheikh, A., & Ababneh, S. (2022). Evaluation of Prevalence of Fatigue Among Jordanian University Students and Its Relation to COVID-19 Quarantine. _Open Access Macedonian Journal of Medical Sciences_, _10_(E), 1898–1903. https://doi.org/10.3889/oamjms.2022.10842

Silva, C. S. da, Teles, G. de O., Marques, V. A., Silva, M. S., Rebelo, A. C. S., & Fiuza, T. de S. (2022). The Practice of Physical Exercise in the Cardiovascular and Psychobiological Health of University Students. _Research Society and Development_, _11_(1), e36711125000. https://doi.org/10.33448/rsd-v11i1.25000

Siregar, K. N., Eryando, T., Rahmaniati, M., Trihandini, I., & Retnowati, R. (2020). _New Research Agenda: Potentials Use of Machine Learning for Public Health_. https://doi.org/10.2991/ahsr.k.200215.028

Teixeira, J. E., Afonso, P., Schneider, A., Branquinho, L., Maio, E., Ferraz, R., Nascimento, R., Morgans, R., Barbosa, T. M., Monteiro, A. M., & Forte, P. (2025). Player Tracking Data and Psychophysiological Features Associated with Mental Fatigue in U15, U17, and U19 Male Football Players: A Machine Learning Approach. _Applied Sciences (Switzerland)_, _15_(7). https://doi.org/10.3390/app15073718

Twick, I., Zahavi, G., Benvenisti, H., Rubinstein, R., Woods, M. S., Berkenstadt, H., Nissan, A., Hosgor, E., & Assaf, D. (2022). Towards Interpretable, Medically Grounded, EMR-based Risk Prediction Models. _Scientific Reports_, _12_(1). https://doi.org/10.1038/s41598-022-13504-7

Wang, S. (2024). Diabetes Prediction Using Random Forest in Healthcare. _Highlights in Science Engineering and Technology_, _92_, 210–217. https://doi.org/10.54097/5ndh9a05

Warsinsky, S., Schmidt-Kraepelin, M., Thiebes, S., & Sunyaev, A. (2021). _Are Gamification Projects Different? An Exploratory Study on Software Project Risks for Gamified Health Behavior Change Support Systems_. https://doi.org/10.24251/hicss.2021.159

Yan, S., Hosseinmardi, H., Kao, H. Te, Narayanan, S., Lerman, K., & Ferrara, E. (2020). Affect Estimation with Wearable Sensors. _Journal of Healthcare Informatics Research_, _4_(3), 261–294. https://doi.org/10.1007/s41666-019-00066-z

Yosep, I., Mardhiyah, A., Suryani, S., & Mediani, ‎Henny S. (2023). Hardiness and Zoom Fatigue on Nursing Students: A Cross-Sectional Study in Indonesia During Online Learning. _Advances in Medical Education and Practice_, _Volume 14_, 1137–1145. https://doi.org/10.2147/amep.s430776

Zeng, Z., Huang, Z., Leng, K., Han, W., Niu, H., Yu, Y., Ling, Q., Liu, J., Wu, Z., & Zang, J. (2020). Nonintrusive Monitoring of Mental Fatigue Status Using Epidermal Electronic Systems and Machine-Learning Algorithms. _Acs Sensors_, _5_(5), 1305–1313. https://doi.org/10.1021/acssensors.9b02451

Zhang, L., Zhao, S., Yang, W., Yang, Z., Wu, Z., Zheng, H., & Lei, M. (2024). Utilizing machine learning techniques to identify severe sleep disturbances in Chinese adolescents: an analysis of lifestyle, physical activity, and psychological factors. _Frontiers in Psychiatry_, _15_. https://doi.org/10.3389/fpsyt.2024.1447281

Zhang, T., Zhong, Z., Mao, W., Zhang, Z., & Li, Z. (2024). A New Machine-Learning-Driven Grade-Point Average Prediction Approach for College Students Incorporating Psychological Evaluations in the Post-COVID-19 Era. _Electronics (Switzerland)_, _13_(10). https://doi.org/10.3390/electronics13101928

Zhang, X., Shi, X., Wang, Y., Jing, H., Zhai, Q., Li, K., Zhao, D., Zhong, S., Song, Y., Zhang, F., & Bao, Y. (2021). Risk Factors of Psychological Responses of Chinese University Students During the COVID-19 Outbreak: Cross-Sectional Web-Based Survey Study. _Journal of Medical Internet Research_, _23_(7), e29312. https://doi.org/10.2196/29312

Zhang, Y., & Li, J. (2025). Deep Learning-Based Model for Predicting Student Learning Behavior: A Pathway to Early Intervention and Enhanced Outcomes. _Journal of Computational Methods in Sciences and Engineering_, _25_(3), 2822–2835. https://doi.org/10.1177/14727978251322332

Zhao, Q., Liu, L., Luo, J., Luo, Y., Wang, H., Zhang, Y., Gui, R., Tu, G.-W., & Luo, Z. (2021). A Machine-Learning Approach for Dynamic Prediction of Sepsis-Induced Coagulopathy in Critically Ill Patients With Sepsis. _Frontiers in Medicine_, _7_. https://doi.org/10.3389/fmed.2020.637434
