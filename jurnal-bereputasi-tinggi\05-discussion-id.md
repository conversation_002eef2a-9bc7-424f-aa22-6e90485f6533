# 5. PEMBAHASAN

## 5.1 Pergeseran Paradigma: Fitur Linguistik sebagai Prediktor Utama Kelelahan

Temuan paling signifikan dari penelitian ini adalah dominasi fitur linguistik yang mengubah paradigma dalam prediksi kele<PERSON>, dengan pola kognitif-linguistik berkontribusi 15,06% dari total kekuatan prediktif dibandingkan dengan metrik kuantitatif tradisional. Temuan ini secara fundamental menantang pendekatan konvensional dalam pemantauan kesehatan digital, yang secara historis bergantung terutama pada data kuantitatif fisiologis dan perilaku seperti detak jantung, jum<PERSON> langkah, dan durasi aktivitas.

Dominasi pomokit_unique_words (5,54%), total_title_diversity (5,33%), dan title_balance_ratio (5,19%) sebagai tiga prediktor teratas menunjukkan bahwa kompleksitas kognitif dan keragaman linguistik dalam cara mahasiswa mendeskripsikan aktivitas mereka berfungsi sebagai indikator yang lebih sensitif terhadap keadaan kelelahan daripada pengukuran fisik langsung. Hal ini sejalan dengan teori beban kognitif, yang menyatakan bahwa kelelahan mental termanifestasi dalam berkurangnya kapasitas pemrosesan kognitif, yang berpotensi tercermin dalam penggunaan bahasa yang disederhanakan dan penurunan keragaman leksikal dalam konten yang dihasilkan sendiri.

Implikasi teoretis dari temuan ini meluas melampaui prediksi kelelahan hingga pertanyaan yang lebih luas tentang hubungan antara penggunaan bahasa dan keadaan kognitif. Fakta bahwa fitur linguistik yang diekstrak dari judul aktivitas singkat mengungguli metrik kuantitatif komprehensif menunjukkan bahwa pemrosesan bahasa alami dari konten yang dihasilkan pengguna mungkin memberikan jendela yang lebih langsung ke dalam keadaan psikologis dan kognitif daripada yang sebelumnya diakui. Hal ini mendukung bidang yang berkembang dari psikolinguistik komputasional, yang meneliti bagaimana pola penggunaan bahasa mencerminkan proses mental yang mendasari.

## 5.2 Trade-off Performa Model dan Stabilitas

Evaluasi komprehensif mengungkapkan trade-off kritis antara akurasi prediktif dan stabilitas model yang memiliki implikasi penting untuk deployment praktis. XGBoost mencapai akurasi test tertinggi (79,66%) tetapi menunjukkan overfitting signifikan dengan gap 12,9% antara performa test dan cross-validation, sementara Logistic Regression menunjukkan stabilitas superior dengan overfitting minimal (gap 1,71%) meskipun akurasi puncak lebih rendah (71,19%).

Temuan ini menyoroti tantangan fundamental dalam aplikasi pembelajaran mesin untuk kesehatan: ketegangan antara memaksimalkan performa prediktif dan memastikan model yang andal dan dapat digeneralisasi. Skor overfitting tinggi yang diamati pada model berbasis pohon (Random Forest: 27,8%, Gradient Boosting: 18,7%, XGBoost: 21,3%) menunjukkan bahwa model kompleks ini mungkin menangkap noise daripada sinyal sebenarnya dalam dataset yang relatif kecil (291 observasi). Hal ini konsisten dengan prinsip bias-variance trade-off, di mana kompleksitas model harus diseimbangkan dengan data pelatihan yang tersedia.

Stabilitas superior Logistic Regression, meskipun asumsi linearnya, menunjukkan bahwa hubungan antara fitur dan risiko kelelahan mungkin lebih linear daripada yang awalnya dihipotesiskan. Temuan ini memiliki implikasi praktis untuk skenario deployment di mana keandalan model dan interpretabilitas diprioritaskan daripada peningkatan akurasi marginal. Performa konsisten Logistic Regression di berbagai fold cross-validation (69,35% ± 4,46%) memberikan kepercayaan dalam generalisabilitasnya ke populasi mahasiswa baru.

## 5.3 Interpretabilitas yang Ditingkatkan SHAP dan Relevansi Klinis

Penerapan analisis SHAP memberikan interpretabilitas yang belum pernah ada sebelumnya dalam model prediksi kelelahan, mengatasi kesenjangan kritis dalam aplikasi AI kesehatan di mana transparensi model sangat penting untuk adopsi klinis. Peringkat fitur yang konsisten di keempat algoritma (korelasi Spearman = 0,89) memvalidasi robustness pola prediktif yang diidentifikasi dan memberikan kepercayaan dalam interpretasi klinis.

Relevansi klinis dari fitur linguistik teratas menawarkan wawasan yang dapat ditindaklanjuti untuk pengembangan intervensi. Dominasi pomokit_unique_words menunjukkan bahwa memantau kompleksitas kognitif deskripsi tugas dapat berfungsi sebagai sistem peringatan dini untuk kelelahan mental. Mahasiswa yang mengalami kelelahan mungkin secara tidak sadar menyederhanakan penggunaan bahasa mereka, mengurangi keragaman kosakata dalam deskripsi aktivitas. Temuan ini sejalan dengan penelitian dalam psikologi kognitif yang menunjukkan bahwa kelelahan mental mengurangi kapasitas memori kerja dan fungsi eksekutif, yang berpotensi termanifestasi dalam ekspresi linguistik yang disederhanakan.

Demikian pula, pentingnya total_title_diversity (5,33%) menunjukkan bahwa variasi leksikal di berbagai aktivitas berkorelasi dengan ketersediaan sumber daya kognitif. Mahasiswa dengan cadangan kognitif yang lebih tinggi mungkin menunjukkan kreativitas dan variasi linguistik yang lebih besar dalam mendeskripsikan aktivitas mereka, sementara mereka yang mengalami kelelahan mungkin menunjukkan fleksibilitas linguistik yang berkurang. Hal ini menyediakan metode non-intrusif untuk memantau perubahan keadaan kognitif dari waktu ke waktu.

Title_balance_ratio (5,19%) mengungkapkan bahwa kompleksitas relatif antara deskripsi produktivitas dan aktivitas fisik berfungsi sebagai penanda keseimbangan kognitif-fisik. Temuan ini menunjukkan bahwa mahasiswa secara alami menyesuaikan investasi kognitif mereka dalam berbagai jenis aktivitas berdasarkan keadaan kelelahan keseluruhan mereka, dengan pola penyesuaian ini dapat dideteksi melalui analisis linguistik.

## 5.4 Validasi Pendekatan Analisis Berbasis Judul Saja

Efektivitas analisis berbasis judul saja merepresentasikan kemajuan metodologis yang signifikan dengan implikasi praktis yang penting. Mencapai akurasi 71,19% menggunakan hanya fitur linguistik yang diekstrak dari judul aktivitas menunjukkan bahwa pengumpulan data kuantitatif komprehensif mungkin tidak diperlukan untuk pemantauan kelelahan yang efektif. Temuan ini mengatasi hambatan utama untuk implementasi luas sistem pemantauan kesehatan digital, termasuk kekhawatiran privasi, beban pengumpulan data, dan persyaratan infrastruktur.

Pendekatan berbasis judul saja menunjukkan kekuatan khusus dalam mengidentifikasi kasus kelelahan risiko tinggi dengan presisi 78%, menunjukkan bahwa penanda linguistik berfungsi sebagai indikator peringatan dini yang andal untuk keadaan kelelahan parah. Presisi tinggi untuk kategori paling kritis (risiko tinggi) ini sangat berharga dari perspektif klinis, karena meminimalkan alarm palsu sambil mempertahankan sensitivitas terhadap kasus yang memerlukan intervensi.

Implikasi praktis dari temuan ini meluas ke skenario implementasi dunia nyata di mana pengumpulan data komprehensif mungkin tidak praktis atau intrusif. Institusi pendidikan dapat mengimplementasikan sistem pemantauan kelelahan yang hanya memerlukan deskripsi aktivitas singkat dari mahasiswa, secara signifikan mengurangi hambatan teknologi dan privasi yang terkait dengan pemantauan perilaku komprehensif. Pendekatan ini sejalan dengan prinsip minimal viable monitoring, di mana tujuannya adalah mencapai wawasan kesehatan maksimum dengan persyaratan pengumpulan data minimum.

## 5.5 Pertimbangan Lintas Budaya dan Generalisabilitas

Fokus penelitian pada mahasiswa universitas Indonesia menimbulkan pertanyaan penting tentang generalisabilitas lintas budaya dari prediksi kelelahan berbasis linguistik. Pola penggunaan bahasa, ekspresi budaya kelelahan, dan norma perilaku digital dapat bervariasi secara signifikan di berbagai konteks budaya, yang berpotensi mempengaruhi transferabilitas fitur linguistik yang diidentifikasi.

Framework koreksi bias yang diimplementasikan dalam penelitian ini mengatasi beberapa bias budaya dan spesifik platform, sebagaimana dibuktikan oleh performa stabil di berbagai populasi mahasiswa dalam konteks Indonesia. Namun, validasi eksternal pada kelompok budaya dan bahasa yang berbeda akan diperlukan untuk menetapkan generalisabilitas yang lebih luas. Dominasi fitur linguistik mungkin sangat sensitif terhadap variasi budaya dan linguistik, memerlukan adaptasi metode ekstraksi fitur untuk bahasa dan konteks budaya yang berbeda.

Ketergantungan penelitian pada platform berbahasa Inggris (Strava) dan mahasiswa Indonesia yang menggunakan deskripsi aktivitas predominan bahasa Inggris dapat membatasi generalisabilitas ke populasi yang menggunakan bahasa lain atau platform digital yang berbeda. Penelitian masa depan harus menyelidiki apakah pola linguistik serupa muncul dalam bahasa lain dan apakah pentingnya relatif fitur linguistik versus kuantitatif tetap konsisten di berbagai konteks budaya.

## 5.6 Perbandingan dengan Literatur yang Ada

Temuan penelitian ini sejalan dengan dan memperluas penelitian yang ada dalam pemantauan kesehatan digital dan prediksi kelelahan. Performa superior fitur linguistik dibandingkan dengan metrik fisiologis tradisional kontras dengan penelitian sebelumnya yang telah fokus terutama pada pendekatan berbasis sensor. Zhang et al. (2024) mencapai akurasi 83,64% menggunakan indikator psikologis dan data akademik, sementara penelitian ini mencapai 79,66% menggunakan terutama fitur linguistik, menunjukkan bahwa pendekatan berbasis bahasa mungkin menawarkan jalur komplementer atau alternatif untuk metode penilaian psikologis tradisional.

Penekanan penelitian pada interpretabilitas melalui analisis SHAP mengatasi kesenjangan kritis yang diidentifikasi dalam penelitian sebelumnya. Sementara Lekkas et al. (2021) mencapai AUC 75,5% dalam memprediksi ideasi bunuh diri melalui analisis media sosial, pendekatan mereka kurang memiliki framework interpretabilitas sistematis yang disediakan oleh analisis SHAP. Integrasi SHAP penelitian saat ini dengan beberapa algoritma memberikan fondasi yang lebih robust untuk memahami kontribusi fitur dan membangun kepercayaan klinis dalam prediksi berbasis AI.

Temuan bahwa metode ensemble (Random Forest, Gradient Boosting, XGBoost) menunjukkan overfitting tinggi kontras dengan performa superior mereka yang khas dalam domain lain. Hal ini menunjukkan bahwa prediksi kelelahan dari data perilaku digital mungkin memiliki karakteristik yang berbeda dari aplikasi pembelajaran mesin lainnya, kemungkinan karena variabilitas individual yang tinggi dalam ekspresi kelelahan dan ukuran dataset yang relatif kecil yang khas dari penelitian kesehatan.

## 5.7 Keterbatasan dan Arah Masa Depan

Beberapa keterbatasan harus diakui dalam menginterpretasikan temuan ini. Desain cross-sectional dengan elemen longitudinal terbatas membatasi kemampuan untuk menetapkan hubungan kausal antara pola linguistik dan keadaan kelelahan. Sementara penelitian menunjukkan asosiasi prediktif, mekanisme yang mendasari yang menghubungkan penggunaan bahasa dengan kelelahan masih harus dijelaskan sepenuhnya melalui desain penelitian longitudinal.

Representativeness sampel terbatas pada mahasiswa universitas yang aktif menggunakan aplikasi pelacakan kebugaran dan produktivitas digital, berpotensi mengecualikan mahasiswa dengan pola adopsi teknologi yang berbeda atau latar belakang sosial ekonomi. Bias seleksi ini dapat membatasi generalisabilitas temuan ke populasi mahasiswa yang lebih luas, terutama mereka dengan akses terbatas atau keterlibatan dengan teknologi kesehatan digital.

Ketergantungan pada data self-reported melalui platform digital memperkenalkan bias potensial yang terkait dengan desirabilitas sosial, akurasi recall, dan perilaku pengguna spesifik platform. Sementara penelitian mengimplementasikan prosedur koreksi bias, keterbatasan fundamental data self-reported tetap ada. Penelitian masa depan dapat menguntungkan dari integrasi dengan ukuran fisiologis objektif untuk memvalidasi hubungan antara pola linguistik dan keadaan kelelahan aktual.

Periode observasi temporal 13 minggu, meskipun substansial untuk penelitian perilaku, mungkin tidak menangkap pola jangka panjang atau variasi musiman dalam pola kelelahan dan aktivitas. Kalender akademik, perubahan musiman, dan peristiwa kehidupan jangka panjang dapat mempengaruhi baik pola kelelahan maupun ekspresi linguistik dengan cara yang tidak ditangkap oleh desain penelitian saat ini.

## 5.8 Implikasi untuk Inovasi Kesehatan Digital

Temuan penelitian ini memiliki implikasi signifikan untuk pengembangan masa depan sistem pemantauan kesehatan digital. Demonstrasi bahwa analisis linguistik dapat memberikan performa prediktif superior dibandingkan dengan metrik kuantitatif tradisional menunjukkan pergeseran fundamental dalam bagaimana aplikasi kesehatan digital harus dirancang dan diimplementasikan.

Pendekatan analisis berbasis judul saja menawarkan jalur untuk mengembangkan sistem pemantauan kesehatan yang menjaga privasi yang memerlukan pengumpulan data minimal sambil mempertahankan akurasi prediktif yang substansial. Hal ini mengatasi kekhawatiran yang berkembang tentang privasi data dalam aplikasi kesehatan digital dan dapat memfasilitasi adopsi yang lebih luas dari teknologi pemantauan kesehatan dalam pengaturan pendidikan dan tempat kerja.

Framework interpretabilitas yang ditingkatkan SHAP menyediakan model untuk mengembangkan sistem AI yang dapat diterima secara klinis dalam konteks kesehatan. Kemampuan untuk memberikan penjelasan yang jelas dan berdasar teoretis untuk prediksi sangat penting untuk adopsi klinis dan persetujuan regulasi sistem pemantauan kesehatan berbasis AI. Peringkat fitur yang konsisten di beberapa algoritma memberikan kepercayaan dalam keandalan penjelasan ini.

Integrasi data kardiovaskular dan produktivitas akademik menunjukkan nilai pendekatan multi-modal dalam pemantauan kesehatan digital. Daripada fokus pada aliran data tunggal, aplikasi kesehatan digital masa depan dapat menguntungkan dari mengintegrasikan sumber data perilaku yang beragam sambil menggunakan analisis linguistik sebagai framework interpretatif yang menyatukan.

## 5.9 Kontribusi Teoretis dan Penelitian Masa Depan

Penelitian ini berkontribusi pada beberapa framework teoretis dalam kesehatan digital, linguistik komputasional, dan psikologi perilaku. Temuan bahwa fitur linguistik mendominasi prediksi kelelahan mendukung teori kognisi yang terwujud, yang menunjukkan bahwa keadaan kognitif tercermin dalam pola penggunaan bahasa. Penelitian ini memberikan bukti empiris untuk utilitas pendekatan psikolinguistik komputasional dalam aplikasi pemantauan kesehatan.

Penerapan analisis SHAP yang berhasil pada model prediksi kesehatan berkontribusi pada bidang yang berkembang dari AI yang dapat dijelaskan dalam kesehatan. Demonstrasi bahwa metode atribusi fitur berbasis teori permainan dapat memberikan wawasan yang relevan secara klinis menunjukkan aplikasi yang lebih luas untuk SHAP dan metode interpretabilitas serupa dalam aplikasi AI kesehatan.

Arah penelitian masa depan harus mencakup studi longitudinal untuk menetapkan hubungan kausal antara pola linguistik dan pengembangan kelelahan, studi validasi eksternal di berbagai konteks budaya dan linguistik, integrasi dengan ukuran fisiologis objektif untuk memvalidasi prediksi berbasis linguistik, dan pengembangan sistem pemantauan real-time berdasarkan pendekatan analisis berbasis judul saja.

Penelitian ini juga membuka pertanyaan tentang integrasi optimal fitur linguistik dan kuantitatif, pengembangan baseline linguistik yang dipersonalisasi untuk pengguna individual, dan aplikasi potensial pendekatan serupa untuk kondisi kesehatan lain selain kelelahan. Arah-arah ini dapat lebih lanjut menetapkan analisis linguistik sebagai komponen fundamental dari sistem pemantauan kesehatan digital.
