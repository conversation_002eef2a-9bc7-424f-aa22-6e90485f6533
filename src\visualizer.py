"""
Visualization Module
Clean code implementation for creating publication-ready visualizations
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class Visualizer:
    """
    Clean visualization class following SOLID principles
    """
    
    def __init__(self, output_path: str = "results/visualizations"):
        """
        Initialize visualizer
        
        Args:
            output_path: Path to save visualizations
        """
        self.output_path = Path(output_path)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Configure matplotlib for high quality output
        plt.rcParams['figure.dpi'] = 300
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['font.size'] = 10
        plt.rcParams['axes.titlesize'] = 12
        plt.rcParams['axes.labelsize'] = 10
        plt.rcParams['xtick.labelsize'] = 9
        plt.rcParams['ytick.labelsize'] = 9
        plt.rcParams['legend.fontsize'] = 9
    


    def create_achievement_comparison(self, data: pd.DataFrame,
                                    achievement_var: str, target_var: str,
                                    title: str, filename: str) -> None:
        """
        Create achievement level comparison plot
        
        Args:
            data: Input dataset
            achievement_var: Achievement variable
            target_var: Target variable
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating achievement comparison: {filename}")
        
        # Create achievement categories
        data_copy = data.copy()
        data_copy['achievement_category'] = pd.cut(
            data_copy[achievement_var], 
            bins=[0, 0.33, 0.66, 1.0], 
            labels=['Low', 'Medium', 'High']
        )
        
        # Calculate means by category
        means = data_copy.groupby('achievement_category')[target_var].mean()
        counts = data_copy.groupby('achievement_category').size()
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create bar plot
        bars = ax.bar(means.index, means.values, 
                     color=['#ff7f7f', '#ffbf7f', '#7fbf7f'], alpha=0.8)
        
        # Add value labels on bars
        for i, (bar, mean, count) in enumerate(zip(bars, means.values, counts.values)):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                   f'{mean:.1f}\n(n={count})', ha='center', va='bottom', fontweight='bold')
        
        # Customize plot
        ax.set_xlabel('Achievement Level')
        ax.set_ylabel(self._format_variable_name(target_var))
        ax.set_title(title, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3, axis='y')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_distribution_plot(self, data: pd.DataFrame,
                               variable: str, title: str, filename: str) -> None:
        """
        Create distribution plot with statistics
        
        Args:
            data: Input dataset
            variable: Variable to plot
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating distribution plot: {filename}")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Histogram
        ax1.hist(data[variable], bins=20, alpha=0.7, color='steelblue', edgecolor='black')
        ax1.set_xlabel(self._format_variable_name(variable))
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Box plot
        ax2.boxplot(data[variable], vert=True)
        ax2.set_ylabel(self._format_variable_name(variable))
        ax2.set_title('Box Plot')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics
        stats_text = f"""
        Mean: {data[variable].mean():.2f}
        Median: {data[variable].median():.2f}
        Std: {data[variable].std():.2f}
        Min: {data[variable].min():.2f}
        Max: {data[variable].max():.2f}
        """
        
        fig.text(0.02, 0.98, stats_text, transform=fig.transFigure, 
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        # Main title
        fig.suptitle(title, fontsize=14, fontweight='bold')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def _format_variable_name(self, var_name: str) -> str:
        """Format variable name for display"""
        
        name_mapping = {
            'total_cycles': 'Total Cycles (per week)',
            'consistency_score': 'Consistency Score',
            'activity_days': 'Activity Days (per week)',
            'avg_intensity': 'Average Intensity (km/h)',
            'total_gamification_points': 'Total Gamification Points',
            'achievement_rate': 'Achievement Rate (%)',
            'gamification_balance': 'Gamification Balance',
            'activity_points': 'Activity Points',
            'productivity_points': 'Productivity Points'
        }
        
        return name_mapping.get(var_name, var_name.replace('_', ' ').title())
    
    def create_basic_visualizations(self, data: pd.DataFrame) -> None:
        """
        Create basic visualizations without correlation analysis

        Args:
            data: Input dataset
        """
        logger.info("Creating basic visualizations...")

        # 1. Achievement comparison
        if 'achievement_rate' in data.columns:
            self.create_achievement_comparison(
                data, 'achievement_rate', 'total_cycles',
                "Productivity by Achievement Level",
                "01_achievement_comparison.png"
            )

        # 2. Productivity distribution
        self.create_distribution_plot(
            data, 'total_cycles',
            "Productivity Distribution Analysis",
            "02_productivity_distribution.png"
        )

        logger.info("Basic visualizations created successfully")


def main():
    """Main function for testing visualizer"""
    # Load processed data
    data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")

    if not data_path.exists():
        print("Processed data not found. Run data_processor.py first.")
        return

    data = pd.read_csv(data_path)

    # Create basic visualizations
    visualizer = Visualizer()
    visualizer.create_basic_visualizations(data)

    print("Basic visualizations created successfully!")


if __name__ == "__main__":
    main()
