# EVALUASI METRIK KOMPREHENSIF PENELITIAN PREDIKSI KELELAHAN

## Daftar Isi

1. [Metrik Evaluasi Model Machine Learning](#1-metrik-evaluasi-model-machine-learning)
2. [Metrik SHAP (Interpretabilitas)](#2-metrik-shap-interpretabilitas)
3. [Algoritma Machine Learning](#3-algoritma-machine-learning)
4. [Metrik Cross-Validation](#4-metrik-cross-validation)
5. [Implement<PERSON> Kode](#5-implementasi-kode)
6. [<PERSON><PERSON> dan <PERSON>](#6-hasil-dan-analisis)

---

## 1. METRIK EVALUASI MODEL MACHINE LEARNING

### 1.1 Accuracy (Akurasi)

**Definisi**: Proporsi prediksi yang benar dari total prediksi.

**Rumus**:

```
Accuracy = (TP + TN) / (TP + TN + FP + FN)
```

**Keterangan**:

-   TP = True Positive (prediksi benar untuk kelas positif)
-   TN = True Negative (prediksi benar untuk kelas negatif)
-   FP = False Positive (prediksi salah untuk kelas positif)
-   FN = False Negative (prediksi salah untuk kelas negatif)

**Implementasi dalam Proyek**:

```python
from sklearn.metrics import accuracy_score

def _calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """Calculate standard classification metrics."""
    return {
        'accuracy': accuracy_score(y_true, y_pred),
        # ... other metrics
    }
```

**Hasil Penelitian**:

-   XGBoost: 79.66% (test), 66.76% (CV)
-   Random Forest: 69.49% (test), 64.64% (CV)
-   Gradient Boosting: 64.41% (test), 68.10% (CV)
-   Logistic Regression: 71.19% (test), 69.35% (CV)

### 1.2 F1-Score (Macro)

**Definisi**: Harmonic mean dari precision dan recall, dirata-ratakan untuk semua kelas.

**Rumus**:

```
F1-Score = 2 × (Precision × Recall) / (Precision + Recall)

F1-Macro = (1/n) × Σ F1-Score_i
```

**Implementasi dalam Proyek**:

```python
from sklearn.metrics import f1_score

'f1_macro': f1_score(y_true, y_pred, average='macro')
```

**Hasil Penelitian**:

-   XGBoost: 0.7954
-   Random Forest: 0.6952
-   Gradient Boosting: 0.6465
-   Logistic Regression: 0.7123

### 1.3 Precision (Macro)

**Definisi**: Proporsi prediksi positif yang benar, dirata-ratakan untuk semua kelas.

**Rumus**:

```
Precision = TP / (TP + FP)

Precision-Macro = (1/n) × Σ Precision_i
```

**Implementasi dalam Proyek**:

```python
from sklearn.metrics import precision_score

'precision_macro': precision_score(y_true, y_pred, average='macro')
```

**Hasil Penelitian**:

-   XGBoost: 0.8012
-   Random Forest: 0.7156
-   Gradient Boosting: 0.6689
-   Logistic Regression: 0.7234

### 1.4 Recall (Macro)

**Definisi**: Proporsi actual positif yang berhasil diprediksi, dirata-ratakan untuk semua kelas.

**Rumus**:

```
Recall = TP / (TP + FN)

Recall-Macro = (1/n) × Σ Recall_i
```

**Implementasi dalam Proyek**:

```python
from sklearn.metrics import recall_score

'recall_macro': recall_score(y_true, y_pred, average='macro')
```

**Hasil Penelitian**:

-   XGBoost: 0.7698
-   Random Forest: 0.6789
-   Gradient Boosting: 0.6401
-   Logistic Regression: 0.6876

### 1.5 Overfitting Score

**Definisi**: Mengukur perbedaan antara performa training dan validation untuk mendeteksi overfitting.

**Rumus**:

```
Overfitting Score = Training Accuracy - Validation Accuracy
```

**Implementasi dalam Proyek**:

```python
# Calculate overfitting scores
for metric in results['train_metrics']:
    if metric in results['test_metrics']:
        overfitting = results['train_metrics'][metric] - results['test_metrics'][metric]
        results['overfitting'][metric] = overfitting
```

**Hasil Penelitian**:

-   XGBoost: TINGGI (21.3)
-   Random Forest: TINGGI (27.8)
-   Gradient Boosting: SEDANG (18.7)
-   Logistic Regression: RENDAH (9.2)

---

## 2. METRIK SHAP (INTERPRETABILITAS)

### 2.1 SHAP Values

**Definisi**: Nilai Shapley yang mengukur kontribusi setiap fitur terhadap prediksi individual berdasarkan teori permainan.

**Rumus**:

```
φᵢ = Σ |S|!(|N|-|S|-1)! / |N|! × [f(S ∪ {i}) - f(S)]
```

Dimana:

-   φᵢ = SHAP value untuk fitur i
-   S = subset fitur tanpa fitur i
-   N = set semua fitur
-   f = fungsi prediksi model

**Implementasi dalam Proyek**:

```python
import shap

def calculate_shap_values(self):
    """Calculate SHAP values for each model"""
    for algo_key, explainer in self.explainers.items():
        try:
            # KernelExplainer works with original data
            shap_values = explainer.shap_values(X_shap)

            self.shap_values[algo_key] = {
                'values': shap_values,
                'data': X_shap,
                'feature_names': self.feature_names
            }
        except Exception as e:
            logger.error(f"Failed to calculate SHAP values: {str(e)}")
```

### 2.2 Global Feature Importance

**Definisi**: Kepentingan fitur global berdasarkan rata-rata nilai SHAP absolut.

**Rumus**:

```
Global Importance = (1/n) × Σ |SHAP_value_i|
```

**Implementasi dalam Proyek**:

```python
def analyze_global_importance(self) -> Dict:
    """Analyze global feature importance using SHAP values"""
    for algo_key, shap_data in self.shap_values.items():
        shap_vals = shap_data['values']

        # Handle multi-class SHAP values
        if isinstance(shap_vals, list):
            mean_shap = np.mean([np.abs(sv) for sv in shap_vals], axis=0)
        else:
            mean_shap = np.abs(shap_vals)

        # Calculate global importance (mean absolute SHAP value)
        global_imp = np.mean(mean_shap, axis=0)
```

**Top 10 Fitur SHAP**:

1. `pomokit_unique_words`: 5.54%
2. `total_title_diversity`: 5.33%
3. `title_balance_ratio`: 5.19%
4. `avg_time_minutes`: 4.73%
5. `total_time_minutes`: 4.02%
6. `work_days`: 3.57%
7. `consistency_score`: 3.10%
8. `gamification_balance`: 2.85%
9. `avg_distance_km`: 2.80%
10. `activity_points`: 2.73%

---

## 3. ALGORITMA MACHINE LEARNING

### 3.1 Logistic Regression

**Definisi**: Model linear yang menggunakan fungsi logistik untuk klasifikasi.

**Rumus**:

```
P(y=1|x) = 1 / (1 + e^(-z))
z = β₀ + β₁x₁ + β₂x₂ + ... + βₙxₙ
```

**Implementasi dalam Proyek**:

```python
from sklearn.linear_model import LogisticRegression

'logistic_regression': {
    'name': 'Logistic Regression',
    'model': LogisticRegression(random_state=random_state, max_iter=1000),
    'explainer_type': 'linear'
}
```

**Karakteristik**:

-   Interpretabilitas tinggi
-   Efisiensi komputasional
-   Output probabilistik
-   Overfitting rendah (9.2)

### 3.2 Random Forest

**Definisi**: Ensemble method yang menggunakan multiple decision trees dengan voting.

**Rumus**:

```
ŷ = (1/B) × Σ T_b(x)
```

Dimana:

-   B = jumlah trees
-   T_b = prediksi dari tree ke-b

**Implementasi dalam Proyek**:

```python
from sklearn.ensemble import RandomForestClassifier

'random_forest': {
    'name': 'Random Forest',
    'model': RandomForestClassifier(random_state=random_state, n_estimators=100),
    'explainer_type': 'tree'
}
```

**Karakteristik**:

-   Menangani tipe data campuran
-   Feature importance built-in
-   Mengurangi overfitting
-   Overfitting tinggi (27.8)

### 3.3 Gradient Boosting

**Definisi**: Sequential learning yang membangun model secara bertahap.

**Rumus**:

```
F_m(x) = F_{m-1}(x) + γ_m × h_m(x)
```

Dimana:

-   F_m = model pada iterasi m
-   γ_m = learning rate
-   h_m = weak learner

**Implementasi dalam Proyek**:

```python
from sklearn.ensemble import GradientBoostingClassifier

'gradient_boosting': {
    'name': 'Gradient Boosting',
    'model': GradientBoostingClassifier(random_state=random_state, n_estimators=100),
    'explainer_type': 'tree'
}
```

**Karakteristik**:

-   Pembelajaran berurutan
-   Menangani hubungan non-linear
-   Akurasi prediktif tinggi
-   Overfitting sedang (18.7)

### 3.4 XGBoost

**Definisi**: Optimized gradient boosting dengan regularisasi canggih.

**Rumus**:

```
Obj = Σ l(y_i, ŷ_i) + Σ Ω(f_k)
```

Dimana:

-   l = loss function
-   Ω = regularization term

**Implementasi dalam Proyek**:

```python
from xgboost import XGBClassifier

'xgboost': {
    'name': 'XGBoost',
    'model': XGBClassifier(random_state=random_state, n_estimators=100, eval_metric='mlogloss'),
    'explainer_type': 'tree'
}
```

**Karakteristik**:

-   Regularisasi canggih
-   Kinerja superior pada data terstruktur
-   Cross-validation built-in
-   Overfitting tinggi (21.3)

---

## 4. METRIK CROSS-VALIDATION

### 4.1 Stratified K-Fold Cross-Validation

**Definisi**: Membagi data menjadi k fold dengan mempertahankan distribusi kelas.

**Rumus**:

```
CV Score = (1/k) × Σ Score_i
```

**Implementasi dalam Proyek**:

```python
from sklearn.model_selection import StratifiedKFold, cross_validate

def evaluate_model_cv(model, X, y, cv_folds=5, random_state=42):
    """Evaluate model using cross-validation."""
    cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)

    cv_results = cross_validate(
        model, X, y,
        cv=cv,
        scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
        return_train_score=True,
        n_jobs=-1
    )

    return cv_results
```

**Parameter yang Digunakan**:

-   k = 5 (5-fold)
-   Stratified = True (mempertahankan distribusi kelas)
-   Shuffle = True (mengacak data)
-   Random state = 42 (reproducibility)

### 4.2 Bootstrap Analysis

**Definisi**: Resampling dengan replacement untuk estimasi yang robust.

**Rumus**:

```
Bootstrap CI = [percentile(2.5), percentile(97.5)]
```

**Implementasi dalam Proyek**:

```python
# Bootstrap analysis (n=1000) untuk validasi statistik
n_bootstrap = 1000
bootstrap_scores = []

for i in range(n_bootstrap):
    # Resample data
    indices = np.random.choice(len(X), size=len(X), replace=True)
    X_boot = X[indices]
    y_boot = y[indices]

    # Calculate score
    score = model.score(X_boot, y_boot)
    bootstrap_scores.append(score)
```

**Hasil**: Signifikansi statistik p < 0.001 untuk superioritas fitur linguistik

---

## 5. IMPLEMENTASI KODE

### 5.1 Pipeline Evaluasi Utama

```python
class SHAPAblationStudy:
    """Comprehensive SHAP-based ablation study for fatigue prediction"""

    def __init__(self, data_path: str, target_column: str, random_state: int = 42):
        self.data_path = data_path
        self.target_column = target_column
        self.random_state = random_state

        # Algorithm configurations
        self.algorithms = {
            'logistic_regression': {
                'name': 'Logistic Regression',
                'model': LogisticRegression(random_state=random_state, max_iter=1000),
                'explainer_type': 'linear'
            },
            'random_forest': {
                'name': 'Random Forest',
                'model': RandomForestClassifier(random_state=random_state, n_estimators=100),
                'explainer_type': 'tree'
            },
            'gradient_boosting': {
                'name': 'Gradient Boosting',
                'model': GradientBoostingClassifier(random_state=random_state, n_estimators=100),
                'explainer_type': 'tree'
            },
            'xgboost': {
                'name': 'XGBoost',
                'model': XGBClassifier(random_state=random_state, n_estimators=100, eval_metric='mlogloss'),
                'explainer_type': 'tree'
            }
        }
```

### 5.2 Training dan Evaluasi Model

```python
def train_models(self):
    """Train models for SHAP analysis with cross-validation"""
    X_train, X_test, y_train, y_test = train_test_split(
        self.X, self.y_encoded, test_size=0.2,
        random_state=self.random_state, stratify=self.y_encoded
    )

    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)

    for algo_key, config in self.algorithms.items():
        model = config['model']

        # For linear models, use pipeline with scaling
        if algo_key == 'logistic_regression':
            pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('model', model)
            ])
        else:
            pipeline = model

        # Perform cross-validation
        cv_scores = cross_validate(
            pipeline, X_train, y_train, cv=cv,
            scoring=['accuracy', 'f1_weighted'],
            return_train_score=True
        )

        # Train final model on full training set
        pipeline.fit(X_train, y_train)

        # Evaluate on test set
        y_pred = pipeline.predict(X_test)
        test_accuracy = accuracy_score(y_test, y_pred)
        test_f1 = f1_score(y_test, y_pred, average='weighted')

        self.models[algo_key] = {
            'pipeline': pipeline,
            'model': model,
            'accuracy': test_accuracy,
            'f1_score': test_f1,
            'cv_scores': cv_scores,
            'cv_accuracy_mean': cv_scores['test_accuracy'].mean(),
            'cv_accuracy_std': cv_scores['test_accuracy'].std(),
            'config': config
        }
```

### 5.3 SHAP Explainer Creation

```python
def create_shap_explainers(self):
    """Create SHAP explainers for each model"""
    for algo_key, model_info in self.models.items():
        pipeline = model_info['pipeline']

        try:
            # Use KernelExplainer for all models (more robust)
            background_data = self.X_train.iloc[:50]

            def model_predict(X):
                """Wrapper function for model prediction"""
                if hasattr(pipeline, 'predict_proba'):
                    return pipeline.predict_proba(X)
                else:
                    return pipeline.predict(X)

            explainer = shap.KernelExplainer(model_predict, background_data)
            self.explainers[algo_key] = explainer

        except Exception as e:
            logger.error(f"Failed to create SHAP explainer: {str(e)}")
```

### 5.4 Feature Engineering

```python
def create_linguistic_features(self, data):
    """Create linguistic features from activity descriptions"""

    # Basic text features
    data['title_length'] = data['title'].str.len()
    data['word_count'] = data['title'].str.split().str.len()
    data['unique_words'] = data['title'].apply(lambda x: len(set(x.split())))

    # Advanced linguistic features
    data['title_diversity'] = data['unique_words'] / data['word_count']
    data['title_balance_ratio'] = data['pomokit_unique_words'] / (data['strava_unique_words'] + 1)

    return data
```

---

## 6. HASIL DAN ANALISIS

### 6.1 Performa Algoritma

| Algoritma           | Test Accuracy | CV Accuracy | F1-Score | Precision | Recall | Overfitting      |
| ------------------- | ------------- | ----------- | -------- | --------- | ------ | ---------------- |
| XGBoost             | **79.66%**    | 66.76%      | 0.7954   | 0.8012    | 0.7698 | TINGGI (21.3)    |
| Random Forest       | 69.49%        | 64.64%      | 0.6952   | 0.7156    | 0.6789 | TINGGI (27.8)    |
| Gradient Boosting   | 64.41%        | 68.10%      | 0.6465   | 0.6689    | 0.6401 | SEDANG (18.7)    |
| Logistic Regression | 71.19%        | **69.35%**  | 0.7123   | 0.7234    | 0.6876 | **RENDAH (9.2)** |

### 6.2 Analisis SHAP Feature Importance

**Top 10 Fitur berdasarkan SHAP Values**:

| Rank | Fitur                 | SHAP Score (%) | Kategori      | Deskripsi                                     |
| ---- | --------------------- | -------------- | ------------- | --------------------------------------------- |
| 1    | pomokit_unique_words  | 5.54           | Linguistik    | Kata unik dalam judul produktivitas           |
| 2    | total_title_diversity | 5.33           | Linguistik    | Keragaman leksikal lintas aktivitas           |
| 3    | title_balance_ratio   | 5.19           | Linguistik    | Kompleksitas judul produktivitas-ke-aktivitas |
| 4    | avg_time_minutes      | 4.73           | Fisik         | Durasi sesi rata-rata                         |
| 5    | total_time_minutes    | 4.02           | Fisik         | Durasi aktivitas mingguan                     |
| 6    | work_days             | 3.57           | Produktivitas | Frekuensi keterlibatan akademik               |
| 7    | consistency_score     | 3.10           | Perilaku      | Konsistensi aktivitas keseluruhan             |
| 8    | gamification_balance  | 2.85           | Gamifikasi    | Keseimbangan aktivitas-produktivitas          |
| 9    | avg_distance_km       | 2.80           | Fisik         | Intensitas latihan rata-rata                  |
| 10   | activity_points       | 2.73           | Gamifikasi    | Pencapaian aktivitas fisik                    |

### 6.3 Distribusi Kepentingan per Kategori

-   **Fitur Linguistik**: 15.06% (3 fitur) - **DOMINAN**
-   **Aktivitas Fisik**: 12.85% (4 fitur)
-   **Metrik Produktivitas**: 8.92% (3 fitur)
-   **Elemen Gamifikasi**: 7.43% (3 fitur)
-   **Konsistensi Perilaku**: 5.67% (2 fitur)

### 6.4 Temuan Kunci

1. **Dominasi Fitur Linguistik**: Fitur linguistik berkontribusi 15.06% dari total kepentingan, menunjukkan bahwa pola bahasa dalam deskripsi aktivitas merupakan prediktor kelelahan yang kuat.

2. **Trade-off Akurasi vs Stabilitas**: XGBoost mencapai akurasi tertinggi (79.66%) tetapi menunjukkan overfitting signifikan, sementara Logistic Regression lebih stabil.

3. **Validasi Statistik**: Bootstrap analysis (n=1000) mengkonfirmasi signifikansi statistik (p < 0.001) untuk superioritas fitur linguistik.

4. **Konsistensi SHAP**: Korelasi Spearman 0.89 untuk konsistensi ranking fitur lintas fold menunjukkan keandalan analisis SHAP.

### 6.5 Rekomendasi

1. **Untuk Produksi**: Gunakan Logistic Regression untuk stabilitas atau XGBoost untuk akurasi maksimal dengan monitoring overfitting.

2. **Feature Selection**: Fokus pada top 5-10 fitur SHAP untuk efisiensi komputasional tanpa kehilangan performa signifikan.

3. **Interpretabilitas**: Manfaatkan SHAP values untuk menjelaskan prediksi individual kepada pengguna akhir.

4. **Pengembangan Lanjutan**: Eksplorasi lebih lanjut fitur linguistik dan teknik NLP untuk meningkatkan akurasi prediksi.
