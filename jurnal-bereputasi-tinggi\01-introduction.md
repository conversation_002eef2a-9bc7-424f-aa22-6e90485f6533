# 1. INTRODUCTION

In the rapidly evolving digital era, university students face increasingly complex challenges in balancing physical activity and academic productivity. The predominant sedentary lifestyle among students, particularly those engaged in technology-intensive fields, has become a serious concern in the context of cardiovascular health and fatigue management. This lifestyle shift has created an urgent need for innovative approaches to monitor and predict student well-being using digital health technologies.

Cardiovascular activity plays a crucial role in maintaining both physical and mental health among university students. Research demonstrates that regular physical activity can enhance cognitive function, reduce stress levels, and improve academic productivity (Güneş & Demirer, 2022; <PERSON><PERSON> et al., 2024). However, many students struggle to maintain consistent physical activity routines due to high academic demands and ineffective time management strategies (<PERSON><PERSON><PERSON> et al., 2023). This challenge is compounded by the increasing prevalence of digital device usage and screen time, which further contributes to sedentary behaviors and potential health risks.

Fatigue represents a multidimensional phenomenon that significantly impacts academic performance and quality of life among university students. Unlike simple physical tiredness, fatigue encompasses physical, mental, and emotional dimensions that can affect motivation, concentration, and decision-making capabilities (<PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2022). The early identification of fatigue risk has become crucial for preventing broader negative impacts on student well-being and academic success. Traditional approaches to fatigue assessment rely heavily on subjective self-reporting or intrusive physiological monitoring, both of which present significant limitations in terms of accuracy, practicality, and scalability.

The advancement of wearable technology and mobile applications has opened new opportunities for real-time monitoring of physical activity and productivity patterns. Platforms such as Strava for cardiovascular activity tracking and Pomokit for productivity management provide rich datasets that can be analyzed to understand student behavioral patterns (Gholami et al., 2020). The integration of data from these platforms enables holistic analysis of the relationship between physical activity and academic productivity, offering unprecedented insights into student lifestyle patterns (Moshawrab et al., 2022). Machine learning and artificial intelligence have demonstrated significant potential in health data analysis and risk prediction applications, enabling personalized health management strategies based on individual student patterns (Masrom et al., 2023; Olsavszky et al., 2020).

Despite the growing interest in digital health monitoring, significant gaps remain in the current understanding and application of machine learning approaches for student fatigue prediction. Most existing research focuses on single-domain analysis, examining either physical activity or academic performance in isolation, without considering the complex interplay between these factors. Current fatigue prediction methods suffer from several critical limitations: physiological sensor-based approaches are often intrusive and require specialized equipment, self-reporting methods are subject to recall bias and social desirability effects, and sleep pattern analysis raises privacy concerns while potentially missing other contributing factors.

The lack of interpretable machine learning models in health prediction represents another significant gap. While complex algorithms may achieve high accuracy, their black-box nature limits clinical adoption and prevents healthcare professionals from understanding the underlying factors driving predictions. Furthermore, existing research has not adequately explored the potential of linguistic analysis in health prediction. The natural language content generated by users in digital platforms represents a rich source of information about cognitive and emotional states, yet this data source remains largely untapped in fatigue prediction applications.

This study addresses these research gaps through three primary objectives. First, we identify the most significant predictive factors for student fatigue risk through comprehensive SHAP-enhanced feature selection analysis across multiple domains, including cardiovascular activity metrics, academic productivity indicators, gamification elements, and novel linguistic features extracted from user-generated content. Second, we develop and evaluate machine learning models for fatigue risk classification using integrated multi-modal data, encompassing implementation of multiple algorithms, comprehensive performance evaluation, and stability analysis. Third, we explore the effectiveness of title-only analysis for fatigue prediction, investigating whether linguistic features extracted solely from activity descriptions can provide accurate predictions without requiring comprehensive quantitative data collection.

This research makes several novel contributions to the fields of digital health monitoring and computational linguistics. The primary contribution is the introduction of a SHAP-enhanced feature selection framework that provides theoretically grounded, interpretable insights into fatigue prediction mechanisms, addressing the critical need for explainable AI in healthcare applications. We present a paradigm-shifting finding regarding the dominance of linguistic features in fatigue prediction, demonstrating that cognitive-linguistic patterns extracted from brief activity descriptions provide superior predictive power compared to traditional quantitative metrics. This finding challenges conventional approaches to health monitoring and opens new avenues for non-intrusive digital health applications.

We present the first comprehensive validation of title-only analysis for health prediction, demonstrating that meaningful health insights can be obtained from minimal data collection. This contribution addresses major barriers to widespread implementation of digital health monitoring systems, including privacy concerns, data collection burden, and infrastructure requirements. The research contributes a novel multi-modal integration framework that combines cardiovascular activity data, academic productivity metrics, and linguistic features in a unified analytical approach, providing a template for future research in digital health monitoring.

The methodological innovations include the integration of SHapley Additive exPlanations (SHAP) analysis, which provides theoretically grounded, model-agnostic explanations based on cooperative game theory principles. Unlike traditional feature importance methods that provide algorithm-specific rankings, SHAP ensures consistent and reliable feature attribution across different algorithms, providing robust insights into the underlying mechanisms of fatigue prediction. The development of comprehensive linguistic feature extraction methods represents another innovation, introducing systematic approaches for extracting meaningful linguistic features from user-generated activity descriptions, including lexical diversity measures, semantic content analysis, and cross-platform linguistic balance indicators.

The practical implications of this research extend across multiple domains. For educational technology integration, the demonstration that effective fatigue prediction can be achieved through title-only analysis opens possibilities for implementing health monitoring features in existing learning management systems and student support platforms without requiring additional hardware or extensive data collection infrastructure. Educational institutions can leverage these findings to develop early warning systems that identify students at risk of fatigue-related academic difficulties, triggering appropriate support interventions.

The research contributes to digital health innovation by demonstrating the potential of linguistic analysis for health monitoring applications. The superior performance of linguistic features compared to traditional quantitative metrics suggests a fundamental shift in how digital health applications should be designed and implemented. The title-only analysis approach offers a pathway for developing privacy-preserving health monitoring systems that require minimal data collection while maintaining substantial predictive accuracy, addressing growing concerns about data privacy in digital health applications.

While this study focuses on student populations, the methodological innovations have broader implications for clinical and therapeutic applications. The SHAP-enhanced interpretability framework provides a model for developing clinically acceptable AI systems in healthcare contexts, where the ability to provide clear, theoretically grounded explanations for predictions is essential for clinical adoption and regulatory approval of AI-based health monitoring systems.
