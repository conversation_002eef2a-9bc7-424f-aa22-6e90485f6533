# 1. INTRODUCTION

In the rapidly evolving digital era, university students face increasingly complex challenges in balancing physical activity and academic productivity. The predominant sedentary lifestyle among students, particularly those engaged in information technology fields, has become a serious concern in the context of cardiovascular health and fatigue management. This lifestyle shift has created an urgent need for innovative approaches to monitor and predict student well-being using digital health technologies.

Cardiovascular activity plays a crucial role in maintaining both physical and mental health among university students. Research demonstrates that regular physical activity can enhance cognitive function, reduce stress levels, and improve academic productivity (Güneş & Demirer, 2022; <PERSON><PERSON> et al., 2024). However, many students struggle to maintain consistent physical activity routines due to high academic demands and ineffective time management strategies (<PERSON><PERSON><PERSON> et al., 2023). This challenge represents a significant barrier to maintaining optimal health and academic performance among the student population.

Fatigue represents a multidimensional phenomenon that significantly impacts academic performance and quality of life among university students. Fatigue is not merely related to physical tiredness, but also encompasses mental and emotional aspects that can affect motivation, concentration, and decision-making capabilities (<PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2022). Early identification of fatigue risk has become crucial for preventing broader negative impacts on student well-being and academic success.

The advancement of wearable technology and mobile applications has opened new opportunities for real-time monitoring of physical activity and productivity patterns. Platforms such as Strava for cardiovascular activity tracking and Pomokit for productivity management provide rich datasets that can be analyzed to understand student behavioral patterns (<PERSON><PERSON><PERSON> et al., 2020). The integration of data from these platforms enables holistic analysis of the relationship between physical activity and academic productivity (<PERSON>shawrab et al., 2022).

Machine learning and artificial intelligence have demonstrated significant potential in health data analysis and risk prediction applications. The application of classification algorithms for fatigue prediction based on physical activity and productivity data can provide valuable insights for preventive interventions. This approach enables personalized health management strategies based on individual student patterns. Research shows that machine learning can identify complex patterns in large datasets that are closely related to individual mental and physical health (Masrom et al., 2023; Olsavszky et al., 2020).

This research becomes relevant due to the lack of studies that integrate cardiovascular activity and academic productivity data for fatigue prediction in Indonesian student populations. Most previous research focuses on single aspects or uses secondary data, thus failing to capture the complexity of relationships between physical activity, productivity, and fatigue in the real context of student life (Siregar et al., 2020).

Recent research in machine learning approaches to mental health has shown promising results in prediction using physical activity data and student behavioral patterns. Zhang et al. (2024) achieved 83.64% accuracy using XGBoost algorithm for GPA prediction, representing the first breakthrough that integrated 24 psychological indicators from the SCL-90 (Symptom Checklist-90) instrument with 7 basic courses data from 229 mechanical engineering students at Henan University of Science and Technology, China. The main strength of this research lies in its holistic approach that combines comprehensive psychological assessment with academic data, creating prediction models that are not only statistically accurate but also clinically relevant for early intervention in supporting student academic success.

Zhang et al. (2024) achieved the highest AUC of 87.2% with 79.2% accuracy using XGBoost with SHAP (SHapley Additive exPlanations). These results demonstrate that the combination of XGBoost models and interpretability techniques like SHAP not only improves prediction performance but also provides deeper understanding of individual feature contributions in determining model output. This approach is highly relevant in mental health contexts, where model transparency and interpretability are important aspects for building user trust and ensuring accountable decision-making.

Lekkas et al. (2021) achieved 75.5% AUC with 70.2% accuracy using ensemble machine learning for suicide ideation prediction in 52 German adolescents through Instagram activity analysis, representing pioneering innovation in integrating social media data with mental health prediction. This shows that digital behavioral patterns can serve as early warning indicators for high-risk mental health conditions. However, despite these advances, significant gaps remain in current approaches, particularly in the integration of multi-modal data and the development of interpretable models for practical clinical application.

Based on the identified research gaps, this study formulates three main research questions: (1) What factors are most significant in predicting fatigue risk in students? (2) How effective are machine learning models in classifying fatigue risk levels based on cardiovascular activity and productivity data? (3) Can title-only analysis provide accurate fatigue prediction without requiring complete quantitative data?

This study aims to address these questions through three primary objectives. First, we identify the main predictive factors for fatigue risk through feature selection analysis. Second, we develop and evaluate machine learning models for fatigue classification using integrated cardiovascular activity and academic productivity data. Third, we explore the effectiveness of title-only analysis for fatigue prediction, investigating whether linguistic analysis can provide meaningful insights with minimal data collection requirements.

The expected benefits of this research are threefold. First, it provides replicable datasets and methodology, offers insights for developing preventive fatigue interventions, and supports further research in digital health. Second, it introduces novel approaches in analyzing physical activity and productivity data, develops research protocols for longitudinal studies based on wearable technology, and provides baseline methodology for similar future research. Third, it explores the application of classification algorithms for fatigue prediction, develops feature engineering techniques specific to student activity data, and contributes to the development of AI-driven health monitoring systems.

This research makes several novel contributions to digital health monitoring and computational linguistics. The primary contribution is the introduction of a SHAP-enhanced feature selection framework that provides theoretically grounded, interpretable insights into fatigue prediction mechanisms. We present a paradigm-shifting finding regarding the dominance of linguistic features in fatigue prediction, demonstrating that cognitive-linguistic patterns extracted from brief activity descriptions provide superior predictive power compared to traditional quantitative metrics. Additionally, we present the first comprehensive validation of title-only analysis for health prediction, demonstrating that meaningful health insights can be obtained from minimal data collection, addressing major barriers to widespread implementation of digital health monitoring systems.

The research scope encompasses several key limitations that define the study boundaries. First, this study uses a cross-sectional design with limited longitudinal elements to analyze student activities observationally, based on primary data collected directly from students through Strava (physical activity) and Pomokit (academic productivity) applications, then analyzed using machine learning algorithms to classify fatigue risk. It should be noted that the data is self-reported, creating potential bias, and does not include activities outside digital platforms. Second, the analysis uses only data from Strava and Pomokit platforms, employing supervised learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, XGBoost) with feature engineering, feature selection, and evaluation using standard classification metrics. Third, the research focuses on Indonesian students who actively use activity tracking and productivity applications, with participants having sufficient digital literacy and self-monitoring motivation. Results are only relevant for student groups with similar characteristics, particularly from certain socioeconomic backgrounds. Fourth, data collection occurs within a limited time period and analyzes weekly activity patterns, not covering long-term data or seasonal variations, but focusing on developing prediction models for real-time monitoring applications.

The methodological approach integrates SHAP analysis to provide theoretically grounded, model-agnostic explanations based on cooperative game theory principles, ensuring consistent feature attribution across different algorithms. The development of comprehensive linguistic feature extraction methods represents a key innovation, introducing systematic approaches for extracting meaningful features from user-generated activity descriptions. This approach addresses the challenge of capturing cognitive and emotional states that may not be reflected in traditional quantitative behavioral metrics, offering new possibilities for non-intrusive digital health monitoring applications.
