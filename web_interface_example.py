#!/usr/bin/env python3
"""
Contoh tampilan web sederhana untuk prediksi risiko k<PERSON>lahan
Menggunakan Streamlit untuk demo interface
"""

import streamlit as st
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go

# Add src to path
sys.path.append('src')

try:
    from utils.data_utils import load_model_artifacts
    MODEL_AVAILABLE = True
except:
    MODEL_AVAILABLE = False

# Page config
st.set_page_config(
    page_title="Fatigue Risk Predictor",
    page_icon="🤖",
    layout="wide"
)

def load_prediction_model():
    """Load the trained model"""
    if not MODEL_AVAILABLE:
        return None, None, None, None
    
    try:
        artifacts = load_model_artifacts("results/clean_production_model")
        model = artifacts['pipeline']
        features = artifacts['features']
        label_encoder = artifacts['label_encoder']
        metadata = artifacts['metadata']
        return model, features, label_encoder, metadata
    except Exception as e:
        st.error(f"Failed to load model: {str(e)}")
        return None, None, None, None

def create_input_form(features):
    """Create input form for all required features"""
    
    st.header("📋 Input Data untuk Prediksi")
    
    # Organize features into categories for better UX
    productivity_features = [f for f in features if 'productivity' in f or 'activity_points' in f]
    time_features = [f for f in features if 'time' in f or 'efficiency' in f]
    strava_features = [f for f in features if 'strava' in f]
    pomokit_features = [f for f in features if 'pomokit' in f]
    other_features = [f for f in features if f not in productivity_features + time_features + strava_features + pomokit_features]
    
    input_data = {}
    
    # Create tabs for different categories
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["🎯 Produktivitas", "⏰ Waktu", "🏃 Strava", "📚 Pomokit", "📊 Lainnya"])
    
    with tab1:
        st.subheader("Data Produktivitas")
        for feature in productivity_features:
            if 'points' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=200.0, value=85.5, step=0.1,
                    help="Poin produktivitas pengguna (0-200)"
                )
            else:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=500.0, value=120.0, step=0.1
                )
    
    with tab2:
        st.subheader("Data Waktu & Efisiensi")
        for feature in time_features:
            if 'minutes' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0, max_value=10000, value=1800, step=1,
                    help="Waktu dalam menit"
                )
            elif 'efficiency' in feature:
                input_data[feature] = st.slider(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=1.0, value=0.82, step=0.01,
                    help="Efisiensi (0.0 - 1.0)"
                )
            else:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=100.0, value=50.0, step=0.1
                )
    
    with tab3:
        st.subheader("Data Strava")
        for feature in strava_features:
            if 'count' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0, max_value=100, value=12, step=1,
                    help="Jumlah aktivitas/judul Strava"
                )
            elif 'words' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0, max_value=50, value=10, step=1,
                    help="Jumlah kata unik"
                )
            elif 'length' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=100.0, value=18.5, step=0.1,
                    help="Panjang rata-rata judul"
                )
            else:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=1000.0, value=100.0, step=0.1
                )
    
    with tab4:
        st.subheader("Data Pomokit")
        for feature in pomokit_features:
            if 'count' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0, max_value=100, value=15, step=1,
                    help="Jumlah aktivitas Pomokit"
                )
            elif 'words' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0, max_value=50, value=10, step=1,
                    help="Jumlah kata unik Pomokit"
                )
            elif 'length' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=100.0, value=25.3, step=0.1,
                    help="Panjang rata-rata judul Pomokit"
                )
            else:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=1000.0, value=100.0, step=0.1
                )
    
    with tab5:
        st.subheader("Data Lainnya")
        for feature in other_features:
            if 'rate' in feature:
                input_data[feature] = st.slider(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=1.0, value=0.85, step=0.01,
                    help="Tingkat pencapaian (0.0 - 1.0)"
                )
            elif 'ratio' in feature:
                input_data[feature] = st.slider(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=2.0, value=0.75, step=0.01,
                    help="Rasio keseimbangan"
                )
            elif 'balance' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=1000.0, value=150.0, step=0.1,
                    help="Saldo gamifikasi"
                )
            elif 'distance' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=100.0, value=5.4, step=0.1,
                    help="Jarak dalam kilometer"
                )
            elif 'cycles' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=20.0, value=3.2, step=0.1,
                    help="Rata-rata siklus aktivitas"
                )
            elif 'diversity' in feature:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=50.0, value=8.5, step=0.1,
                    help="Tingkat keragaman"
                )
            else:
                input_data[feature] = st.number_input(
                    f"{feature.replace('_', ' ').title()}", 
                    min_value=0.0, max_value=1000.0, value=100.0, step=0.1
                )
    
    return input_data

def display_prediction_results(predicted_class, probabilities, metadata):
    """Display prediction results with visualizations"""
    
    st.header("🔮 Hasil Prediksi")
    
    # Main prediction result
    col1, col2, col3 = st.columns(3)
    
    # Color coding for risk levels
    colors = {
        'low_risk': '#28a745',      # Green
        'medium_risk': '#ffc107',   # Yellow
        'high_risk': '#dc3545'      # Red
    }
    
    risk_labels = {
        'low_risk': 'Risiko Rendah',
        'medium_risk': 'Risiko Sedang', 
        'high_risk': 'Risiko Tinggi'
    }
    
    with col1:
        st.metric(
            label="🎯 Prediksi Risiko Kelelahan",
            value=risk_labels[predicted_class],
            delta=f"Confidence: {probabilities[predicted_class]*100:.1f}%"
        )
    
    with col2:
        st.metric(
            label="🤖 Model Accuracy",
            value=f"{metadata.get('accuracy', 0)*100:.1f}%",
            delta="Random Forest"
        )
    
    with col3:
        max_prob = max(probabilities.values())
        confidence_level = "Tinggi" if max_prob > 0.8 else "Sedang" if max_prob > 0.6 else "Rendah"
        st.metric(
            label="📊 Confidence Level",
            value=confidence_level,
            delta=f"{max_prob*100:.1f}%"
        )
    
    # Probability visualization
    st.subheader("📈 Distribusi Probabilitas")
    
    # Create probability chart
    prob_df = pd.DataFrame([
        {'Risk Level': risk_labels[k], 'Probability': v*100, 'Color': colors[k]} 
        for k, v in probabilities.items()
    ])
    
    fig = px.bar(
        prob_df, 
        x='Risk Level', 
        y='Probability',
        color='Risk Level',
        color_discrete_map={
            'Risiko Rendah': colors['low_risk'],
            'Risiko Sedang': colors['medium_risk'],
            'Risiko Tinggi': colors['high_risk']
        },
        title="Probabilitas untuk Setiap Tingkat Risiko"
    )
    fig.update_layout(showlegend=False)
    fig.update_yaxis(title="Probabilitas (%)")
    st.plotly_chart(fig, use_container_width=True)
    
    # Recommendations based on prediction
    st.subheader("💡 Rekomendasi")
    
    if predicted_class == 'high_risk':
        st.error("""
        **⚠️ RISIKO TINGGI TERDETEKSI**
        - Segera istirahat dan kurangi aktivitas berat
        - Monitor kondisi kesehatan secara berkala
        - Pertimbangkan konsultasi dengan profesional kesehatan
        - Hindari aktivitas yang membutuhkan konsentrasi tinggi
        """)
    elif predicted_class == 'medium_risk':
        st.warning("""
        **⚡ RISIKO SEDANG**
        - Perhatikan pola istirahat dan aktivitas
        - Lakukan aktivitas ringan untuk recovery
        - Monitor gejala kelelahan
        - Pertimbangkan mengurangi beban kerja sementara
        """)
    else:
        st.success("""
        **✅ RISIKO RENDAH**
        - Kondisi baik, lanjutkan aktivitas normal
        - Tetap jaga pola istirahat yang sehat
        - Monitor kondisi secara berkala
        - Pertahankan gaya hidup sehat
        """)

def main():
    """Main Streamlit app"""
    
    st.title("🤖 Fatigue Risk Prediction System")
    st.markdown("---")
    
    # Load model
    model, features, label_encoder, metadata = load_prediction_model()
    
    if not model:
        st.error("❌ Model tidak dapat dimuat. Pastikan model sudah dilatih dengan menjalankan: `python main1.py --ml-only`")
        return
    
    # Sidebar with model info
    with st.sidebar:
        st.header("ℹ️ Model Information")
        st.write(f"**Algorithm:** {metadata.get('algorithm_name', 'Unknown')}")
        st.write(f"**Accuracy:** {metadata.get('accuracy', 0)*100:.2f}%")
        st.write(f"**Features:** {len(features)}")
        st.write(f"**Classes:** Low, Medium, High Risk")
        
        st.markdown("---")
        st.header("📖 Panduan Penggunaan")
        st.write("""
        1. Isi semua field input data
        2. Klik tombol 'Prediksi Risiko'
        3. Lihat hasil dan rekomendasi
        4. Gunakan hasil untuk pengambilan keputusan
        """)
    
    # Create input form
    input_data = create_input_form(features)
    
    # Prediction button
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🔮 Prediksi Risiko Kelelahan", type="primary", use_container_width=True):
            try:
                # Prepare data for prediction
                df = pd.DataFrame([input_data])
                X = df[features]
                
                # Make prediction
                prediction = model.predict(X)[0]
                probabilities = model.predict_proba(X)[0]
                predicted_class = label_encoder.inverse_transform([prediction])[0]
                
                # Create probabilities dict
                classes = label_encoder.classes_
                prob_dict = {cls: prob for cls, prob in zip(classes, probabilities)}
                
                # Display results
                st.markdown("---")
                display_prediction_results(predicted_class, prob_dict, metadata)
                
            except Exception as e:
                st.error(f"❌ Error dalam prediksi: {str(e)}")

if __name__ == "__main__":
    main()
