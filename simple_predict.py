#!/usr/bin/env python3
"""
Script sederhana untuk prediksi cepat
"""

import sys
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append('src')
from utils.data_utils import load_model_artifacts

def quick_predict():
    """Fungsi prediksi cepat"""
    
    print("🤖 QUICK FATIGUE RISK PREDICTION")
    print("="*40)
    
    # Load model
    print("📂 Loading model...")
    try:
        artifacts = load_model_artifacts("results/clean_production_model")
        model = artifacts['pipeline']
        features = artifacts['features']
        label_encoder = artifacts['label_encoder']
        metadata = artifacts['metadata']
        
        print(f"✅ Model loaded: {metadata.get('algorithm_name')} ({metadata.get('accuracy', 0)*100:.1f}% accuracy)")
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        print("💡 Make sure to train the model first: python main1.py --ml-only")
        return
    
    # Show required features
    print(f"\n📋 Required features ({len(features)}):")
    for i, feature in enumerate(features[:10], 1):  # Show first 10
        print(f"  {i:2d}. {feature}")
    if len(features) > 10:
        print(f"  ... and {len(features)-10} more features")
    
    # Example prediction with sample data
    print("\n🔮 Example prediction with sample data:")
    
    # Load some real data for example
    data_path = "dataset/processed/safe_ml_fatigue_dataset.csv"
    if Path(data_path).exists():
        # Load first row as example
        data = pd.read_csv(data_path)
        sample = data[features].iloc[0:1]  # First row
        actual = data['fatigue_risk'].iloc[0] if 'fatigue_risk' in data.columns else 'Unknown'
        
        # Make prediction
        prediction = model.predict(sample)[0]
        probabilities = model.predict_proba(sample)[0]
        predicted_class = label_encoder.inverse_transform([prediction])[0]
        
        # Show results
        print(f"📊 Prediction: {predicted_class}")
        print(f"📊 Actual: {actual}")
        print("📈 Probabilities:")
        
        classes = label_encoder.classes_
        for cls, prob in zip(classes, probabilities):
            print(f"   • {cls}: {prob:.4f} ({prob*100:.2f}%)")
        
        # Show sample data values (first few features)
        print(f"\n📋 Sample data (first 5 features):")
        for feature in features[:5]:
            value = sample[feature].iloc[0]
            print(f"   • {feature}: {value}")
        
    else:
        print("⚠️  No sample data found")
    
    print(f"\n💡 To use with your own data:")
    print(f"   1. Prepare CSV with required features")
    print(f"   2. Use predict_with_model.py for full functionality")

if __name__ == "__main__":
    quick_predict()
