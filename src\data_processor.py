"""
Data Processing Module
Clean code implementation for processing raw activity and productivity data

VISUALIZATION FEATURES:
This module includes comprehensive visualization capabilities built directly into the DataProcessor class.

Available Visualization Methods:
1. visualize_data_overview() - Overview of key metrics distributions and relationships
2. visualize_correlation_matrix() - Correlation heatmap of numeric variables
3. visualize_time_series() - Time series analysis of key metrics over weeks
4. visualize_user_analysis() - User-specific analysis and top performers
5. visualize_gamification_analysis() - Gamification metrics analysis
6. generate_data_quality_report() - Comprehensive data quality assessment
7. create_comprehensive_report() - All visualizations + summary statistics

USAGE EXAMPLES:

# Basic usage - Process data and create all visualizations:
processor = DataProcessor()
data = processor.process_all()
processor.create_comprehensive_report(data, save_plots=True)

# Individual visualizations:
processor.visualize_data_overview(data, save_plots=True)
processor.visualize_correlation_matrix(data, save_plots=False)  # Don't save, just generate
processor.visualize_user_analysis(data)

# Data quality analysis only:
processor.generate_data_quality_report(data)

# Command line usage:
# python data_processor.py              # Run comprehensive report
# python data_processor.py individual   # Run individual visualization examples

All plots are saved to dataset/processed/ directory as PNG files with 300 DPI resolution.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Tuple
import logging
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

from constants import DataConstants

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Clean data processing class following SOLID principles
    """
    
    def __init__(self, raw_data_path: str = DataConstants.DEFAULT_RAW_PATH):
        """
        Initialize data processor

        Args:
            raw_data_path: Path to raw data directory
        """
        self.raw_data_path = Path(raw_data_path)
        self.processed_data_path = Path(DataConstants.DEFAULT_PROCESSED_PATH)
        self.processed_data_path.mkdir(parents=True, exist_ok=True)
        
    def load_raw_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Load raw datasets from original files
        
        Returns:
            Tuple of (strava_data, pomokit_data)
        """
        try:
            # Load original datasets
            strava_path = self.raw_data_path / "strava.csv"
            pomokit_path = self.raw_data_path / "pomokit.csv"
            
            logger.info(f"Loading Strava data from {strava_path}")
            strava_data = pd.read_csv(strava_path)
            
            logger.info(f"Loading Pomokit data from {pomokit_path}")
            pomokit_data = pd.read_csv(pomokit_path)
            
            logger.info(f"Loaded {len(strava_data)} Strava records and {len(pomokit_data)} Pomokit records")
            
            return strava_data, pomokit_data
            
        except FileNotFoundError as e:
            logger.error(f"Data file not found: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def clean_strava_data(self, strava_data: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and standardize Strava activity data

        Args:
            strava_data: Raw Strava DataFrame

        Returns:
            Cleaned Strava DataFrame
        """
        logger.info("Cleaning Strava data...")

        # Create copy to avoid modifying original
        df = strava_data.copy()

        # Standardize column names
        df.columns = df.columns.str.lower().str.replace(' ', '_')

        # Convert date columns
        if 'date_time_wib' in df.columns:
            df['date'] = pd.to_datetime(df['date_time_wib'])
        elif 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        elif 'activity_date' in df.columns:
            df['date'] = pd.to_datetime(df['activity_date'])

        # Add year_week column for time series analysis
        if 'date' in df.columns:
            df['year_week'] = df['date'].dt.isocalendar().year * 100 + df['date'].dt.isocalendar().week

        # Clean title column
        if 'title' in df.columns:
            df['title'] = df['title'].astype(str).str.strip()
            df['title'] = df['title'].replace('nan', '')
            logger.info("Cleaned title column for Strava data")

        # Clean numeric columns - adjust for actual Strava columns
        numeric_columns = ['distance', 'moving_time']
        for col in numeric_columns:
            if col in df.columns:
                # Handle distance with 'km' suffix
                if col == 'distance' and df[col].dtype == 'object':
                    df[col] = df[col].str.replace(' km', '').str.replace(',', '.')
                # Handle time format
                elif col == 'moving_time' and df[col].dtype == 'object':
                    # Convert time format like "41m 21s" to minutes
                    df[col] = self._convert_time_to_minutes(df[col])
                    continue

                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove invalid records
        initial_count = len(df)
        df = df.dropna(subset=['date'])
        df = df[df['distance'] > 0] if 'distance' in df.columns else df

        logger.info(f"Cleaned Strava data: {initial_count} → {len(df)} records")

        return df
    
    def clean_pomokit_data(self, pomokit_data: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and standardize Pomokit productivity data

        Args:
            pomokit_data: Raw Pomokit DataFrame

        Returns:
            Cleaned Pomokit DataFrame
        """
        logger.info("Cleaning Pomokit data...")

        # Create copy to avoid modifying original
        df = pomokit_data.copy()

        # Standardize column names
        df.columns = df.columns.str.lower().str.replace(' ', '_')

        # Convert date columns
        if 'date_time_wib' in df.columns:
            df['date'] = pd.to_datetime(df['date_time_wib'])
        elif 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        elif 'session_date' in df.columns:
            df['date'] = pd.to_datetime(df['session_date'])

        # Add year_week column for time series analysis
        if 'date' in df.columns:
            df['year_week'] = df['date'].dt.isocalendar().year * 100 + df['date'].dt.isocalendar().week

        # Clean title column
        if 'title' in df.columns:
            df['title'] = df['title'].astype(str).str.strip()
            df['title'] = df['title'].replace('nan', '')
            logger.info("Cleaned title column for Pomokit data")

        # Clean numeric columns - adjust for actual Pomokit columns
        numeric_columns = ['cycle']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove invalid records
        initial_count = len(df)
        df = df.dropna(subset=['date'])

        logger.info(f"Cleaned Pomokit data: {initial_count} → {len(df)} records")

        return df
    
    def create_weekly_aggregation(self, strava_data: pd.DataFrame, 
                                pomokit_data: pd.DataFrame) -> pd.DataFrame:
        """
        Create weekly aggregated dataset from daily data
        
        Args:
            strava_data: Cleaned Strava data
            pomokit_data: Cleaned Pomokit data
            
        Returns:
            Weekly aggregated DataFrame
        """
        logger.info("Creating weekly aggregation...")
        
        # Aggregate Strava data by week
        strava_weekly = self._aggregate_strava_weekly(strava_data)
        
        # Aggregate Pomokit data by week
        pomokit_weekly = self._aggregate_pomokit_weekly(pomokit_data)
        
        # Merge weekly data
        weekly_data = self._merge_weekly_data(strava_weekly, pomokit_weekly)
        
        logger.info(f"Created weekly dataset with {len(weekly_data)} observations")
        
        return weekly_data

    def _convert_time_to_minutes(self, time_series: pd.Series) -> pd.Series:
        """Convert time format like '41m 21s' to total minutes"""
        def parse_time(time_str):
            if pd.isna(time_str):
                return 0
            try:
                time_str = str(time_str).lower()
                minutes = 0
                if 'h' in time_str:
                    hours = int(time_str.split('h')[0])
                    minutes += hours * 60
                    time_str = time_str.split('h')[1] if 'h' in time_str else time_str
                if 'm' in time_str:
                    mins = int(time_str.split('m')[0])
                    minutes += mins
                if 's' in time_str and 'm' in time_str:
                    # Handle seconds if present
                    pass
                return minutes
            except:
                return 0

        return time_series.apply(parse_time)

    def _aggregate_strava_weekly(self, strava_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate Strava data by week"""

        # Group by phonenumber and year_week
        grouping_cols = ['phonenumber', 'year_week']

        # Use actual column names from Strava data
        agg_dict = {}
        if 'distance' in strava_data.columns:
            agg_dict['distance'] = ['sum', 'mean', 'count']
        if 'moving_time' in strava_data.columns:
            agg_dict['moving_time'] = ['sum', 'mean']
        if 'title' in strava_data.columns:
            agg_dict['title'] = lambda x: ' | '.join(x.dropna().astype(str))

        weekly_strava = strava_data.groupby(grouping_cols).agg(agg_dict).reset_index()

        # Flatten column names
        weekly_strava.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0]
                               for col in weekly_strava.columns]

        # Rename columns for clarity
        rename_dict = {
            'distance_sum': 'total_distance_km',
            'distance_mean': 'avg_distance_km',
            'distance_count': 'activity_days',
            'moving_time_sum': 'total_time_minutes',
            'moving_time_mean': 'avg_time_minutes',
            'title_<lambda>': 'strava_activities_titles'
        }

        weekly_strava = weekly_strava.rename(columns=rename_dict)

        return weekly_strava
    
    def _aggregate_pomokit_weekly(self, pomokit_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate Pomokit data by week"""

        grouping_cols = ['phonenumber', 'year_week']

        # Use actual column names from Pomokit data
        agg_dict = {}
        if 'cycle' in pomokit_data.columns:
            agg_dict['cycle'] = ['sum', 'mean', 'count']
        if 'title' in pomokit_data.columns:
            agg_dict['title'] = lambda x: ' | '.join(x.dropna().astype(str))

        weekly_pomokit = pomokit_data.groupby(grouping_cols).agg(agg_dict).reset_index()

        # Flatten column names
        weekly_pomokit.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0]
                                for col in weekly_pomokit.columns]

        # Rename columns for clarity
        rename_dict = {
            'cycle_sum': 'total_cycles',
            'cycle_mean': 'avg_cycles',
            'cycle_count': 'work_days',
            'title_<lambda>': 'pomokit_activities_titles'
        }

        weekly_pomokit = weekly_pomokit.rename(columns=rename_dict)

        return weekly_pomokit

    def add_title_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add advanced title-based features to the dataset

        Args:
            data: Weekly aggregated data with title columns

        Returns:
            Data with additional title-based features
        """
        logger.info("Adding title-based features...")

        df = data.copy()

        # Process Strava titles
        if 'strava_activities_titles' in df.columns:
            df['strava_title_count'] = df['strava_activities_titles'].apply(
                lambda x: len(str(x).split(' | ')) if pd.notna(x) and str(x) != '' else 0
            )
            df['strava_title_length'] = df['strava_activities_titles'].apply(
                lambda x: len(str(x)) if pd.notna(x) else 0
            )
            df['strava_unique_words'] = df['strava_activities_titles'].apply(
                lambda x: len(set(str(x).lower().split())) if pd.notna(x) and str(x) != '' else 0
            )

        # Process Pomokit titles
        if 'pomokit_activities_titles' in df.columns:
            df['pomokit_title_count'] = df['pomokit_activities_titles'].apply(
                lambda x: len(str(x).split(' | ')) if pd.notna(x) and str(x) != '' else 0
            )
            df['pomokit_title_length'] = df['pomokit_activities_titles'].apply(
                lambda x: len(str(x)) if pd.notna(x) else 0
            )
            df['pomokit_unique_words'] = df['pomokit_activities_titles'].apply(
                lambda x: len(set(str(x).lower().split())) if pd.notna(x) and str(x) != '' else 0
            )

        # Combined title features
        if 'strava_activities_titles' in df.columns and 'pomokit_activities_titles' in df.columns:
            df['combined_titles'] = df['strava_activities_titles'].astype(str) + ' | ' + df['pomokit_activities_titles'].astype(str)
            df['total_title_diversity'] = df['strava_unique_words'] + df['pomokit_unique_words']
            df['title_balance_ratio'] = np.where(
                df['pomokit_title_length'] == 0,
                np.inf,
                df['strava_title_length'] / df['pomokit_title_length']
            )

        logger.info("Title-based features added successfully")

        return df



    def _merge_weekly_data(self, strava_weekly: pd.DataFrame,
                          pomokit_weekly: pd.DataFrame) -> pd.DataFrame:
        """Merge weekly Strava and Pomokit data"""
        
        # Merge on phonenumber and year_week
        merged_data = pd.merge(
            strava_weekly,
            pomokit_weekly,
            on=['phonenumber', 'year_week'],
            how='inner'
        )
        
        # Calculate additional metrics
        merged_data = self._calculate_derived_metrics(merged_data)
        
        return merged_data
    
    def _calculate_derived_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate derived metrics and consistency scores"""
        
        df = data.copy()
        
        # Calculate consistency score (original formula)
        # Simple average of activity days and work days
        physical_consistency = (df['activity_days'].clip(upper=2)) / 2
        work_consistency = (df['work_days'].clip(upper=5)) / 5

        df['consistency_score'] = (physical_consistency + work_consistency) / 2
        
        # Calculate efficiency metrics
        df['weekly_efficiency'] = np.where(
            df['work_days'] == 0,
            np.nan,  # atau 0 jika ingin aman
            df['total_cycles'] / df['work_days']
        )
        
        return df
    
    def add_gamification_variables(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add gamification variables to the dataset
        
        Args:
            data: Weekly aggregated data
            
        Returns:
            Data with gamification variables added
        """
        logger.info("Adding gamification variables...")
        
        df = data.copy()
        
        # Activity points (max points for weekly distance target)
        df['activity_points'] = np.minimum(
            (df['total_distance_km'] / DataConstants.WEEKLY_DISTANCE_TARGET) * DataConstants.MAX_ACTIVITY_POINTS,
            DataConstants.MAX_ACTIVITY_POINTS
        )

        # Productivity points (max points for weekly cycles target)
        df['productivity_points'] = np.minimum(
            (df['total_cycles'] / DataConstants.WEEKLY_CYCLES_TARGET) * DataConstants.MAX_PRODUCTIVITY_POINTS,
            DataConstants.MAX_PRODUCTIVITY_POINTS
        )

        # Achievement rate (percentage of maximum possible points)
        # Calculate directly from activity and productivity points
        total_points = df['activity_points'] + df['productivity_points']
        df['achievement_rate'] = total_points / DataConstants.MAX_TOTAL_GAMIFICATION_POINTS
        
        # Gamification balance (how balanced between activity and productivity)
        df['gamification_balance'] = abs(df['activity_points'] - df['productivity_points'])
        
        logger.info("Gamification variables added successfully")
        
        return df

    def generate_user_identities(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate anonymized user identities based on unique phone numbers

        Args:
            data: DataFrame with phonenumber column

        Returns:
            DataFrame with added identity column
        """
        logger.info("Generating anonymized user identities...")

        df = data.copy()

        # Get unique phone numbers and create mapping
        unique_phones = sorted(df['phonenumber'].unique())
        phone_to_identity = {phone: f"user-{i+1}" for i, phone in enumerate(unique_phones)}

        # Add identity column
        df['identity'] = df['phonenumber'].map(phone_to_identity)

        logger.info(f"Generated identities for {len(unique_phones)} unique users")

        return df

    def finalize_dataset(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Finalize dataset by removing phonenumber and reordering columns

        Args:
            data: DataFrame with phonenumber and identity columns

        Returns:
            DataFrame with identity as first column and phonenumber removed
        """
        logger.info("Finalizing dataset: removing phonenumber and reordering columns...")

        df = data.copy()

        # Remove phonenumber column
        if 'phonenumber' in df.columns:
            df = df.drop('phonenumber', axis=1)
            logger.info("Removed phonenumber column")

        # Reorder columns to put identity first
        if 'identity' in df.columns:
            cols = df.columns.tolist()
            cols.remove('identity')
            cols.insert(0, 'identity')
            df = df[cols]
            logger.info("Moved identity column to first position")

        logger.info(f"Final dataset shape: {df.shape}")

        return df

    def save_processed_data(self, data: pd.DataFrame, filename: str) -> None:
        """
        Save processed data to file
        
        Args:
            data: Processed DataFrame
            filename: Output filename
        """
        output_path = self.processed_data_path / filename
        data.to_csv(output_path, index=False)
        logger.info(f"Saved processed data to {output_path}")
    
    def process_all(self) -> pd.DataFrame:
        """
        Execute complete data processing pipeline

        Returns:
            Final processed dataset
        """
        logger.info("Starting complete data processing pipeline...")

        # Load raw data
        strava_data, pomokit_data = self.load_raw_data()

        # Clean data
        strava_clean = self.clean_strava_data(strava_data)
        pomokit_clean = self.clean_pomokit_data(pomokit_data)

        # Create weekly aggregation
        weekly_data = self.create_weekly_aggregation(strava_clean, pomokit_clean)

        # Add title-based features
        weekly_data = self.add_title_features(weekly_data)

        # Add gamification variables
        final_data = self.add_gamification_variables(weekly_data)

        # Generate anonymized user identities
        final_data = self.generate_user_identities(final_data)

        # Remove phonenumber column and reorder columns
        final_data = self.finalize_dataset(final_data)

        # Save processed data
        self.save_processed_data(final_data, DataConstants.OUTPUT_FILENAME)

        logger.info("Data processing pipeline completed successfully")

        return final_data

    def visualize_data_overview(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create comprehensive data overview visualizations

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating data overview visualizations...")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Cardiovascular Activity & Productivity Data Overview', fontsize=16, fontweight='bold')

        # 1. Distribution of total distance
        if 'total_distance_km' in data.columns:
            axes[0, 0].hist(data['total_distance_km'].dropna(), bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 0].set_title('Distribution of Weekly Total Distance')
            axes[0, 0].set_xlabel('Total Distance (km)')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].grid(True, alpha=0.3)

        # 2. Distribution of total cycles
        if 'total_cycles' in data.columns:
            axes[0, 1].hist(data['total_cycles'].dropna(), bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
            axes[0, 1].set_title('Distribution of Weekly Total Cycles')
            axes[0, 1].set_xlabel('Total Cycles')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].grid(True, alpha=0.3)

        # 3. Activity vs Productivity scatter plot
        if 'total_distance_km' in data.columns and 'total_cycles' in data.columns:
            scatter = axes[0, 2].scatter(data['total_distance_km'], data['total_cycles'],
                                       alpha=0.6, c=data.index, cmap='viridis')
            axes[0, 2].set_title('Physical Activity vs Productivity')
            axes[0, 2].set_xlabel('Total Distance (km)')
            axes[0, 2].set_ylabel('Total Cycles')
            axes[0, 2].grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=axes[0, 2], label='Data Point Index')

        # 4. Consistency score distribution
        if 'consistency_score' in data.columns:
            axes[1, 0].hist(data['consistency_score'].dropna(), bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
            axes[1, 0].set_title('Distribution of Consistency Scores')
            axes[1, 0].set_xlabel('Consistency Score')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].grid(True, alpha=0.3)

        # 5. Achievement rate distribution
        if 'achievement_rate' in data.columns:
            axes[1, 1].hist(data['achievement_rate'].dropna(), bins=15, alpha=0.7, color='gold', edgecolor='black')
            axes[1, 1].set_title('Distribution of Achievement Rates')
            axes[1, 1].set_xlabel('Achievement Rate')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].grid(True, alpha=0.3)

        # 6. Weekly efficiency boxplot by user
        if 'weekly_efficiency' in data.columns and 'identity' in data.columns:
            # Get top 10 users by data points for cleaner visualization
            top_users = data['identity'].value_counts().head(10).index
            filtered_data = data[data['identity'].isin(top_users)]

            if len(filtered_data) > 0:
                box_data = [filtered_data[filtered_data['identity'] == user]['weekly_efficiency'].dropna()
                           for user in top_users]
                axes[1, 2].boxplot(box_data, labels=[f'U{i+1}' for i in range(len(top_users))])
                axes[1, 2].set_title('Weekly Efficiency by Top Users')
                axes[1, 2].set_xlabel('User')
                axes[1, 2].set_ylabel('Weekly Efficiency')
                axes[1, 2].tick_params(axis='x', rotation=45)
                axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'data_overview.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved data overview plot to {plot_path}")

        plt.close()

    def visualize_correlation_matrix(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create correlation matrix heatmap for numeric variables

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating correlation matrix visualization...")

        # Select numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()

        # Remove identity-related columns if they exist
        exclude_cols = ['phonenumber', 'year_week']
        numeric_cols = [col for col in numeric_cols if col not in exclude_cols]

        if len(numeric_cols) < 2:
            logger.warning("Not enough numeric columns for correlation matrix")
            return

        # Calculate correlation matrix
        corr_matrix = data[numeric_cols].corr()

        # Create heatmap
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))

        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')

        plt.title('Correlation Matrix of Numeric Variables', fontsize=14, fontweight='bold', pad=20)
        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'correlation_matrix.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved correlation matrix plot to {plot_path}")

        plt.close()

    def visualize_time_series(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create time series visualizations for key metrics

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating time series visualizations...")

        if 'year_week' not in data.columns:
            logger.warning("No year_week column found for time series analysis")
            return

        # Aggregate data by week
        weekly_agg = data.groupby('year_week').agg({
            'total_distance_km': 'mean',
            'total_cycles': 'mean',
            'consistency_score': 'mean',
            'achievement_rate': 'mean'
        }).reset_index()

        # Create time series plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Time Series Analysis of Key Metrics', fontsize=16, fontweight='bold')

        # Convert year_week to datetime for better x-axis
        weekly_agg['date'] = pd.to_datetime(weekly_agg['year_week'].astype(str) + '-1', format='%Y%W-%w')

        # Plot 1: Average weekly distance
        if 'total_distance_km' in weekly_agg.columns:
            axes[0, 0].plot(weekly_agg['date'], weekly_agg['total_distance_km'],
                           marker='o', linewidth=2, markersize=4, color='blue')
            axes[0, 0].set_title('Average Weekly Distance Over Time')
            axes[0, 0].set_ylabel('Distance (km)')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].tick_params(axis='x', rotation=45)

        # Plot 2: Average weekly cycles
        if 'total_cycles' in weekly_agg.columns:
            axes[0, 1].plot(weekly_agg['date'], weekly_agg['total_cycles'],
                           marker='s', linewidth=2, markersize=4, color='red')
            axes[0, 1].set_title('Average Weekly Cycles Over Time')
            axes[0, 1].set_ylabel('Cycles')
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].tick_params(axis='x', rotation=45)

        # Plot 3: Average consistency score
        if 'consistency_score' in weekly_agg.columns:
            axes[1, 0].plot(weekly_agg['date'], weekly_agg['consistency_score'],
                           marker='^', linewidth=2, markersize=4, color='green')
            axes[1, 0].set_title('Average Consistency Score Over Time')
            axes[1, 0].set_ylabel('Consistency Score')
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].tick_params(axis='x', rotation=45)

        # Plot 4: Average achievement rate
        if 'achievement_rate' in weekly_agg.columns:
            axes[1, 1].plot(weekly_agg['date'], weekly_agg['achievement_rate'],
                           marker='d', linewidth=2, markersize=4, color='orange')
            axes[1, 1].set_title('Average Achievement Rate Over Time')
            axes[1, 1].set_ylabel('Achievement Rate')
            axes[1, 1].grid(True, alpha=0.3)
            axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'time_series_analysis.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved time series plot to {plot_path}")

        plt.close()


    def visualize_user_analysis(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create user-specific analysis visualizations

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating user analysis visualizations...")

        if 'identity' not in data.columns:
            logger.warning("No identity column found for user analysis")
            return

        # User statistics
        user_stats = data.groupby('identity').agg({
            'total_distance_km': ['mean', 'sum', 'count'],
            'total_cycles': ['mean', 'sum'],
            'consistency_score': 'mean',
            'achievement_rate': 'mean'
        }).round(2)

        # Flatten column names
        user_stats.columns = ['_'.join(col).strip() for col in user_stats.columns]
        user_stats = user_stats.reset_index()

        # Create visualizations
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('User Analysis Dashboard', fontsize=16, fontweight='bold')

        # Top 10 users by total distance
        top_distance_users = user_stats.nlargest(10, 'total_distance_km_sum')
        axes[0, 0].barh(range(len(top_distance_users)), top_distance_users['total_distance_km_sum'])
        axes[0, 0].set_yticks(range(len(top_distance_users)))
        axes[0, 0].set_yticklabels(top_distance_users['identity'])
        axes[0, 0].set_title('Top 10 Users by Total Distance')
        axes[0, 0].set_xlabel('Total Distance (km)')
        axes[0, 0].grid(True, alpha=0.3)

        # Top 10 users by total cycles
        top_cycles_users = user_stats.nlargest(10, 'total_cycles_sum')
        axes[0, 1].barh(range(len(top_cycles_users)), top_cycles_users['total_cycles_sum'])
        axes[0, 1].set_yticks(range(len(top_cycles_users)))
        axes[0, 1].set_yticklabels(top_cycles_users['identity'])
        axes[0, 1].set_title('Top 10 Users by Total Cycles')
        axes[0, 1].set_xlabel('Total Cycles')
        axes[0, 1].grid(True, alpha=0.3)

        # User consistency vs achievement scatter
        axes[1, 0].scatter(user_stats['consistency_score_mean'], user_stats['achievement_rate_mean'],
                          alpha=0.6, s=60)
        axes[1, 0].set_title('User Consistency vs Achievement Rate')
        axes[1, 0].set_xlabel('Average Consistency Score')
        axes[1, 0].set_ylabel('Average Achievement Rate')
        axes[1, 0].grid(True, alpha=0.3)

        # Activity frequency distribution
        activity_freq = user_stats['total_distance_km_count']
        axes[1, 1].hist(activity_freq, bins=15, alpha=0.7, color='purple', edgecolor='black')
        axes[1, 1].set_title('Distribution of User Activity Frequency')
        axes[1, 1].set_xlabel('Number of Active Weeks')
        axes[1, 1].set_ylabel('Number of Users')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'user_analysis.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved user analysis plot to {plot_path}")

        plt.close()

        # Print top performers
        print("\n" + "="*50)
        print("TOP PERFORMERS SUMMARY")
        print("="*50)
        print(f"Most Active User (Distance): {top_distance_users.iloc[0]['identity']} - {top_distance_users.iloc[0]['total_distance_km_sum']:.1f} km")
        print(f"Most Productive User (Cycles): {top_cycles_users.iloc[0]['identity']} - {top_cycles_users.iloc[0]['total_cycles_sum']:.0f} cycles")

        # Most consistent user
        most_consistent = user_stats.loc[user_stats['consistency_score_mean'].idxmax()]
        print(f"Most Consistent User: {most_consistent['identity']} - {most_consistent['consistency_score_mean']:.3f} score")

        # Highest achiever
        highest_achiever = user_stats.loc[user_stats['achievement_rate_mean'].idxmax()]
        print(f"Highest Achiever: {highest_achiever['identity']} - {highest_achiever['achievement_rate_mean']:.3f} rate")

    def create_comprehensive_report(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create a comprehensive visualization report

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating comprehensive visualization report...")

        print("\n" + "="*60)
        print("CARDIOVASCULAR ACTIVITY & PRODUCTIVITY ANALYSIS REPORT")
        print("="*60)
        print(f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Dataset shape: {data.shape}")
        print(f"Number of unique users: {data['identity'].nunique() if 'identity' in data.columns else 'N/A'}")
        print(f"Date range: {data['year_week'].min() if 'year_week' in data.columns else 'N/A'} to {data['year_week'].max() if 'year_week' in data.columns else 'N/A'}")

        # Generate all visualizations
        self.visualize_data_overview(data, save_plots)
        self.visualize_correlation_matrix(data, save_plots)
        self.visualize_time_series(data, save_plots)
        self.visualize_user_analysis(data, save_plots)
        self.visualize_gamification_analysis(data, save_plots)

        # Summary statistics
        print("\n" + "="*50)
        print("DATASET SUMMARY STATISTICS")
        print("="*50)

        numeric_cols = data.select_dtypes(include=[np.number]).columns
        summary_stats = data[numeric_cols].describe().round(2)
        print(summary_stats)

        # Generate data quality report
        self.generate_data_quality_report(data)

        logger.info("Comprehensive visualization report completed!")

    def visualize_gamification_analysis(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create gamification-specific visualizations

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating gamification analysis visualizations...")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Gamification Analysis Dashboard', fontsize=16, fontweight='bold')

        # 1. Activity Points vs Productivity Points
        if 'activity_points' in data.columns and 'productivity_points' in data.columns:
            scatter = axes[0, 0].scatter(data['activity_points'], data['productivity_points'],
                                       alpha=0.6, c=data['achievement_rate'], cmap='viridis')
            axes[0, 0].set_title('Activity Points vs Productivity Points')
            axes[0, 0].set_xlabel('Activity Points')
            axes[0, 0].set_ylabel('Productivity Points')
            axes[0, 0].grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=axes[0, 0], label='Achievement Rate')

        # 2. Achievement Rate Distribution by Quartiles
        if 'achievement_rate' in data.columns:
            # Create quartile bins
            data['achievement_quartile'] = pd.qcut(data['achievement_rate'], 4, labels=['Q1', 'Q2', 'Q3', 'Q4'])
            quartile_counts = data['achievement_quartile'].value_counts().sort_index()

            axes[0, 1].bar(quartile_counts.index, quartile_counts.values,
                          color=['red', 'orange', 'yellow', 'green'], alpha=0.7)
            axes[0, 1].set_title('Achievement Rate Distribution by Quartiles')
            axes[0, 1].set_xlabel('Achievement Quartile')
            axes[0, 1].set_ylabel('Number of Users')
            axes[0, 1].grid(True, alpha=0.3)

        # 3. Gamification Balance Analysis
        if 'gamification_balance' in data.columns:
            axes[1, 0].hist(data['gamification_balance'], bins=20, alpha=0.7, color='purple', edgecolor='black')
            axes[1, 0].set_title('Gamification Balance Distribution')
            axes[1, 0].set_xlabel('Gamification Balance (Point Difference)')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].grid(True, alpha=0.3)

        # 4. Weekly Efficiency vs Achievement Rate
        if 'weekly_efficiency' in data.columns and 'achievement_rate' in data.columns:
            # Remove outliers for better visualization
            efficiency_clean = data['weekly_efficiency'].dropna()
            achievement_clean = data['achievement_rate'][efficiency_clean.index]

            axes[1, 1].scatter(efficiency_clean, achievement_clean, alpha=0.6, color='orange')
            axes[1, 1].set_title('Weekly Efficiency vs Achievement Rate')
            axes[1, 1].set_xlabel('Weekly Efficiency')
            axes[1, 1].set_ylabel('Achievement Rate')
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'gamification_analysis.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved gamification analysis plot to {plot_path}")

        plt.close()

    def generate_data_quality_report(self, data: pd.DataFrame) -> None:
        """
        Generate a comprehensive data quality report

        Args:
            data: Processed DataFrame to analyze
        """
        logger.info("Generating data quality report...")

        print("\n" + "="*60)
        print("DATA QUALITY REPORT")
        print("="*60)

        # Basic info
        print(f"Dataset Shape: {data.shape}")
        print(f"Memory Usage: {data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

        # Missing values analysis
        print("\n" + "-"*40)
        print("MISSING VALUES ANALYSIS")
        print("-"*40)
        missing_data = data.isnull().sum()
        missing_percent = (missing_data / len(data)) * 100
        missing_df = pd.DataFrame({
            'Missing Count': missing_data,
            'Missing Percentage': missing_percent
        })
        missing_df = missing_df[missing_df['Missing Count'] > 0].sort_values('Missing Count', ascending=False)

        if len(missing_df) > 0:
            print(missing_df)
        else:
            print("No missing values found!")

        # Data types analysis
        print("\n" + "-"*40)
        print("DATA TYPES ANALYSIS")
        print("-"*40)
        dtype_counts = data.dtypes.value_counts()
        print(dtype_counts)

        # Numeric columns statistics
        print("\n" + "-"*40)
        print("NUMERIC COLUMNS OUTLIER ANALYSIS")
        print("-"*40)
        numeric_cols = data.select_dtypes(include=[np.number]).columns

        for col in numeric_cols:
            if col not in ['year_week']:  # Skip year_week as it's not a true numeric measure
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = data[(data[col] < lower_bound) | (data[col] > upper_bound)][col]
                outlier_count = len(outliers)
                outlier_percent = (outlier_count / len(data)) * 100

                print(f"{col}: {outlier_count} outliers ({outlier_percent:.1f}%)")

        # Unique values analysis for categorical columns
        print("\n" + "-"*40)
        print("CATEGORICAL COLUMNS ANALYSIS")
        print("-"*40)
        categorical_cols = data.select_dtypes(include=['object']).columns

        for col in categorical_cols:
            unique_count = data[col].nunique()
            print(f"{col}: {unique_count} unique values")
            if unique_count <= 10:  # Show values if not too many
                print(f"  Values: {list(data[col].unique())}")

    def create_individual_visualizations(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create individual visualization methods that can be called separately

        Args:
            data: Processed DataFrame to visualize
            save_plots: Whether to save plots to files
        """
        logger.info("Creating individual visualizations...")

        # Call all visualization methods
        self.visualize_data_overview(data, save_plots)
        self.visualize_correlation_matrix(data, save_plots)
        self.visualize_time_series(data, save_plots)
        self.visualize_user_analysis(data, save_plots)
        self.visualize_gamification_analysis(data, save_plots)

        # Generate data quality report
        self.generate_data_quality_report(data)

        logger.info("All individual visualizations completed!")


def main():
    """Main function for testing data processor with visualizations"""
    processor = DataProcessor()

    # Process data
    final_dataset = processor.process_all()
    print(f"Final dataset shape: {final_dataset.shape}")
    print(f"Columns: {list(final_dataset.columns)}")

    # Create comprehensive report with all visualizations
    print("\nGenerating comprehensive visualization report...")
    processor.create_comprehensive_report(final_dataset, save_plots=True)


def example_individual_visualizations():
    """Example of how to use individual visualization methods"""
    processor = DataProcessor()

    # Load existing processed data (if available)
    try:
        processed_file = processor.processed_data_path / DataConstants.OUTPUT_FILENAME
        if processed_file.exists():
            data = pd.read_csv(processed_file)
            print(f"Loaded existing processed data: {data.shape}")
        else:
            # Process data if not available
            data = processor.process_all()
    except Exception as e:
        print(f"Error loading data: {e}")
        data = processor.process_all()

    print("\n" + "="*60)
    print("INDIVIDUAL VISUALIZATION EXAMPLES")
    print("="*60)

    # Example 1: Only data overview
    print("\n1. Creating data overview visualization...")
    processor.visualize_data_overview(data, save_plots=True)

    # Example 2: Only correlation matrix
    print("\n2. Creating correlation matrix...")
    processor.visualize_correlation_matrix(data, save_plots=True)

    # Example 3: Only time series analysis
    print("\n3. Creating time series analysis...")
    processor.visualize_time_series(data, save_plots=True)

    # Example 4: Only user analysis
    print("\n4. Creating user analysis...")
    processor.visualize_user_analysis(data, save_plots=True)

    # Example 5: Only gamification analysis
    print("\n5. Creating gamification analysis...")
    processor.visualize_gamification_analysis(data, save_plots=True)

    # Example 6: Data quality report only
    print("\n6. Generating data quality report...")
    processor.generate_data_quality_report(data)

    print("\nAll individual visualizations completed!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "individual":
        # Run individual visualization examples
        example_individual_visualizations()
    else:
        # Run main comprehensive report
        main()
