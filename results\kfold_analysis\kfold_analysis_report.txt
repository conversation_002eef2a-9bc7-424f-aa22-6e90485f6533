================================================================================
K-FOLD CROSS-VALIDATION OVERFITTING ANALYSIS REPORT
================================================================================

📊 DATASET INFORMATION:
   • Total samples: 291
   • Features: 20
   • Target distribution: {'medium_risk': np.int64(153), 'low_risk': np.int64(115), 'high_risk': np.int64(23)}
   • K values tested: 2 to 20

🎯 MODEL PERFORMANCE SUMMARY:
   • Logistic Regression:
     - Best validation: 0.7114 ± 0.0372 (k=4)
     - Training at best k: 0.7285
     - Train-val gap: 0.0171
   • Random Forest:
     - Best validation: 0.7218 ± 0.1179 (k=17)
     - Training at best k: 1.0000
     - Train-val gap: 0.2782
   • Gradient Boosting:
     - Best validation: 0.7078 ± 0.0375 (k=4)
     - Training at best k: 0.9989
     - Train-val gap: 0.2910
   • XGBoost:
     - Best validation: 0.7160 ± 0.1352 (k=19)
     - Training at best k: 1.0000
     - Train-val gap: 0.2840

🔍 OVERFITTING ANALYSIS:
   Models ranked by overfitting risk (low to high):
   1. Logistic Regression - LOW RISK
      • Overfitting score: 9.23
      • Optimal k: 2
      • Max train-val gap: 0.0679
   2. Random Forest - HIGH RISK
      • Overfitting score: 21.02
      • Optimal k: 2
      • Max train-val gap: 0.3265
   3. Gradient Boosting - HIGH RISK
      • Overfitting score: 21.77
      • Optimal k: 4
      • Max train-val gap: 0.3423
   4. XGBoost - HIGH RISK
      • Overfitting score: 22.01
      • Optimal k: 2
      • Max train-val gap: 0.3327

💡 RECOMMENDATIONS:
   • BEST MODEL: Logistic Regression (lowest overfitting risk)
   • RECOMMENDED K: 2
   • HIGH RISK MODELS: Random Forest, Gradient Boosting, XGBoost
   • Consider regularization or feature selection for high-risk models
   • AVERAGE OPTIMAL K: 2.5
   • Low k values suggest small dataset - consider data augmentation
================================================================================