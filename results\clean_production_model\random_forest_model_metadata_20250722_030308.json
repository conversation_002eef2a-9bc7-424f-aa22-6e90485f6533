{"algorithm_name": "Random Forest", "accuracy": 0.6949152542372882, "f1_score": 0.6951815980629541, "cv_accuracy_mean": 0.6464384828862164, "cv_accuracy_std": 0.01895060564462422, "cv_f1_mean": 0.6413344513879771, "cv_f1_std": 0.020878896111467423, "feature_count": 20, "timestamp": "2025-07-22T03:03:08.527556", "model_type": "RandomForestClassifier", "algorithm_key": "random_forest", "random_state": 42, "dataset_path": "dataset/processed/safe_ml_bias_corrected_dataset.csv", "target_column": "corrected_fatigue_risk", "train_samples": 232, "test_samples": 59}