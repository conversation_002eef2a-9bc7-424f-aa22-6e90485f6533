"""
Feature Importance Validation Script
Menguji apakah fitur-fitur yang teridentifikasi SHAP benar-benar penting untuk akurasi

Metode pengujian:
1. Baseline: Model dengan semua fitur
2. Top Features: Model dengan hanya fitur terpenting
3. Ablation: Model tanpa fitur terpenting (drop one by one)
4. Random Features: Model dengan fitur acak (sebagai kontrol)
5. Progressive Addition: Menambah fitur satu per satu berdasarkan ranking SHAP
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, f1_score, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
import os
import glob

# Try to import XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    logging.warning("XGBoost not available, will skip XGBoost tests")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureImportanceValidator:
    """Validator untuk menguji pentingnya fitur berdasarkan hasil SHAP"""
    
    def __init__(self, data_path, shap_results_path, target_column='corrected_fatigue_risk'):
        """
        Initialize validator
        
        Args:
            data_path: Path ke dataset
            shap_results_path: Path ke hasil SHAP
            target_column: Nama kolom target
        """
        self.data_path = data_path
        self.shap_results_path = shap_results_path
        self.target_column = target_column
        
        # Load data
        self.data = pd.read_csv(data_path)
        self.shap_results = pd.read_csv(shap_results_path)
        
        # Prepare features and target
        self.X = self.data.drop(columns=[target_column])
        self.y = self.data[target_column]

        # Encode target for XGBoost (needs numeric labels)
        self.label_encoder = LabelEncoder()
        self.y_encoded = self.label_encoder.fit_transform(self.y)

        # Get SHAP feature ranking
        self.feature_ranking = self._get_feature_ranking()

        # Setup CV
        self.cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        # Results storage
        self.results = {}
        
    def _get_feature_ranking(self):
        """Extract feature ranking from SHAP results or consensus features"""
        try:
            # Try to load from consensus features file (find the most recent one)
            consensus_files = list(Path("results/feature_selection/selected_features/").glob("consensus_features_*.txt"))
            if consensus_files:
                # Use the most recent consensus file
                consensus_file = max(consensus_files, key=lambda p: p.stat().st_mtime)
                logger.info(f"Loading feature ranking from consensus features file: {consensus_file}")
                with open(consensus_file, 'r') as f:
                    lines = f.readlines()

                features = []
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        # Extract feature name (before the first space or parenthesis)
                        feature_name = line.split()[0].strip()
                        if feature_name in self.X.columns:
                            features.append(feature_name)

                if features:
                    logger.info(f"Found {len(features)} consensus features")
                    # Add remaining features
                    remaining = [f for f in self.X.columns if f not in features]
                    return features + remaining

            # Try to extract from SHAP results CSV
            if 'feature' in self.shap_results.columns:
                if 'importance' in self.shap_results.columns:
                    return self.shap_results.sort_values('importance', ascending=False)['feature'].tolist()
                elif 'shap_importance' in self.shap_results.columns:
                    return self.shap_results.sort_values('shap_importance', ascending=False)['feature'].tolist()

            # Fallback: use all features in original order
            logger.warning("Could not extract feature ranking, using all features in original order")
            return list(self.X.columns)

        except Exception as e:
            logger.error(f"Error extracting feature ranking: {e}")
            return list(self.X.columns)
    
    def test_baseline_performance(self):
        """Test baseline performance with all features"""
        logger.info("Testing baseline performance with all features...")

        # Random Forest
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_scores = cross_val_score(rf, self.X, self.y, cv=self.cv, scoring='accuracy')

        # Logistic Regression with scaling
        lr_pipeline = Pipeline([
            ('scaler', StandardScaler()),
            ('lr', LogisticRegression(random_state=42, max_iter=1000))
        ])
        lr_scores = cross_val_score(lr_pipeline, self.X, self.y, cv=self.cv, scoring='accuracy')

        # Gradient Boosting
        gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb_scores = cross_val_score(gb, self.X, self.y, cv=self.cv, scoring='accuracy')

        # XGBoost (if available)
        xgb_scores = None
        if XGBOOST_AVAILABLE:
            xgb = XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss')
            xgb_scores = cross_val_score(xgb, self.X, self.y_encoded, cv=self.cv, scoring='accuracy')

        self.results['baseline'] = {
            'rf_accuracy': rf_scores.mean(),
            'rf_std': rf_scores.std(),
            'lr_accuracy': lr_scores.mean(),
            'lr_std': lr_scores.std(),
            'gb_accuracy': gb_scores.mean(),
            'gb_std': gb_scores.std(),
            'n_features': len(self.X.columns)
        }

        if XGBOOST_AVAILABLE and xgb_scores is not None:
            self.results['baseline']['xgb_accuracy'] = xgb_scores.mean()
            self.results['baseline']['xgb_std'] = xgb_scores.std()

        logger.info(f"Baseline RF: {rf_scores.mean():.4f} ± {rf_scores.std():.4f}")
        logger.info(f"Baseline LR: {lr_scores.mean():.4f} ± {lr_scores.std():.4f}")
        logger.info(f"Baseline GB: {gb_scores.mean():.4f} ± {gb_scores.std():.4f}")
        if XGBOOST_AVAILABLE and xgb_scores is not None:
            logger.info(f"Baseline XGB: {xgb_scores.mean():.4f} ± {xgb_scores.std():.4f}")
        
    def test_top_features_only(self, top_n_list=[5, 10, 15, 20]):
        """Test performance with only top N features"""
        logger.info("Testing performance with top N features only...")

        self.results['top_features'] = {}

        for n in top_n_list:
            if n > len(self.feature_ranking):
                continue

            top_features = self.feature_ranking[:n]
            X_top = self.X[top_features]

            # Random Forest
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_scores = cross_val_score(rf, X_top, self.y, cv=self.cv, scoring='accuracy')

            # Logistic Regression
            lr_pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ])
            lr_scores = cross_val_score(lr_pipeline, X_top, self.y, cv=self.cv, scoring='accuracy')

            # Gradient Boosting
            gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
            gb_scores = cross_val_score(gb, X_top, self.y, cv=self.cv, scoring='accuracy')

            # XGBoost (if available)
            xgb_scores = None
            if XGBOOST_AVAILABLE:
                xgb = XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss')
                xgb_scores = cross_val_score(xgb, X_top, self.y_encoded, cv=self.cv, scoring='accuracy')

            self.results['top_features'][f'top_{n}'] = {
                'rf_accuracy': rf_scores.mean(),
                'rf_std': rf_scores.std(),
                'lr_accuracy': lr_scores.mean(),
                'lr_std': lr_scores.std(),
                'gb_accuracy': gb_scores.mean(),
                'gb_std': gb_scores.std(),
                'n_features': n,
                'features': top_features
            }

            if XGBOOST_AVAILABLE and xgb_scores is not None:
                self.results['top_features'][f'top_{n}']['xgb_accuracy'] = xgb_scores.mean()
                self.results['top_features'][f'top_{n}']['xgb_std'] = xgb_scores.std()

            logger.info(f"Top {n} features - RF: {rf_scores.mean():.4f} ± {rf_scores.std():.4f}")
            logger.info(f"Top {n} features - LR: {lr_scores.mean():.4f} ± {lr_scores.std():.4f}")
            logger.info(f"Top {n} features - GB: {gb_scores.mean():.4f} ± {gb_scores.std():.4f}")
            if XGBOOST_AVAILABLE and xgb_scores is not None:
                logger.info(f"Top {n} features - XGB: {xgb_scores.mean():.4f} ± {xgb_scores.std():.4f}")
    
    def test_ablation_study(self, top_n=10):
        """Test ablation: remove top features one by one"""
        logger.info(f"Testing ablation study - removing top {top_n} features one by one...")

        self.results['ablation'] = {}

        for i in range(min(top_n, len(self.feature_ranking))):
            removed_feature = self.feature_ranking[i]
            remaining_features = [f for f in self.X.columns if f != removed_feature]
            X_ablated = self.X[remaining_features]

            # Random Forest
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_scores = cross_val_score(rf, X_ablated, self.y, cv=self.cv, scoring='accuracy')

            # Logistic Regression
            lr_pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ])
            lr_scores = cross_val_score(lr_pipeline, X_ablated, self.y, cv=self.cv, scoring='accuracy')

            # Gradient Boosting
            gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
            gb_scores = cross_val_score(gb, X_ablated, self.y, cv=self.cv, scoring='accuracy')

            # XGBoost (if available)
            xgb_scores = None
            if XGBOOST_AVAILABLE:
                xgb = XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss')
                xgb_scores = cross_val_score(xgb, X_ablated, self.y_encoded, cv=self.cv, scoring='accuracy')

            self.results['ablation'][f'without_{removed_feature}'] = {
                'rf_accuracy': rf_scores.mean(),
                'rf_std': rf_scores.std(),
                'lr_accuracy': lr_scores.mean(),
                'lr_std': lr_scores.std(),
                'gb_accuracy': gb_scores.mean(),
                'gb_std': gb_scores.std(),
                'removed_feature': removed_feature,
                'n_features': len(remaining_features)
            }

            if XGBOOST_AVAILABLE and xgb_scores is not None:
                self.results['ablation'][f'without_{removed_feature}']['xgb_accuracy'] = xgb_scores.mean()
                self.results['ablation'][f'without_{removed_feature}']['xgb_std'] = xgb_scores.std()

            # Calculate performance drop
            rf_drop = self.results['baseline']['rf_accuracy'] - rf_scores.mean()
            lr_drop = self.results['baseline']['lr_accuracy'] - lr_scores.mean()
            gb_drop = self.results['baseline']['gb_accuracy'] - gb_scores.mean()

            logger.info(f"Without {removed_feature} - RF drop: {rf_drop:.4f}, LR drop: {lr_drop:.4f}, GB drop: {gb_drop:.4f}")
            if XGBOOST_AVAILABLE and xgb_scores is not None:
                xgb_drop = self.results['baseline']['xgb_accuracy'] - xgb_scores.mean()
                logger.info(f"                        XGB drop: {xgb_drop:.4f}")
    
    def test_random_features(self, n_tests=5, feature_counts=[5, 10, 15]):
        """Test with random feature selections as control"""
        logger.info("Testing with random feature selections...")

        self.results['random_features'] = {}

        for n_features in feature_counts:
            if n_features > len(self.X.columns):
                continue

            rf_scores_list = []
            lr_scores_list = []
            gb_scores_list = []
            xgb_scores_list = []

            for test_i in range(n_tests):
                # Random feature selection
                random_features = np.random.choice(self.X.columns, size=n_features, replace=False)
                X_random = self.X[random_features]

                # Random Forest
                rf = RandomForestClassifier(n_estimators=100, random_state=42+test_i)
                rf_scores = cross_val_score(rf, X_random, self.y, cv=self.cv, scoring='accuracy')
                rf_scores_list.append(rf_scores.mean())

                # Logistic Regression
                lr_pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('lr', LogisticRegression(random_state=42+test_i, max_iter=1000))
                ])
                lr_scores = cross_val_score(lr_pipeline, X_random, self.y, cv=self.cv, scoring='accuracy')
                lr_scores_list.append(lr_scores.mean())

                # Gradient Boosting
                gb = GradientBoostingClassifier(n_estimators=100, random_state=42+test_i)
                gb_scores = cross_val_score(gb, X_random, self.y, cv=self.cv, scoring='accuracy')
                gb_scores_list.append(gb_scores.mean())

                # XGBoost (if available)
                if XGBOOST_AVAILABLE:
                    xgb = XGBClassifier(n_estimators=100, random_state=42+test_i, eval_metric='mlogloss')
                    xgb_scores = cross_val_score(xgb, X_random, self.y_encoded, cv=self.cv, scoring='accuracy')
                    xgb_scores_list.append(xgb_scores.mean())

            self.results['random_features'][f'random_{n_features}'] = {
                'rf_accuracy': np.mean(rf_scores_list),
                'rf_std': np.std(rf_scores_list),
                'lr_accuracy': np.mean(lr_scores_list),
                'lr_std': np.std(lr_scores_list),
                'gb_accuracy': np.mean(gb_scores_list),
                'gb_std': np.std(gb_scores_list),
                'n_features': n_features
            }

            if XGBOOST_AVAILABLE and xgb_scores_list:
                self.results['random_features'][f'random_{n_features}']['xgb_accuracy'] = np.mean(xgb_scores_list)
                self.results['random_features'][f'random_{n_features}']['xgb_std'] = np.std(xgb_scores_list)

            logger.info(f"Random {n_features} features - RF: {np.mean(rf_scores_list):.4f} ± {np.std(rf_scores_list):.4f}")
            logger.info(f"                          LR: {np.mean(lr_scores_list):.4f} ± {np.std(lr_scores_list):.4f}")
            logger.info(f"                          GB: {np.mean(gb_scores_list):.4f} ± {np.std(gb_scores_list):.4f}")
            if XGBOOST_AVAILABLE and xgb_scores_list:
                logger.info(f"                          XGB: {np.mean(xgb_scores_list):.4f} ± {np.std(xgb_scores_list):.4f}")
    
    def test_progressive_addition(self, max_features=15):
        """Test progressive addition of features based on SHAP ranking"""
        logger.info("Testing progressive addition of features...")

        self.results['progressive'] = {}

        for n in range(1, min(max_features + 1, len(self.feature_ranking) + 1)):
            selected_features = self.feature_ranking[:n]
            X_progressive = self.X[selected_features]

            # Random Forest
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_scores = cross_val_score(rf, X_progressive, self.y, cv=self.cv, scoring='accuracy')

            # Logistic Regression
            lr_pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ])
            lr_scores = cross_val_score(lr_pipeline, X_progressive, self.y, cv=self.cv, scoring='accuracy')

            # Gradient Boosting
            gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
            gb_scores = cross_val_score(gb, X_progressive, self.y, cv=self.cv, scoring='accuracy')

            # XGBoost (if available)
            xgb_scores = None
            if XGBOOST_AVAILABLE:
                xgb = XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss')
                xgb_scores = cross_val_score(xgb, X_progressive, self.y_encoded, cv=self.cv, scoring='accuracy')

            self.results['progressive'][f'features_{n}'] = {
                'rf_accuracy': rf_scores.mean(),
                'rf_std': rf_scores.std(),
                'lr_accuracy': lr_scores.mean(),
                'lr_std': lr_scores.std(),
                'gb_accuracy': gb_scores.mean(),
                'gb_std': gb_scores.std(),
                'n_features': n,
                'features': selected_features
            }

            if XGBOOST_AVAILABLE and xgb_scores is not None:
                self.results['progressive'][f'features_{n}']['xgb_accuracy'] = xgb_scores.mean()
                self.results['progressive'][f'features_{n}']['xgb_std'] = xgb_scores.std()

            logger.info(f"Progressive {n} features - RF: {rf_scores.mean():.4f}, LR: {lr_scores.mean():.4f}, GB: {gb_scores.mean():.4f}")
            if XGBOOST_AVAILABLE and xgb_scores is not None:
                logger.info(f"                        XGB: {xgb_scores.mean():.4f}")
    
    def run_all_tests(self):
        """Run all validation tests"""
        logger.info("Starting comprehensive feature importance validation...")
        
        self.test_baseline_performance()
        self.test_top_features_only()
        self.test_ablation_study()
        self.test_random_features()
        self.test_progressive_addition()
        
        logger.info("All tests completed!")
    
    def create_visualizations(self, save_plots=True):
        """Create visualizations of the results"""
        logger.info("Creating visualizations...")
        
        # Create output directory
        viz_dir = Path("results/feature_validation")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. Top Features Performance Comparison
        self._plot_top_features_comparison(viz_dir if save_plots else None)
        
        # 2. Ablation Study Results
        self._plot_ablation_results(viz_dir if save_plots else None)
        
        # 3. Progressive Addition Curve
        self._plot_progressive_addition(viz_dir if save_plots else None)
        
        # 4. Random vs SHAP Features Comparison
        self._plot_random_vs_shap(viz_dir if save_plots else None)
        
        if save_plots:
            logger.info(f"Visualizations saved to {viz_dir}")
    
    def _plot_top_features_comparison(self, save_dir):
        """Plot top features performance comparison for all 4 algorithms"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Top Features Performance Comparison - All 4 Algorithms', fontsize=16, fontweight='bold')

        # Extract data for plotting
        n_features = []
        rf_accuracies = []
        lr_accuracies = []
        gb_accuracies = []
        xgb_accuracies = []
        rf_stds = []
        lr_stds = []
        gb_stds = []
        xgb_stds = []

        for key, result in self.results['top_features'].items():
            n_features.append(result['n_features'])
            rf_accuracies.append(result['rf_accuracy'])
            lr_accuracies.append(result['lr_accuracy'])
            gb_accuracies.append(result['gb_accuracy'])
            rf_stds.append(result['rf_std'])
            lr_stds.append(result['lr_std'])
            gb_stds.append(result['gb_std'])

            if 'xgb_accuracy' in result:
                xgb_accuracies.append(result['xgb_accuracy'])
                xgb_stds.append(result['xgb_std'])

        # Add baseline
        n_features.append(self.results['baseline']['n_features'])
        rf_accuracies.append(self.results['baseline']['rf_accuracy'])
        lr_accuracies.append(self.results['baseline']['lr_accuracy'])
        gb_accuracies.append(self.results['baseline']['gb_accuracy'])
        rf_stds.append(self.results['baseline']['rf_std'])
        lr_stds.append(self.results['baseline']['lr_std'])
        gb_stds.append(self.results['baseline']['gb_std'])

        if 'xgb_accuracy' in self.results['baseline']:
            xgb_accuracies.append(self.results['baseline']['xgb_accuracy'])
            xgb_stds.append(self.results['baseline']['xgb_std'])

        # Plot Random Forest (top-left)
        axes[0, 0].errorbar(n_features, rf_accuracies, yerr=rf_stds, marker='o', capsize=5, color='skyblue')
        axes[0, 0].set_title('Random Forest: Top Features Performance')
        axes[0, 0].set_xlabel('Number of Features')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].grid(True, alpha=0.3)

        # Plot Logistic Regression (top-right)
        axes[0, 1].errorbar(n_features, lr_accuracies, yerr=lr_stds, marker='s', capsize=5, color='orange')
        axes[0, 1].set_title('Logistic Regression: Top Features Performance')
        axes[0, 1].set_xlabel('Number of Features')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot Gradient Boosting (bottom-left)
        axes[1, 0].errorbar(n_features, gb_accuracies, yerr=gb_stds, marker='^', capsize=5, color='lightcoral')
        axes[1, 0].set_title('Gradient Boosting: Top Features Performance')
        axes[1, 0].set_xlabel('Number of Features')
        axes[1, 0].set_ylabel('Accuracy')
        axes[1, 0].grid(True, alpha=0.3)

        # Plot XGBoost (bottom-right) if available
        if xgb_accuracies and len(xgb_accuracies) == len(n_features):
            axes[1, 1].errorbar(n_features, xgb_accuracies, yerr=xgb_stds, marker='d', capsize=5, color='lightgreen')
            axes[1, 1].set_title('XGBoost: Top Features Performance')
            axes[1, 1].set_xlabel('Number of Features')
            axes[1, 1].set_ylabel('Accuracy')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            # Hide the subplot if XGBoost is not available
            axes[1, 1].text(0.5, 0.5, 'XGBoost\nNot Available',
                           horizontalalignment='center', verticalalignment='center',
                           transform=axes[1, 1].transAxes, fontsize=14)
            axes[1, 1].set_xticks([])
            axes[1, 1].set_yticks([])

        plt.tight_layout()

        if save_dir:
            plt.savefig(save_dir / 'top_features_comparison.png', dpi=300, bbox_inches='tight')
        # plt.show()
    
    def _plot_ablation_results(self, save_dir):
        """Plot ablation study results for all 4 algorithms"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Ablation Study: Performance Drop When Removing Features', fontsize=16, fontweight='bold')

        # Extract ablation data
        features = []
        rf_drops = []
        lr_drops = []
        gb_drops = []
        xgb_drops = []

        baseline_rf = self.results['baseline']['rf_accuracy']
        baseline_lr = self.results['baseline']['lr_accuracy']
        baseline_gb = self.results['baseline']['gb_accuracy']
        baseline_xgb = self.results['baseline'].get('xgb_accuracy', 0)

        for key, result in self.results['ablation'].items():
            features.append(result['removed_feature'])
            rf_drops.append(baseline_rf - result['rf_accuracy'])
            lr_drops.append(baseline_lr - result['lr_accuracy'])
            gb_drops.append(baseline_gb - result['gb_accuracy'])
            if 'xgb_accuracy' in result and baseline_xgb > 0:
                xgb_drops.append(baseline_xgb - result['xgb_accuracy'])

        # Plot Random Forest drops (top-left)
        bars1 = axes[0, 0].bar(range(len(features)), rf_drops, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('Random Forest: Performance Drop')
        axes[0, 0].set_xlabel('Removed Feature')
        axes[0, 0].set_ylabel('Accuracy Drop')
        axes[0, 0].set_xticks(range(len(features)))
        axes[0, 0].set_xticklabels(features, rotation=45, ha='right')
        axes[0, 0].grid(True, alpha=0.3)

        # Plot Logistic Regression drops (top-right)
        bars2 = axes[0, 1].bar(range(len(features)), lr_drops, alpha=0.7, color='orange')
        axes[0, 1].set_title('Logistic Regression: Performance Drop')
        axes[0, 1].set_xlabel('Removed Feature')
        axes[0, 1].set_ylabel('Accuracy Drop')
        axes[0, 1].set_xticks(range(len(features)))
        axes[0, 1].set_xticklabels(features, rotation=45, ha='right')
        axes[0, 1].grid(True, alpha=0.3)

        # Plot Gradient Boosting drops (bottom-left)
        bars3 = axes[1, 0].bar(range(len(features)), gb_drops, alpha=0.7, color='lightcoral')
        axes[1, 0].set_title('Gradient Boosting: Performance Drop')
        axes[1, 0].set_xlabel('Removed Feature')
        axes[1, 0].set_ylabel('Accuracy Drop')
        axes[1, 0].set_xticks(range(len(features)))
        axes[1, 0].set_xticklabels(features, rotation=45, ha='right')
        axes[1, 0].grid(True, alpha=0.3)

        # Plot XGBoost drops (bottom-right) if available
        if xgb_drops and len(xgb_drops) == len(features):
            bars4 = axes[1, 1].bar(range(len(features)), xgb_drops, alpha=0.7, color='lightgreen')
            axes[1, 1].set_title('XGBoost: Performance Drop')
            axes[1, 1].set_xlabel('Removed Feature')
            axes[1, 1].set_ylabel('Accuracy Drop')
            axes[1, 1].set_xticks(range(len(features)))
            axes[1, 1].set_xticklabels(features, rotation=45, ha='right')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            # Hide the subplot if XGBoost is not available
            axes[1, 1].text(0.5, 0.5, 'XGBoost\nNot Available',
                           horizontalalignment='center', verticalalignment='center',
                           transform=axes[1, 1].transAxes, fontsize=14)
            axes[1, 1].set_xticks([])
            axes[1, 1].set_yticks([])

        plt.tight_layout()

        if save_dir:
            plt.savefig(save_dir / 'ablation_results.png', dpi=300, bbox_inches='tight')
        # plt.show()
    
    def _plot_progressive_addition(self, save_dir):
        """Plot progressive addition curve"""
        fig, ax = plt.subplots(figsize=(12, 6))

        # Extract progressive data
        n_features = []
        rf_accuracies = []
        lr_accuracies = []
        gb_accuracies = []
        xgb_accuracies = []

        for key, result in sorted(self.results['progressive'].items(),
                                key=lambda x: x[1]['n_features']):
            n_features.append(result['n_features'])
            rf_accuracies.append(result['rf_accuracy'])
            lr_accuracies.append(result['lr_accuracy'])
            gb_accuracies.append(result['gb_accuracy'])
            if 'xgb_accuracy' in result:
                xgb_accuracies.append(result['xgb_accuracy'])

        # Plot all algorithms
        ax.plot(n_features, rf_accuracies, marker='o', label='Random Forest', linewidth=2)
        ax.plot(n_features, lr_accuracies, marker='s', label='Logistic Regression', linewidth=2)
        ax.plot(n_features, gb_accuracies, marker='^', label='Gradient Boosting', linewidth=2)
        if xgb_accuracies and len(xgb_accuracies) == len(n_features):
            ax.plot(n_features, xgb_accuracies, marker='d', label='XGBoost', linewidth=2)

        # Add baseline lines
        ax.axhline(y=self.results['baseline']['rf_accuracy'], color='blue',
                  linestyle='--', alpha=0.7, label='RF Baseline (All Features)')
        ax.axhline(y=self.results['baseline']['lr_accuracy'], color='orange',
                  linestyle='--', alpha=0.7, label='LR Baseline (All Features)')
        ax.axhline(y=self.results['baseline']['gb_accuracy'], color='green',
                  linestyle='--', alpha=0.7, label='GB Baseline (All Features)')
        if 'xgb_accuracy' in self.results['baseline']:
            ax.axhline(y=self.results['baseline']['xgb_accuracy'], color='red',
                      linestyle='--', alpha=0.7, label='XGB Baseline (All Features)')

        ax.set_title('Progressive Feature Addition: Performance Curve (All 4 Algorithms)')
        ax.set_xlabel('Number of Features (Added by SHAP Ranking)')
        ax.set_ylabel('Accuracy')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_dir:
            plt.savefig(save_dir / 'progressive_addition.png', dpi=300, bbox_inches='tight')
        # plt.show()
    
    def _plot_random_vs_shap(self, save_dir):
        """Plot random vs SHAP features comparison for all 4 algorithms"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('SHAP vs Random Features Comparison - All 4 Algorithms', fontsize=16, fontweight='bold')

        # Compare with same number of features
        feature_counts = []
        shap_rf = []
        random_rf = []
        shap_lr = []
        random_lr = []
        shap_gb = []
        random_gb = []
        shap_xgb = []
        random_xgb = []

        for count in [5, 10, 15]:
            if f'top_{count}' in self.results['top_features'] and f'random_{count}' in self.results['random_features']:
                feature_counts.append(count)
                shap_rf.append(self.results['top_features'][f'top_{count}']['rf_accuracy'])
                random_rf.append(self.results['random_features'][f'random_{count}']['rf_accuracy'])
                shap_lr.append(self.results['top_features'][f'top_{count}']['lr_accuracy'])
                random_lr.append(self.results['random_features'][f'random_{count}']['lr_accuracy'])
                shap_gb.append(self.results['top_features'][f'top_{count}']['gb_accuracy'])
                random_gb.append(self.results['random_features'][f'random_{count}']['gb_accuracy'])

                # XGBoost if available
                if ('xgb_accuracy' in self.results['top_features'][f'top_{count}'] and
                    'xgb_accuracy' in self.results['random_features'][f'random_{count}']):
                    shap_xgb.append(self.results['top_features'][f'top_{count}']['xgb_accuracy'])
                    random_xgb.append(self.results['random_features'][f'random_{count}']['xgb_accuracy'])

        x = np.arange(len(feature_counts))
        width = 0.35

        # Random Forest comparison (top-left)
        axes[0, 0].bar(x - width/2, shap_rf, width, label='SHAP Features', alpha=0.8, color='skyblue')
        axes[0, 0].bar(x + width/2, random_rf, width, label='Random Features', alpha=0.8, color='lightblue')
        axes[0, 0].set_title('Random Forest: SHAP vs Random Features')
        axes[0, 0].set_xlabel('Number of Features')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(feature_counts)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Logistic Regression comparison (top-right)
        axes[0, 1].bar(x - width/2, shap_lr, width, label='SHAP Features', alpha=0.8, color='orange')
        axes[0, 1].bar(x + width/2, random_lr, width, label='Random Features', alpha=0.8, color='red')
        axes[0, 1].set_title('Logistic Regression: SHAP vs Random Features')
        axes[0, 1].set_xlabel('Number of Features')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(feature_counts)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Gradient Boosting comparison (bottom-left)
        axes[1, 0].bar(x - width/2, shap_gb, width, label='SHAP Features', alpha=0.8, color='lightcoral')
        axes[1, 0].bar(x + width/2, random_gb, width, label='Random Features', alpha=0.8, color='salmon')
        axes[1, 0].set_title('Gradient Boosting: SHAP vs Random Features')
        axes[1, 0].set_xlabel('Number of Features')
        axes[1, 0].set_ylabel('Accuracy')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(feature_counts)
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # XGBoost comparison (bottom-right) if available
        if shap_xgb and len(shap_xgb) == len(feature_counts):
            axes[1, 1].bar(x - width/2, shap_xgb, width, label='SHAP Features', alpha=0.8, color='lightgreen')
            axes[1, 1].bar(x + width/2, random_xgb, width, label='Random Features', alpha=0.8, color='palegreen')
            axes[1, 1].set_title('XGBoost: SHAP vs Random Features')
            axes[1, 1].set_xlabel('Number of Features')
            axes[1, 1].set_ylabel('Accuracy')
            axes[1, 1].set_xticks(x)
            axes[1, 1].set_xticklabels(feature_counts)
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        else:
            # Hide the subplot if XGBoost is not available
            axes[1, 1].text(0.5, 0.5, 'XGBoost\nNot Available',
                           horizontalalignment='center', verticalalignment='center',
                           transform=axes[1, 1].transAxes, fontsize=14)
            axes[1, 1].set_xticks([])
            axes[1, 1].set_yticks([])

        plt.tight_layout()

        if save_dir:
            plt.savefig(save_dir / 'shap_vs_random.png', dpi=300, bbox_inches='tight')
        # plt.show()
    
    def generate_report(self):
        """Generate comprehensive validation report"""
        report = []
        report.append("="*80)
        report.append("FEATURE IMPORTANCE VALIDATION REPORT")
        report.append("="*80)
        
        # Baseline performance
        report.append(f"\n[BASELINE] BASELINE PERFORMANCE (All {self.results['baseline']['n_features']} features):")
        report.append(f"   * Random Forest: {self.results['baseline']['rf_accuracy']:.4f} +/- {self.results['baseline']['rf_std']:.4f}")
        report.append(f"   * Logistic Regression: {self.results['baseline']['lr_accuracy']:.4f} +/- {self.results['baseline']['lr_std']:.4f}")
        report.append(f"   * Gradient Boosting: {self.results['baseline']['gb_accuracy']:.4f} +/- {self.results['baseline']['gb_std']:.4f}")
        if 'xgb_accuracy' in self.results['baseline']:
            report.append(f"   * XGBoost: {self.results['baseline']['xgb_accuracy']:.4f} +/- {self.results['baseline']['xgb_std']:.4f}")

        # Top features performance
        report.append(f"\n[TOP FEATURES] TOP FEATURES PERFORMANCE:")
        for key, result in self.results['top_features'].items():
            n = result['n_features']
            report.append(f"   * Top {n} features:")
            report.append(f"     - RF: {result['rf_accuracy']:.4f} +/- {result['rf_std']:.4f}")
            report.append(f"     - LR: {result['lr_accuracy']:.4f} +/- {result['lr_std']:.4f}")
            report.append(f"     - GB: {result['gb_accuracy']:.4f} +/- {result['gb_std']:.4f}")
            if 'xgb_accuracy' in result:
                report.append(f"     - XGB: {result['xgb_accuracy']:.4f} +/- {result['xgb_std']:.4f}")

        # Find best performing top-N for all algorithms
        best_rf = max(self.results['top_features'].items(), key=lambda x: x[1]['rf_accuracy'])
        best_lr = max(self.results['top_features'].items(), key=lambda x: x[1]['lr_accuracy'])
        best_gb = max(self.results['top_features'].items(), key=lambda x: x[1]['gb_accuracy'])

        report.append(f"\n[BEST] BEST PERFORMANCE:")
        report.append(f"   * RF: {best_rf[0]} with {best_rf[1]['rf_accuracy']:.4f}")
        report.append(f"   * LR: {best_lr[0]} with {best_lr[1]['lr_accuracy']:.4f}")
        report.append(f"   * GB: {best_gb[0]} with {best_gb[1]['gb_accuracy']:.4f}")

        # Find best XGBoost if available
        xgb_results = [(k, v) for k, v in self.results['top_features'].items() if 'xgb_accuracy' in v]
        if xgb_results:
            best_xgb = max(xgb_results, key=lambda x: x[1]['xgb_accuracy'])
            report.append(f"   * XGB: {best_xgb[0]} with {best_xgb[1]['xgb_accuracy']:.4f}")

        # Ablation study insights
        report.append(f"\n[ABLATION] ABLATION STUDY (Feature Importance):")
        baseline_rf = self.results['baseline']['rf_accuracy']
        baseline_lr = self.results['baseline']['lr_accuracy']
        baseline_gb = self.results['baseline']['gb_accuracy']
        baseline_xgb = self.results['baseline'].get('xgb_accuracy', 0)

        # Sort by performance drop (using RF as primary metric)
        ablation_sorted = sorted(self.results['ablation'].items(),
                               key=lambda x: baseline_rf - x[1]['rf_accuracy'], reverse=True)

        report.append("   Most important features (by performance drop when removed):")
        for i, (key, result) in enumerate(ablation_sorted[:5]):
            rf_drop = baseline_rf - result['rf_accuracy']
            lr_drop = baseline_lr - result['lr_accuracy']
            gb_drop = baseline_gb - result['gb_accuracy']

            report.append(f"   {i+1}. {result['removed_feature']}:")
            report.append(f"      RF drop: {rf_drop:.4f}, LR drop: {lr_drop:.4f}, GB drop: {gb_drop:.4f}")
            if 'xgb_accuracy' in result and baseline_xgb > 0:
                xgb_drop = baseline_xgb - result['xgb_accuracy']
                report.append(f"      XGB drop: {xgb_drop:.4f}")

        # Random vs SHAP comparison
        report.append(f"\n[COMPARISON] SHAP vs RANDOM FEATURES:")
        for count in [5, 10, 15]:
            if f'top_{count}' in self.results['top_features'] and f'random_{count}' in self.results['random_features']:
                # RF comparison
                shap_rf = self.results['top_features'][f'top_{count}']['rf_accuracy']
                random_rf = self.results['random_features'][f'random_{count}']['rf_accuracy']
                rf_improvement = shap_rf - random_rf

                # GB comparison
                shap_gb = self.results['top_features'][f'top_{count}']['gb_accuracy']
                random_gb = self.results['random_features'][f'random_{count}']['gb_accuracy']
                gb_improvement = shap_gb - random_gb

                report.append(f"   * {count} features - SHAP advantages:")
                report.append(f"     RF: {rf_improvement:.4f} ({rf_improvement*100:.2f}%)")
                report.append(f"     GB: {gb_improvement:.4f} ({gb_improvement*100:.2f}%)")

                # XGB comparison if available
                if ('xgb_accuracy' in self.results['top_features'][f'top_{count}'] and
                    'xgb_accuracy' in self.results['random_features'][f'random_{count}']):
                    shap_xgb = self.results['top_features'][f'top_{count}']['xgb_accuracy']
                    random_xgb = self.results['random_features'][f'random_{count}']['xgb_accuracy']
                    xgb_improvement = shap_xgb - random_xgb
                    report.append(f"     XGB: {xgb_improvement:.4f} ({xgb_improvement*100:.2f}%)")

        # Conclusions
        report.append(f"\n[CONCLUSIONS] CONCLUSIONS:")

        # Check if top features perform as well as all features for all algorithms
        best_top_rf = max([r['rf_accuracy'] for r in self.results['top_features'].values()])
        best_top_gb = max([r['gb_accuracy'] for r in self.results['top_features'].values()])

        # RF conclusions
        if best_top_rf >= baseline_rf - 0.01:  # Within 1% of baseline
            report.append("   [OK] RF: Top SHAP features achieve comparable performance to all features")
        else:
            report.append("   [WARNING] RF: Top SHAP features show performance degradation compared to all features")

        # GB conclusions
        if best_top_gb >= baseline_gb - 0.01:  # Within 1% of baseline
            report.append("   [OK] GB: Top SHAP features achieve comparable performance to all features")
        else:
            report.append("   [WARNING] GB: Top SHAP features show performance degradation compared to all features")

        # XGB conclusions if available
        xgb_results = [r for r in self.results['top_features'].values() if 'xgb_accuracy' in r]
        if xgb_results and baseline_xgb > 0:
            best_top_xgb = max([r['xgb_accuracy'] for r in xgb_results])
            if best_top_xgb >= baseline_xgb - 0.01:
                report.append("   [OK] XGB: Top SHAP features achieve comparable performance to all features")
            else:
                report.append("   [WARNING] XGB: Top SHAP features show performance degradation compared to all features")

        # Check SHAP vs random for all algorithms
        valid_counts = [count for count in [5, 10, 15]
                       if f'top_{count}' in self.results['top_features'] and f'random_{count}' in self.results['random_features']]

        if valid_counts:
            # RF advantage
            avg_rf_advantage = np.mean([
                self.results['top_features'][f'top_{count}']['rf_accuracy'] -
                self.results['random_features'][f'random_{count}']['rf_accuracy']
                for count in valid_counts
            ])

            # GB advantage
            avg_gb_advantage = np.mean([
                self.results['top_features'][f'top_{count}']['gb_accuracy'] -
                self.results['random_features'][f'random_{count}']['gb_accuracy']
                for count in valid_counts
            ])

            # Overall assessment
            avg_advantage = (avg_rf_advantage + avg_gb_advantage) / 2

            if avg_advantage > 0.02:  # More than 2% advantage
                report.append("   [OK] SHAP features significantly outperform random feature selection")
            elif avg_advantage > 0:
                report.append("   [OK] SHAP features show modest advantage over random selection")
            else:
                report.append("   [ERROR] SHAP features do not show clear advantage over random selection")

            report.append(f"   [STATS] Average SHAP advantages: RF {avg_rf_advantage:.3f}, GB {avg_gb_advantage:.3f}")

            # XGB advantage if available
            xgb_valid_counts = [count for count in valid_counts
                               if ('xgb_accuracy' in self.results['top_features'][f'top_{count}'] and
                                   'xgb_accuracy' in self.results['random_features'][f'random_{count}'])]
            if xgb_valid_counts:
                avg_xgb_advantage = np.mean([
                    self.results['top_features'][f'top_{count}']['xgb_accuracy'] -
                    self.results['random_features'][f'random_{count}']['xgb_accuracy']
                    for count in xgb_valid_counts
                ])
                report.append(f"                                XGB {avg_xgb_advantage:.3f}")
        
        report.append("="*80)
        
        # Save report
        report_text = '\n'.join(report)
        report_file = Path("results/feature_validation/validation_report.txt")
        report_file.parent.mkdir(parents=True, exist_ok=True)

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        print(report_text)
        logger.info(f"Validation report saved to {report_file}")
        
        return report_text


def main():
    """Main function to run feature importance validation"""
    
    # Paths - adjust these based on your file locations
    data_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
    # shap_results_path = "results/shap_ablation_study/shap_results_20250721_043719.csv"
    
        # Path folder tempat file SHAP disimpan
    folder_path = "results/shap_ablation_study"

    # Pola pencarian file
    pattern = os.path.join(folder_path, "shap_results_*.csv")

    # Cari semua file yang cocok
    matching_files = glob.glob(pattern)

    if not matching_files:
        print("Tidak ada file SHAP ditemukan.")
    else:
        # Ambil file dengan timestamp terbaru (berdasarkan nama file)
        shap_results_path = max(matching_files, key=os.path.getctime)
        # Check if files exist
        if not Path(data_path).exists():
            print(f"❌ Data file not found: {data_path}")
            print("Please check the path or run the main pipeline first")
            return
        
        if not Path(shap_results_path).exists():
            print(f"❌ SHAP results file not found: {shap_results_path}")
            print("Please run main1.py first to generate SHAP results")
            return
    
    # Initialize validator
    validator = FeatureImportanceValidator(
        data_path=data_path,
        shap_results_path=shap_results_path,
        target_column='corrected_fatigue_risk'
    )
    
    # Run all validation tests
    validator.run_all_tests()
    
    # Create visualizations
    validator.create_visualizations(save_plots=True)
    
    # Generate report
    validator.generate_report()
    
    print("\n🎉 Feature importance validation completed!")
    print("📁 Results saved to: results/feature_validation/")


if __name__ == "__main__":
    main()
