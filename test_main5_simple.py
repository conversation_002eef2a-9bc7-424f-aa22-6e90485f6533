#!/usr/bin/env python3
"""
Simple test to isolate the main5.py issue
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, 'src')

def test_main5_import():
    """Test main5 import and basic functionality"""
    try:
        # Clear any cached modules
        modules_to_clear = [k for k in sys.modules.keys() if 'main5' in k]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        import main5
        print("✅ main5 import successful")
        
        # Test class instantiation
        pipeline = main5.BiasCorrectedSHAPAnalysisPipeline(include_ml=True)
        print("✅ Pipeline instantiation successful")
        
        # Test method existence
        if hasattr(pipeline, 'run_bias_corrected_prediction'):
            print("✅ run_bias_corrected_prediction method exists")
        else:
            print("❌ run_bias_corrected_prediction method missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shap_study_direct():
    """Test SHAP study directly"""
    try:
        from shap_ablation_study import SHAPAblationStudy
        print("✅ SHAP import successful")
        
        # Check for test datasets
        test_datasets = [
            'dataset/processed/safe_ml_bias_corrected_dataset.csv',
            'dataset/processed/safe_ml_fatigue_dataset.csv'
        ]
        
        available_dataset = None
        for dataset_path in test_datasets:
            if Path(dataset_path).exists():
                available_dataset = dataset_path
                print(f"✅ Found test dataset: {dataset_path}")
                break
        
        if not available_dataset:
            print("❌ No test datasets found")
            return False
            
        # Test SHAP study
        target_column = 'corrected_fatigue_risk' if 'bias_corrected' in available_dataset else 'fatigue_risk'
        print(f"🎯 Using target column: {target_column}")
        
        shap_study = SHAPAblationStudy(
            data_path=available_dataset,
            target_column=target_column,
            random_state=42
        )
        print("✅ SHAP study instantiation successful")
        
        # Test data loading
        shap_study.load_data()
        print("✅ Data loading successful")
        
        # Test SHAP study execution (quick test)
        print("🔍 Running SHAP study...")
        study_results = shap_study.run_complete_shap_study()
        print("✅ SHAP study execution successful")
        
        # Check results structure
        print(f"📊 Results keys: {list(study_results.keys())}")
        
        if 'model_performance' in study_results:
            print("✅ model_performance found in results")
            best_accuracy = 0.0
            best_algo = None
            for algo, perf in study_results['model_performance'].items():
                accuracy = perf.get('accuracy', 0.0)
                print(f"   • {algo}: {accuracy:.4f}")
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_algo = algo
            
            print(f"🏆 Best algorithm: {best_algo} with accuracy {best_accuracy:.4f}")
            return True
        else:
            print("❌ model_performance not found in results")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing main5.py components...")
    
    print("\n1. Testing SHAP study directly...")
    shap_success = test_shap_study_direct()
    
    print("\n2. Testing main5 import...")
    main5_success = test_main5_import()
    
    if shap_success and main5_success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")
