# 🎯 Panduan Input Sederhana untuk Prediksi Kelelahan

## ❌ **MASALAH SEBELUMNYA**

Interface sebelumnya meminta **18 fitur turunan** yang sulit diisi user:

-   `productivity_points`, `achievement_rate`, `title_balance_ratio`, dll.
-   Fitur-fitur ini adalah **hasil kalkulasi** dari data mentah
-   User tidak tahu bagaimana menghitung fitur turunan tersebut

## ✅ **SOLUSI BARU**

Interface baru hanya meminta **INPUT DASAR** yang mudah diisi user:

### **📋 INPUT YANG DIBUTUHKAN USER (6 Field Saja!)**

| No  | Input Field                      | Type    | Range  | Deskripsi                           | Contoh    |
| --- | -------------------------------- | ------- | ------ | ----------------------------------- | --------- |
| 1   | **Jumlah aktivitas olahraga**    | Integer | 0-20   | Berapa kali olahraga dalam seminggu | 3 kali    |
| 2   | **Total jarak olahraga (km)**    | Float   | 0-100  | Total jarak tempuh saat olahraga    | 12.0 km   |
| 3   | **Total waktu olahraga (menit)** | Integer | 0-2000 | Total waktu berolahraga             | 180 menit |
| 4   | **Hari aktif olahraga**          | Integer | 0-7    | Berapa hari berolahraga             | 3 hari    |
| 5   | **Sesi kerja/belajar**           | Integer | 0-50   | Jumlah sesi produktif               | 15 sesi   |
| 6   | **Hari kerja/belajar**           | Integer | 0-7    | Berapa hari bekerja/belajar         | 5 hari    |

### **❌ FITUR YANG DIHAPUS (Tidak Dibutuhkan Model):**

-   ~~Kualitas tidur~~ ← **Tidak ada dalam 18 fitur model**
-   ~~Tingkat stress~~ ← **Tidak ada dalam 18 fitur model**

## 🔄 **PROSES KONVERSI OTOMATIS**

Sistem akan **otomatis menghitung** 18 fitur turunan dari 6 input dasar:

### **Dari Input Dasar → Fitur Model:**

```python
# Input user: 3 aktivitas, 12km, 180 menit, 3 hari aktif
# Sistem menghitung:
avg_distance_km = 12.0 / 3 = 4.0
avg_time_minutes = 180 / 3 = 60.0
activity_points = min((12.0 / 6) * 100, 100) = 100.0
# ... dan seterusnya untuk 18 fitur
```

### **Fitur yang Dihitung Otomatis:**

1. `total_distance_km` ← langsung dari input
2. `avg_distance_km` ← total_distance / activity_days
3. `total_time_minutes` ← langsung dari input
4. `avg_time_minutes` ← total_time / activity_days
5. `avg_cycles` ← pomokit_cycles / work_days
6. `weekly_efficiency` ← pomokit_cycles / work_days
7. `strava_title_count` ← strava_activities
8. `strava_title_length` ← estimasi 15.0
9. `strava_unique_words` ← estimasi dari aktivitas
10. `pomokit_title_count` ← pomokit_cycles
11. `pomokit_title_length` ← estimasi 25.0
12. `pomokit_unique_words` ← estimasi dari cycles
13. `total_title_diversity` ← strava_words + pomokit_words
14. `title_balance_ratio` ← strava_length / pomokit_length
15. `activity_points` ← (distance/target) \* 100
16. `productivity_points` ← (cycles/target) \* 100
17. `achievement_rate` ← total_points / 200
18. `gamification_balance` ← |activity_points - productivity_points|

## 🎨 **INTERFACE DESIGN**

### **Layout Sederhana:**

```
┌─────────────────────────────────────────┐
│ 🤖 Fatigue Risk Prediction System      │
├─────────────────────────────────────────┤
│ 🏃 Aktivitas Fisik    │ 💼 Produktivitas │
│ - Jumlah olahraga     │ - Sesi kerja     │
│ - Total jarak         │ - Hari kerja     │
│ - Total waktu         │                  │
│ - Hari aktif          │ 📊 Konteks       │
│                       │ - Kualitas tidur │
│                       │ - Tingkat stress │
├─────────────────────────────────────────┤
│           [🔮 Prediksi Risiko]          │
├─────────────────────────────────────────┤
│ 📊 Hasil & Rekomendasi                  │
└─────────────────────────────────────────┘
```

### **Form Fields:**

#### **🏃 Aktivitas Fisik (Strava)**

```html
<input
    type="number"
    placeholder="Jumlah aktivitas olahraga"
    min="0"
    max="20"
    value="3"
/>
<input
    type="number"
    placeholder="Total jarak (km)"
    min="0"
    max="100"
    step="0.5"
    value="12.0"
/>
<input
    type="number"
    placeholder="Total waktu (menit)"
    min="0"
    max="2000"
    step="15"
    value="180"
/>
<input type="number" placeholder="Hari aktif" min="0" max="7" value="3" />
```

#### **💼 Aktivitas Produktivitas (Pomokit)**

```html
<input
    type="number"
    placeholder="Sesi kerja/belajar"
    min="0"
    max="50"
    value="15"
/>
<input type="number" placeholder="Hari kerja" min="0" max="7" value="5" />
```

#### **📊 Konteks Tambahan**

```html
<select name="sleep_quality">
    <option value="1">Sangat Buruk</option>
    <option value="2">Buruk</option>
    <option value="3">Cukup</option>
    <option value="4">Baik</option>
    <option value="5">Sangat Baik</option>
</select>

<select name="stress_level">
    <option value="1">Sangat Rendah</option>
    <option value="2">Rendah</option>
    <option value="3">Sedang</option>
    <option value="4">Tinggi</option>
    <option value="5">Sangat Tinggi</option>
</select>
```

## 💻 **IMPLEMENTASI BACKEND**

### **Fungsi Konversi:**

```python
def calculate_derived_features(user_input):
    """Konversi input dasar ke 18 fitur model"""

    # Ambil input dasar
    strava_activities = user_input['strava_activities']
    total_distance = user_input['total_distance_km']
    total_time = user_input['total_time_minutes']
    activity_days = user_input['activity_days']
    pomokit_cycles = user_input['pomokit_cycles']
    work_days = user_input['work_days']

    # Hitung fitur turunan
    derived = {}

    # Distance & time metrics
    derived['total_distance_km'] = total_distance
    derived['avg_distance_km'] = total_distance / max(activity_days, 1)
    derived['total_time_minutes'] = total_time
    derived['avg_time_minutes'] = total_time / max(activity_days, 1)

    # Productivity metrics
    derived['avg_cycles'] = pomokit_cycles / max(work_days, 1)
    derived['weekly_efficiency'] = pomokit_cycles / max(work_days, 1)

    # Title features (estimasi)
    derived['strava_title_count'] = strava_activities
    derived['strava_title_length'] = 15.0  # rata-rata
    derived['strava_unique_words'] = max(1, int(strava_activities * 0.8))

    # Gamification features
    derived['activity_points'] = min((total_distance / 6) * 100, 100)
    derived['productivity_points'] = min((pomokit_cycles / 5) * 100, 100)
    derived['achievement_rate'] = (derived['activity_points'] + derived['productivity_points']) / 200
    derived['gamification_balance'] = abs(derived['activity_points'] - derived['productivity_points'])

    return derived
```

## 🚀 **CARA MENGGUNAKAN**

### **1. Jalankan Interface Sederhana:**

```bash
streamlit run simple_user_interface.py
```

### **2. Isi Form dengan Data Nyata:**

-   **Aktivitas Olahraga**: Hitung aktivitas seminggu terakhir
-   **Jarak & Waktu**: Total dari semua aktivitas
-   **Sesi Kerja**: Hitung sesi produktif (Pomokit)
-   **Konteks**: Evaluasi tidur dan stress subjektif

### **3. Interpretasi Hasil:**

-   **Risiko Rendah**: Lanjutkan aktivitas normal
-   **Risiko Sedang**: Perhatikan keseimbangan
-   **Risiko Tinggi**: Istirahat dan evaluasi

## 🎯 **KEUNTUNGAN PENDEKATAN INI**

### **✅ User-Friendly:**

-   Hanya 8 input vs 18 fitur kompleks
-   Pertanyaan yang mudah dipahami
-   Tidak perlu menghitung fitur turunan

### **✅ Akurat:**

-   Menggunakan model yang sama (93.22% accuracy)
-   Konversi otomatis ke fitur yang dibutuhkan
-   Estimasi yang reasonable untuk fitur title

### **✅ Praktis:**

-   Input berdasarkan aktivitas nyata user
-   Tidak memerlukan pengetahuan teknis
-   Hasil langsung dengan rekomendasi

## 📱 **CONTOH PENGGUNAAN**

### **Skenario: Mahasiswa Aktif**

```
🏃 Aktivitas Fisik:
- Olahraga: 4 kali seminggu
- Jarak: 15 km total
- Waktu: 240 menit total
- Hari aktif: 4 hari

💼 Produktivitas:
- Sesi belajar: 20 sesi
- Hari belajar: 6 hari

📊 Konteks:
- Tidur: Baik
- Stress: Sedang

→ Hasil: Risiko Rendah (85% confidence)
```

**Ini jauh lebih mudah daripada menghitung `title_balance_ratio` atau `gamification_balance` secara manual!** 🎉
