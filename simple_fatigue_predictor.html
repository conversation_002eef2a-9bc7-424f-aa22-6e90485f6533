<!DOCTYPE html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>🤖 Simple Fatigue Risk Predictor</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }

            .header h1 {
                font-size: 2.2em;
                margin-bottom: 10px;
            }

            .header p {
                font-size: 1.1em;
                opacity: 0.9;
            }

            .content {
                padding: 40px;
            }

            .form-section {
                margin-bottom: 30px;
                padding: 25px;
                background: #f8f9fa;
                border-radius: 10px;
                border-left: 4px solid #667eea;
            }

            .form-section h3 {
                color: #495057;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .form-row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #495057;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                padding: 12px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 16px;
                transition: border-color 0.3s;
            }

            .form-group input:focus,
            .form-group select:focus {
                outline: none;
                border-color: #667eea;
            }

            .help-text {
                font-size: 12px;
                color: #6c757d;
                margin-top: 5px;
            }

            .predict-btn {
                width: 100%;
                padding: 15px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 18px;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.3s;
                margin-top: 20px;
            }

            .predict-btn:hover {
                transform: translateY(-2px);
            }

            .results {
                margin-top: 30px;
                padding: 30px;
                background: #f8f9fa;
                border-radius: 10px;
                display: none;
            }

            .risk-card {
                text-align: center;
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 20px;
                color: white;
                font-size: 24px;
                font-weight: bold;
            }

            .risk-low {
                background: linear-gradient(135deg, #28a745, #20c997);
            }
            .risk-medium {
                background: linear-gradient(135deg, #ffc107, #fd7e14);
            }
            .risk-high {
                background: linear-gradient(135deg, #dc3545, #e83e8c);
            }

            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }

            .summary-card {
                background: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .summary-card h4 {
                color: #667eea;
                margin-bottom: 10px;
            }

            .summary-card .value {
                font-size: 24px;
                font-weight: bold;
                color: #495057;
            }

            .recommendations {
                margin-top: 20px;
                padding: 20px;
                border-radius: 10px;
            }

            .recommendations h3 {
                margin-bottom: 15px;
            }

            .recommendations ul {
                list-style: none;
            }

            .recommendations li {
                padding: 8px 0;
                padding-left: 25px;
                position: relative;
            }

            .recommendations li:before {
                content: '💡';
                position: absolute;
                left: 0;
            }

            @media (max-width: 768px) {
                .form-row {
                    grid-template-columns: 1fr;
                }

                .summary-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 Simple Fatigue Risk Predictor</h1>
                <p>Prediksi Risiko Kelelahan dari Aktivitas Mingguan</p>
            </div>

            <div class="content">
                <form id="fatigueForm">
                    <!-- Aktivitas Fisik -->
                    <div class="form-section">
                        <h3>🏃 Aktivitas Fisik (Olahraga)</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="strava_activities"
                                    >Jumlah aktivitas olahraga dalam
                                    seminggu</label
                                >
                                <input
                                    type="number"
                                    id="strava_activities"
                                    name="strava_activities"
                                    min="0"
                                    max="20"
                                    value="3"
                                    required
                                />
                                <div class="help-text">
                                    Berapa kali Anda berolahraga dalam seminggu?
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="activity_days"
                                    >Berapa hari Anda berolahraga?</label
                                >
                                <input
                                    type="number"
                                    id="activity_days"
                                    name="activity_days"
                                    min="0"
                                    max="7"
                                    value="3"
                                    required
                                />
                                <div class="help-text">
                                    Jumlah hari dalam seminggu Anda melakukan
                                    aktivitas fisik
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="total_distance"
                                    >Total jarak olahraga (km)</label
                                >
                                <input
                                    type="number"
                                    id="total_distance"
                                    name="total_distance"
                                    min="0"
                                    max="100"
                                    step="0.5"
                                    value="12.0"
                                    required
                                />
                                <div class="help-text">
                                    Total jarak yang Anda tempuh saat
                                    berolahraga
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="total_time"
                                    >Total waktu olahraga (menit)</label
                                >
                                <input
                                    type="number"
                                    id="total_time"
                                    name="total_time"
                                    min="0"
                                    max="2000"
                                    step="15"
                                    value="180"
                                    required
                                />
                                <div class="help-text">
                                    Total waktu yang Anda habiskan untuk
                                    berolahraga
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aktivitas Produktivitas -->
                    <div class="form-section">
                        <h3>💼 Aktivitas Produktivitas (Kerja/Belajar)</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pomokit_cycles"
                                    >Jumlah sesi kerja/belajar dalam
                                    seminggu</label
                                >
                                <input
                                    type="number"
                                    id="pomokit_cycles"
                                    name="pomokit_cycles"
                                    min="0"
                                    max="50"
                                    value="15"
                                    required
                                />
                                <div class="help-text">
                                    Berapa sesi kerja/belajar yang Anda lakukan?
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="work_days"
                                    >Berapa hari Anda bekerja/belajar?</label
                                >
                                <input
                                    type="number"
                                    id="work_days"
                                    name="work_days"
                                    min="0"
                                    max="7"
                                    value="5"
                                    required
                                />
                                <div class="help-text">
                                    Jumlah hari dalam seminggu Anda bekerja atau
                                    belajar
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="predict-btn">
                        🔮 Prediksi Risiko Kelelahan
                    </button>
                </form>

                <div id="results" class="results">
                    <div id="riskCard" class="risk-card">
                        <div id="riskLevel">Medium Risk</div>
                        <div style="font-size: 16px; margin-top: 10px">
                            Berdasarkan aktivitas mingguan Anda
                        </div>
                    </div>

                    <h3>📊 Ringkasan Aktivitas Anda</h3>
                    <div class="summary-grid">
                        <div class="summary-card">
                            <h4>🏃 Aktivitas Olahraga</h4>
                            <div class="value" id="summary-activities">
                                3 kali
                            </div>
                        </div>
                        <div class="summary-card">
                            <h4>📏 Total Jarak</h4>
                            <div class="value" id="summary-distance">
                                12.0 km
                            </div>
                        </div>
                        <div class="summary-card">
                            <h4>⏱️ Total Waktu</h4>
                            <div class="value" id="summary-time">180 menit</div>
                        </div>
                        <div class="summary-card">
                            <h4>💼 Sesi Kerja</h4>
                            <div class="value" id="summary-work">15 sesi</div>
                        </div>
                    </div>

                    <div id="recommendations" class="recommendations">
                        <h3>💡 Rekomendasi</h3>
                        <ul id="recommendationList">
                            <li>Perhatikan pola istirahat dan aktivitas</li>
                            <li>Lakukan aktivitas ringan untuk recovery</li>
                            <li>Monitor gejala kelelahan</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Form submission
            document
                .getElementById('fatigueForm')
                .addEventListener('submit', function (e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(this);
                    const data = Object.fromEntries(formData);

                    // Convert to numbers
                    const userInput = {
                        strava_activities: parseInt(data.strava_activities),
                        total_distance_km: parseFloat(data.total_distance),
                        total_time_minutes: parseInt(data.total_time),
                        activity_days: parseInt(data.activity_days),
                        pomokit_cycles: parseInt(data.pomokit_cycles),
                        work_days: parseInt(data.work_days),
                    };

                    // Calculate derived features (simplified)
                    const derivedFeatures = calculateDerivedFeatures(userInput);

                    // Simple rule-based prediction (demo)
                    const prediction = predictRisk(derivedFeatures, userInput);

                    // Display results
                    displayResults(prediction, userInput);
                });

            function calculateDerivedFeatures(input) {
                // Simplified feature calculation
                const avgDistance =
                    input.total_distance_km / Math.max(input.activity_days, 1);
                const avgTime =
                    input.total_time_minutes / Math.max(input.activity_days, 1);
                const avgCycles =
                    input.pomokit_cycles / Math.max(input.work_days, 1);
                const weeklyEfficiency = avgCycles;

                // Activity points (max 100)
                const activityPoints = Math.min(
                    (input.total_distance_km / 6) * 100,
                    100
                );

                // Productivity points (max 100)
                const productivityPoints = Math.min(
                    (input.pomokit_cycles / 5) * 100,
                    100
                );

                // Achievement rate
                const achievementRate =
                    (activityPoints + productivityPoints) / 200;

                return {
                    avgDistance,
                    avgTime,
                    avgCycles,
                    weeklyEfficiency,
                    activityPoints,
                    productivityPoints,
                    achievementRate,
                };
            }

            function predictRisk(derived, input) {
                // Simple rule-based prediction (replace with actual API call)
                let riskScore = 0;

                // Physical activity factors
                if (input.total_distance_km < 3) riskScore += 2;
                else if (input.total_distance_km < 6) riskScore += 1;

                if (input.strava_activities < 2) riskScore += 2;
                else if (input.strava_activities < 3) riskScore += 1;

                // Work factors
                if (input.pomokit_cycles > 25) riskScore += 2;
                else if (input.pomokit_cycles > 20) riskScore += 1;

                if (derived.avgCycles > 5) riskScore += 1;

                // Additional risk factors based on activity patterns
                if (derived.weeklyEfficiency > 6) riskScore += 1; // Too many cycles per day
                if (derived.avgDistance < 2) riskScore += 1; // Very low activity

                // Determine risk level
                if (riskScore <= 2) return 'low_risk';
                else if (riskScore <= 5) return 'medium_risk';
                else return 'high_risk';
            }

            function displayResults(prediction, userInput) {
                const resultsDiv = document.getElementById('results');
                const riskCard = document.getElementById('riskCard');
                const riskLevel = document.getElementById('riskLevel');

                // Risk labels and classes
                const riskLabels = {
                    low_risk: 'Risiko Rendah',
                    medium_risk: 'Risiko Sedang',
                    high_risk: 'Risiko Tinggi',
                };

                const riskClasses = {
                    low_risk: 'risk-low',
                    medium_risk: 'risk-medium',
                    high_risk: 'risk-high',
                };

                // Update risk display
                riskLevel.textContent = riskLabels[prediction];
                riskCard.className = 'risk-card ' + riskClasses[prediction];

                // Update summary
                document.getElementById('summary-activities').textContent =
                    userInput.strava_activities + ' kali';
                document.getElementById('summary-distance').textContent =
                    userInput.total_distance_km + ' km';
                document.getElementById('summary-time').textContent =
                    userInput.total_time_minutes + ' menit';
                document.getElementById('summary-work').textContent =
                    userInput.pomokit_cycles + ' sesi';

                // Update recommendations
                const recommendations = {
                    low_risk: [
                        'Kondisi baik, lanjutkan aktivitas normal',
                        'Tetap jaga pola istirahat yang sehat',
                        'Monitor kondisi secara berkala',
                        'Pertahankan gaya hidup sehat',
                    ],
                    medium_risk: [
                        'Perhatikan pola istirahat dan aktivitas',
                        'Lakukan aktivitas ringan untuk recovery',
                        'Monitor gejala kelelahan',
                        'Jaga keseimbangan antara aktivitas fisik dan mental',
                    ],
                    high_risk: [
                        'Segera istirahat dan kurangi aktivitas berat',
                        'Monitor kondisi kesehatan secara berkala',
                        'Pertimbangkan konsultasi dengan profesional kesehatan',
                        'Perbaiki kualitas tidur dan kurangi stress',
                    ],
                };

                const recommendationList =
                    document.getElementById('recommendationList');
                recommendationList.innerHTML = '';
                recommendations[prediction].forEach((rec) => {
                    const li = document.createElement('li');
                    li.textContent = rec;
                    recommendationList.appendChild(li);
                });

                // Show results
                resultsDiv.style.display = 'block';
                resultsDiv.scrollIntoView({ behavior: 'smooth' });
            }
        </script>
    </body>
</html>
