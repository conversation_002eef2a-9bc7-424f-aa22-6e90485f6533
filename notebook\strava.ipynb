{"cells": [{"cell_type": "markdown", "id": "b48e3661", "metadata": {}, "source": ["Data dari 10 maret sampai 2 mei 2025"]}, {"cell_type": "code", "execution_count": 1, "id": "66e6ca89", "metadata": {}, "outputs": [], "source": ["# import dependensi\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "1e321c1b", "metadata": {}, "source": ["## Dataset Strava"]}, {"cell_type": "code", "execution_count": 2, "id": "ea37e8fa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>created_at</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phone_number</th>\n", "      <th>updated_at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67cefaa28418358cd683286b</td>\n", "      <td>13840759213</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocIpjC...</td>\n", "      <td>Bagas IP</td>\n", "      <td>Mencoba Hal baru</td>\n", "      <td>10 Mar 2025 16:10 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>31m 14s</td>\n", "      <td>38 m</td>\n", "      <td>https://www.strava.com/activities/13840759213</td>\n", "      <td>2025-03-10T14:43:46.214Z</td>\n", "      <td>Valid</td>\n", "      <td>160605189</td>\n", "      <td>6285179935117</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>2025-03-11T03:11:39.179Z</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>2025-03-11T03:12:18.938Z</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>2025-03-11T04:09:07.276Z</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>2025-03-11T10:53:06.666Z</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>2025-06-05T08:16:05.946Z</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "      <td>0001-01-01T00:00:00.000Z</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>2025-06-05T09:28:18.220Z</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "      <td>0001-01-01T00:00:00.000Z</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>2025-06-05T12:13:24.618Z</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "      <td>0001-01-01T00:00:00.000Z</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>2025-06-05T12:43:15.358Z</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "      <td>0001-01-01T00:00:00.000Z</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064?utm_source=com.strava&amp;utm_medium=r...</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064?...</td>\n", "      <td>2025-06-05T16:44:57.367Z</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "      <td>0001-01-01T00:00:00.000Z</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>720 rows × 16 columns</p>\n", "</div>"], "text/plain": ["                          _id  \\\n", "0    67cefaa28418358cd683286b   \n", "1    67cfa9eb40da00d8c6402d4a   \n", "2    67cfaa1240da00d8c6402d4c   \n", "3    67cfb76340da00d8c6402d86   \n", "4    67d01612b249b1388ac7c4a7   \n", "..                        ...   \n", "715  68415245c28c453ed838d46b   \n", "716  684163322a714c784c3741f8   \n", "717  684189e4bb465f4e88bad56a   \n", "718  684190e3bb465f4e88bad575   \n", "719  6841c9894e147b00850eb771   \n", "\n", "                                           activity_id  \\\n", "0                                          13840759213   \n", "1                                          13848439185   \n", "2                                          13848440241   \n", "3                                          13848516402   \n", "4                                          13850512258   \n", "..                                                 ...   \n", "715                                        14701990198   \n", "716                                        14699992258   \n", "717                                        14703762366   \n", "718                                        14704011151   \n", "719  14706038064?utm_source=com.strava&utm_medium=r...   \n", "\n", "                                               picture  \\\n", "0    https://lh3.googleusercontent.com/a/ACg8ocIpjC...   \n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "..                                                 ...   \n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "0                   Bagas IP  Mencoba Hal baru  10 Mar 2025 16:10 WIB   \n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "715        <PERSON><PERSON>        Going home  05 Jun 2025 15:06 WIB   \n", "716           Raditya  Rizki             5 Jun  05 Jun 2025 07:29 WIB   \n", "717       <PERSON>            c<PERSON>  05 Jun 2025 19:10 WIB   \n", "718    <PERSON><PERSON><PERSON>      Evening Walk  05 Jun 2025 19:37 WIB   \n", "719              hasan pohan     <PERSON>  05 Jun 2025 23:43 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "0         Walk   3.0 km     31m 14s      38 m   \n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "..         ...      ...         ...       ...   \n", "715        Run   2.2 km      5m 49s       0 m   \n", "716        Run   7.8 km     20m 46s     121 m   \n", "717        Run  11.3 km      2m 20s     151 m   \n", "718       Walk   3.0 km      5m 17s      60 m   \n", "719        Run   7.0 km         34s      10 m   \n", "\n", "                                         link_activity  \\\n", "0        https://www.strava.com/activities/13840759213   \n", "1        https://www.strava.com/activities/13848439185   \n", "2        https://www.strava.com/activities/13848440241   \n", "3        https://www.strava.com/activities/13848516402   \n", "4        https://www.strava.com/activities/13850512258   \n", "..                                                 ...   \n", "715      https://www.strava.com/activities/14701990198   \n", "716      https://www.strava.com/activities/14699992258   \n", "717      https://www.strava.com/activities/14703762366   \n", "718      https://www.strava.com/activities/14704011151   \n", "719  https://www.strava.com/activities/14706038064?...   \n", "\n", "                   created_at status  athlete_id    phone_number  \\\n", "0    2025-03-10T14:43:46.214Z  Valid   160605189   6285179935117   \n", "1    2025-03-11T03:11:39.179Z  Valid   144975651   6289662315611   \n", "2    2025-03-11T03:12:18.938Z  Valid   161163973  62895329746650   \n", "3    2025-03-11T04:09:07.276Z  Valid   161200570   6285722341788   \n", "4    2025-03-11T10:53:06.666Z  Valid   161209993   6282232023945   \n", "..                        ...    ...         ...             ...   \n", "715  2025-06-05T08:16:05.946Z  Valid   161143528   6288970788847   \n", "716  2025-06-05T09:28:18.220Z  Valid   161216121   6285860602948   \n", "717  2025-06-05T12:13:24.618Z  Valid   161376292   6289685587547   \n", "718  2025-06-05T12:43:15.358Z  Valid   161216030   6289604482359   \n", "719  2025-06-05T16:44:57.367Z  Valid   161402876   6282161299141   \n", "\n", "                   updated_at  \n", "0                         NaN  \n", "1                         NaN  \n", "2                         NaN  \n", "3                         NaN  \n", "4                         NaN  \n", "..                        ...  \n", "715  0001-01-01T00:00:00.000Z  \n", "716  0001-01-01T00:00:00.000Z  \n", "717  0001-01-01T00:00:00.000Z  \n", "718  0001-01-01T00:00:00.000Z  \n", "719  0001-01-01T00:00:00.000Z  \n", "\n", "[720 rows x 16 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# read csv file\n", "file_path_strava = 'dataset/strava.csv'\n", "df_strava = pd.read_csv(file_path_strava)\n", "df_strava"]}, {"cell_type": "code", "execution_count": 3, "id": "53aa28a2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67cefaa28418358cd683286b</td>\n", "      <td>13840759213</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocIpjC...</td>\n", "      <td>Bagas IP</td>\n", "      <td>Mencoba Hal baru</td>\n", "      <td>10 Mar 2025 16:10 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>31m 14s</td>\n", "      <td>38 m</td>\n", "      <td>https://www.strava.com/activities/13840759213</td>\n", "      <td>Valid</td>\n", "      <td>160605189</td>\n", "      <td>6285179935117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064?utm_source=com.strava&amp;utm_medium=r...</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064?...</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>720 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                          _id  \\\n", "0    67cefaa28418358cd683286b   \n", "1    67cfa9eb40da00d8c6402d4a   \n", "2    67cfaa1240da00d8c6402d4c   \n", "3    67cfb76340da00d8c6402d86   \n", "4    67d01612b249b1388ac7c4a7   \n", "..                        ...   \n", "715  68415245c28c453ed838d46b   \n", "716  684163322a714c784c3741f8   \n", "717  684189e4bb465f4e88bad56a   \n", "718  684190e3bb465f4e88bad575   \n", "719  6841c9894e147b00850eb771   \n", "\n", "                                           activity_id  \\\n", "0                                          13840759213   \n", "1                                          13848439185   \n", "2                                          13848440241   \n", "3                                          13848516402   \n", "4                                          13850512258   \n", "..                                                 ...   \n", "715                                        14701990198   \n", "716                                        14699992258   \n", "717                                        14703762366   \n", "718                                        14704011151   \n", "719  14706038064?utm_source=com.strava&utm_medium=r...   \n", "\n", "                                               picture  \\\n", "0    https://lh3.googleusercontent.com/a/ACg8ocIpjC...   \n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "..                                                 ...   \n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "0                   Bagas IP  Mencoba Hal baru  10 Mar 2025 16:10 WIB   \n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "715        <PERSON><PERSON>        Going home  05 Jun 2025 15:06 WIB   \n", "716           Raditya  Rizki             5 Jun  05 Jun 2025 07:29 WIB   \n", "717       <PERSON>            c<PERSON>  05 Jun 2025 19:10 WIB   \n", "718    <PERSON><PERSON><PERSON>      Evening Walk  05 Jun 2025 19:37 WIB   \n", "719              hasan pohan     <PERSON>  05 Jun 2025 23:43 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "0         Walk   3.0 km     31m 14s      38 m   \n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "..         ...      ...         ...       ...   \n", "715        Run   2.2 km      5m 49s       0 m   \n", "716        Run   7.8 km     20m 46s     121 m   \n", "717        Run  11.3 km      2m 20s     151 m   \n", "718       Walk   3.0 km      5m 17s      60 m   \n", "719        Run   7.0 km         34s      10 m   \n", "\n", "                                         link_activity status  athlete_id  \\\n", "0        https://www.strava.com/activities/13840759213  Valid   160605189   \n", "1        https://www.strava.com/activities/13848439185  Valid   144975651   \n", "2        https://www.strava.com/activities/13848440241  Valid   161163973   \n", "3        https://www.strava.com/activities/13848516402  Valid   161200570   \n", "4        https://www.strava.com/activities/13850512258  Valid   161209993   \n", "..                                                 ...    ...         ...   \n", "715      https://www.strava.com/activities/14701990198  Valid   161143528   \n", "716      https://www.strava.com/activities/14699992258  Valid   161216121   \n", "717      https://www.strava.com/activities/14703762366  Valid   161376292   \n", "718      https://www.strava.com/activities/14704011151  Valid   161216030   \n", "719  https://www.strava.com/activities/14706038064?...  Valid   161402876   \n", "\n", "        phonenumber  \n", "0     6285179935117  \n", "1     6289662315611  \n", "2    62895329746650  \n", "3     6285722341788  \n", "4     6282232023945  \n", "..              ...  \n", "715   6288970788847  \n", "716   6285860602948  \n", "717   6289685587547  \n", "718   6289604482359  \n", "719   6282161299141  \n", "\n", "[720 rows x 14 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# drop kolom yang tidak diperlukan\n", "df_strava.drop(columns=['created_at','updated_at'], inplace=True)\n", "df_strava.rename(columns={'phone_number': 'phonenumber'}, inplace=True)\n", "df_strava"]}, {"cell_type": "code", "execution_count": 4, "id": "bf55e341", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id               0\n", "activity_id       1\n", "picture           0\n", "name              0\n", "title             0\n", "date_time         0\n", "type_sport        0\n", "distance          3\n", "moving_time       3\n", "elevation        10\n", "link_activity     0\n", "status            0\n", "athlete_id        0\n", "phonenumber       0\n", "dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# cek data null atau NaN\n", "df_strava.isna().sum()"]}, {"cell_type": "code", "execution_count": 5, "id": "095e9b13", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>67d17ac45caddbdaaf0a8595</td>\n", "      <td>13860691649</td>\n", "      <td>https://lh3.googleusercontent.com/a-/AOh14Gi9d...</td>\n", "      <td><PERSON></td>\n", "      <td>Walk2</td>\n", "      <td>12 Mar 2025 17:56 WIB</td>\n", "      <td>Walk</td>\n", "      <td>2.0 km</td>\n", "      <td>26m 19s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13860691649</td>\n", "      <td>Invalid</td>\n", "      <td>74075838</td>\n", "      <td>6285157392215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>67d22379601038f9a521a710</td>\n", "      <td>13866887714</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL8Ib...</td>\n", "      <td>Zidan  Ardi<PERSON>yah</td>\n", "      <td>mental health</td>\n", "      <td>13 Mar 2025 06:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>34m 54s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13866887714</td>\n", "      <td>Valid</td>\n", "      <td>161207369</td>\n", "      <td>6285640389741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>67d29afb89bfe9f7a4af89d7</td>\n", "      <td>13863867185</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocJPsj...</td>\n", "      <td>Muhammd Mallq</td>\n", "      <td>AfternoonWalj2</td>\n", "      <td>12 Mar 2025 17:55 WIB</td>\n", "      <td>Walk</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13863867185</td>\n", "      <td>Invalid</td>\n", "      <td>161316264</td>\n", "      <td>6282122339125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>67d2a00f89bfe9f7a4af89e1</td>\n", "      <td>13868831735</td>\n", "      <td>https://lh3.googleusercontent.com/a-/AOh14Gh9r...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Afternoon Run</td>\n", "      <td>13 Mar 2025 15:34 WIB</td>\n", "      <td>Run</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13868831735</td>\n", "      <td>Invalid</td>\n", "      <td>90643556</td>\n", "      <td>6285721368639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>67d2a40389bfe9f7a4af89ec</td>\n", "      <td>13868914133</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocJcrx...</td>\n", "      <td>Nanda SR</td>\n", "      <td>Pulang</td>\n", "      <td>13 Mar 2025 16:01 WIB</td>\n", "      <td>Ride</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13868914133</td>\n", "      <td>Invalid</td>\n", "      <td>161310591</td>\n", "      <td>6285314627451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>67d41fd74fb3aa876bdf0881</td>\n", "      <td>13879107852</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>14 Mar 2025 08:52 WIB</td>\n", "      <td>Walk</td>\n", "      <td>1.5 km</td>\n", "      <td>14m 12s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13879107852</td>\n", "      <td>Invalid</td>\n", "      <td>161408301</td>\n", "      <td>6282191365504</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>67d61f5394f01ffccce8849b</td>\n", "      <td>13893825332</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL8Ib...</td>\n", "      <td>Zidan  Ardi<PERSON>yah</td>\n", "      <td>mental health2</td>\n", "      <td>16 Mar 2025 07:12 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>31m 45s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13893825332</td>\n", "      <td>Valid</td>\n", "      <td>161207369</td>\n", "      <td>6285640389741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>67d7ebe87ffe3848ada8455c</td>\n", "      <td>13879107852</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>14 Mar 2025 08:52 WIB</td>\n", "      <td>Walk</td>\n", "      <td>1.5 km</td>\n", "      <td>14m 12s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/13879107852</td>\n", "      <td>Duplicate</td>\n", "      <td>161408301</td>\n", "      <td>6282191365504</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>67d7f0d47ffe3848ada845be</td>\n", "      <td>11959785546</td>\n", "      <td>https://lh3.googleusercontent.com/a/AAcHTteUqY...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Afternoon Run</td>\n", "      <td>23 Jul 2024 17:48 WIB</td>\n", "      <td>Run</td>\n", "      <td>6.6 km</td>\n", "      <td>55m 53s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/11959785546</td>\n", "      <td>Fraudulent</td>\n", "      <td>121862322</td>\n", "      <td>6285779371516</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>67d7f13b7ffe3848ada845c2</td>\n", "      <td>11951206011</td>\n", "      <td>https://lh3.googleusercontent.com/a/AAcHTteUqY...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Evening Run</td>\n", "      <td>22 Jul 2024 18:01 WIB</td>\n", "      <td>Run</td>\n", "      <td>6.0 km</td>\n", "      <td>46m 47s</td>\n", "      <td>NaN</td>\n", "      <td>https://www.strava.com/activities/11951206011</td>\n", "      <td>Fraudulent</td>\n", "      <td>121862322</td>\n", "      <td>6285779371516</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          _id  activity_id  \\\n", "19   67d17ac45caddbdaaf0a8595  13860691649   \n", "22   67d22379601038f9a521a710  13866887714   \n", "29   67d29afb89bfe9f7a4af89d7  13863867185   \n", "30   67d2a00f89bfe9f7a4af89e1  13868831735   \n", "31   67d2a40389bfe9f7a4af89ec  13868914133   \n", "63   67d41fd74fb3aa876bdf0881  13879107852   \n", "96   67d61f5394f01ffccce8849b  13893825332   \n", "135  67d7ebe87ffe3848ada8455c  13879107852   \n", "143  67d7f0d47ffe3848ada845be  11959785546   \n", "144  67d7f13b7ffe3848ada845c2  11951206011   \n", "\n", "                                               picture                  name  \\\n", "19   https://lh3.googleusercontent.com/a-/AOh14Gi9d...        <PERSON>   \n", "22   https://lh3.googleusercontent.com/a/ACg8ocL8Ib...     Zidan  Ardiansyah   \n", "29   https://lh3.googleusercontent.com/a/ACg8ocJPsj...         Muhammd Mallq   \n", "30   https://lh3.googleusercontent.com/a-/AOh14Gh9r...       <PERSON><PERSON>   \n", "31   https://lh3.googleusercontent.com/a/ACg8ocJcrx...              Nanda SR   \n", "63   https://dgalywyr863hv.cloudfront.net/pictures/...         <PERSON>ya <PERSON>   \n", "96   https://lh3.googleusercontent.com/a/ACg8ocL8Ib...     Zidan  Ardiansyah   \n", "135  https://dgalywyr863hv.cloudfront.net/pictures/...         <PERSON>ya <PERSON>   \n", "143  https://lh3.googleusercontent.com/a/AAcHTteUqY...  <PERSON><PERSON> i<PERSON>   \n", "144  https://lh3.googleusercontent.com/a/AAcHTteUqY...  <PERSON><PERSON> i<PERSON>   \n", "\n", "              title              date_time type_sport distance moving_time  \\\n", "19            Walk2  12 Mar 2025 17:56 WIB       Walk   2.0 km     26m 19s   \n", "22    mental health  13 Mar 2025 06:34 WIB       Walk   3.0 km     34m 54s   \n", "29   AfternoonWalj2  12 Mar 2025 17:55 WIB       Walk      NaN         NaN   \n", "30    Afternoon Run  13 Mar 2025 15:34 WIB        Run      NaN         NaN   \n", "31           Pulang  13 Mar 2025 16:01 WIB       Ride      NaN         NaN   \n", "63     Evening Walk  14 Mar 2025 08:52 WIB       Walk   1.5 km     14m 12s   \n", "96   mental health2  16 Mar 2025 07:12 WIB       Walk   3.2 km     31m 45s   \n", "135    Evening Walk  14 Mar 2025 08:52 WIB       Walk   1.5 km     14m 12s   \n", "143   Afternoon Run  23 Jul 2024 17:48 WIB        Run   6.6 km     55m 53s   \n", "144     Evening Run  22 Jul 2024 18:01 WIB        Run   6.0 km     46m 47s   \n", "\n", "    elevation                                  link_activity      status  \\\n", "19        NaN  https://www.strava.com/activities/13860691649     Invalid   \n", "22        NaN  https://www.strava.com/activities/13866887714       Valid   \n", "29        NaN  https://www.strava.com/activities/13863867185     Invalid   \n", "30        NaN  https://www.strava.com/activities/13868831735     Invalid   \n", "31        NaN  https://www.strava.com/activities/13868914133     Invalid   \n", "63        NaN  https://www.strava.com/activities/13879107852     Invalid   \n", "96        NaN  https://www.strava.com/activities/13893825332       Valid   \n", "135       NaN  https://www.strava.com/activities/13879107852   Duplicate   \n", "143       NaN  https://www.strava.com/activities/11959785546  Fraudulent   \n", "144       NaN  https://www.strava.com/activities/11951206011  Fraudulent   \n", "\n", "     athlete_id    phonenumber  \n", "19     74075838  6285157392215  \n", "22    161207369  6285640389741  \n", "29    161316264  6282122339125  \n", "30     90643556  6285721368639  \n", "31    161310591  6285314627451  \n", "63    161408301  6282191365504  \n", "96    161207369  6285640389741  \n", "135   161408301  6282191365504  \n", "143   121862322  6285779371516  \n", "144   121862322  6285779371516  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# tampil data yang memiliki nilai null atau NaN\n", "df_strava[df_strava['elevation'].isna()]"]}, {"cell_type": "code", "execution_count": 6, "id": "5b6e1ab0", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id              0\n", "activity_id      1\n", "picture          0\n", "name             0\n", "title            0\n", "date_time        0\n", "type_sport       0\n", "distance         3\n", "moving_time      3\n", "elevation        0\n", "link_activity    0\n", "status           0\n", "athlete_id       0\n", "phonenumber      0\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# isi nilai null atau <PERSON><PERSON> '0 m'\n", "df_strava.fillna(value={'elevation': '0 m'}, inplace=True)\n", "df_strava.isna().sum()"]}, {"cell_type": "code", "execution_count": 7, "id": "94e2afd5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67cefaa28418358cd683286b</td>\n", "      <td>13840759213</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocIpjC...</td>\n", "      <td>Bagas IP</td>\n", "      <td>Mencoba Hal baru</td>\n", "      <td>10 Mar 2025 16:10 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>31m 14s</td>\n", "      <td>38 m</td>\n", "      <td>https://www.strava.com/activities/13840759213</td>\n", "      <td>Valid</td>\n", "      <td>160605189</td>\n", "      <td>6285179935117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064?utm_source=com.strava&amp;utm_medium=r...</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064?...</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>630 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                          _id  \\\n", "0    67cefaa28418358cd683286b   \n", "1    67cfa9eb40da00d8c6402d4a   \n", "2    67cfaa1240da00d8c6402d4c   \n", "3    67cfb76340da00d8c6402d86   \n", "4    67d01612b249b1388ac7c4a7   \n", "..                        ...   \n", "715  68415245c28c453ed838d46b   \n", "716  684163322a714c784c3741f8   \n", "717  684189e4bb465f4e88bad56a   \n", "718  684190e3bb465f4e88bad575   \n", "719  6841c9894e147b00850eb771   \n", "\n", "                                           activity_id  \\\n", "0                                          13840759213   \n", "1                                          13848439185   \n", "2                                          13848440241   \n", "3                                          13848516402   \n", "4                                          13850512258   \n", "..                                                 ...   \n", "715                                        14701990198   \n", "716                                        14699992258   \n", "717                                        14703762366   \n", "718                                        14704011151   \n", "719  14706038064?utm_source=com.strava&utm_medium=r...   \n", "\n", "                                               picture  \\\n", "0    https://lh3.googleusercontent.com/a/ACg8ocIpjC...   \n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "..                                                 ...   \n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "0                   Bagas IP  Mencoba Hal baru  10 Mar 2025 16:10 WIB   \n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "715        <PERSON><PERSON>        Going home  05 Jun 2025 15:06 WIB   \n", "716           Raditya  Rizki             5 Jun  05 Jun 2025 07:29 WIB   \n", "717       <PERSON>            c<PERSON>  05 Jun 2025 19:10 WIB   \n", "718    <PERSON><PERSON><PERSON>      Evening Walk  05 Jun 2025 19:37 WIB   \n", "719              hasan pohan     <PERSON>  05 Jun 2025 23:43 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "0         Walk   3.0 km     31m 14s      38 m   \n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "..         ...      ...         ...       ...   \n", "715        Run   2.2 km      5m 49s       0 m   \n", "716        Run   7.8 km     20m 46s     121 m   \n", "717        Run  11.3 km      2m 20s     151 m   \n", "718       Walk   3.0 km      5m 17s      60 m   \n", "719        Run   7.0 km         34s      10 m   \n", "\n", "                                         link_activity status  athlete_id  \\\n", "0        https://www.strava.com/activities/13840759213  Valid   160605189   \n", "1        https://www.strava.com/activities/13848439185  Valid   144975651   \n", "2        https://www.strava.com/activities/13848440241  Valid   161163973   \n", "3        https://www.strava.com/activities/13848516402  Valid   161200570   \n", "4        https://www.strava.com/activities/13850512258  Valid   161209993   \n", "..                                                 ...    ...         ...   \n", "715      https://www.strava.com/activities/14701990198  Valid   161143528   \n", "716      https://www.strava.com/activities/14699992258  Valid   161216121   \n", "717      https://www.strava.com/activities/14703762366  Valid   161376292   \n", "718      https://www.strava.com/activities/14704011151  Valid   161216030   \n", "719  https://www.strava.com/activities/14706038064?...  Valid   161402876   \n", "\n", "        phonenumber  \n", "0     6285179935117  \n", "1     6289662315611  \n", "2    62895329746650  \n", "3     6285722341788  \n", "4     6282232023945  \n", "..              ...  \n", "715   6288970788847  \n", "716   6285860602948  \n", "717   6289685587547  \n", "718   6289604482359  \n", "719   6282161299141  \n", "\n", "[630 rows x 14 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# tampilkan data dengan status 'Valid'\n", "df_strava_valid = df_strava[df_strava['status'] == 'Valid']\n", "df_strava_valid"]}, {"cell_type": "code", "execution_count": 8, "id": "970e92b1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67cefaa28418358cd683286b</td>\n", "      <td>13840759213</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocIpjC...</td>\n", "      <td>Bagas IP</td>\n", "      <td>Mencoba Hal baru</td>\n", "      <td>10 Mar 2025 16:10 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>31m 14s</td>\n", "      <td>38 m</td>\n", "      <td>https://www.strava.com/activities/13840759213</td>\n", "      <td>Valid</td>\n", "      <td>160605189</td>\n", "      <td>6285179935117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064?utm_source=com.strava&amp;utm_medium=r...</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064?...</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>630 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                          _id  \\\n", "0    67cefaa28418358cd683286b   \n", "1    67cfa9eb40da00d8c6402d4a   \n", "2    67cfaa1240da00d8c6402d4c   \n", "3    67cfb76340da00d8c6402d86   \n", "4    67d01612b249b1388ac7c4a7   \n", "..                        ...   \n", "715  68415245c28c453ed838d46b   \n", "716  684163322a714c784c3741f8   \n", "717  684189e4bb465f4e88bad56a   \n", "718  684190e3bb465f4e88bad575   \n", "719  6841c9894e147b00850eb771   \n", "\n", "                                           activity_id  \\\n", "0                                          13840759213   \n", "1                                          13848439185   \n", "2                                          13848440241   \n", "3                                          13848516402   \n", "4                                          13850512258   \n", "..                                                 ...   \n", "715                                        14701990198   \n", "716                                        14699992258   \n", "717                                        14703762366   \n", "718                                        14704011151   \n", "719  14706038064?utm_source=com.strava&utm_medium=r...   \n", "\n", "                                               picture  \\\n", "0    https://lh3.googleusercontent.com/a/ACg8ocIpjC...   \n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "..                                                 ...   \n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "0                   Bagas IP  Mencoba Hal baru  10 Mar 2025 16:10 WIB   \n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "715        <PERSON><PERSON>        Going home  05 Jun 2025 15:06 WIB   \n", "716           Raditya  Rizki             5 Jun  05 Jun 2025 07:29 WIB   \n", "717       <PERSON>            c<PERSON>  05 Jun 2025 19:10 WIB   \n", "718    <PERSON><PERSON><PERSON>      Evening Walk  05 Jun 2025 19:37 WIB   \n", "719              hasan pohan     <PERSON>  05 Jun 2025 23:43 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "0         Walk   3.0 km     31m 14s      38 m   \n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "..         ...      ...         ...       ...   \n", "715        Run   2.2 km      5m 49s       0 m   \n", "716        Run   7.8 km     20m 46s     121 m   \n", "717        Run  11.3 km      2m 20s     151 m   \n", "718       Walk   3.0 km      5m 17s      60 m   \n", "719        Run   7.0 km         34s      10 m   \n", "\n", "                                         link_activity status  athlete_id  \\\n", "0        https://www.strava.com/activities/13840759213  Valid   160605189   \n", "1        https://www.strava.com/activities/13848439185  Valid   144975651   \n", "2        https://www.strava.com/activities/13848440241  Valid   161163973   \n", "3        https://www.strava.com/activities/13848516402  Valid   161200570   \n", "4        https://www.strava.com/activities/13850512258  Valid   161209993   \n", "..                                                 ...    ...         ...   \n", "715      https://www.strava.com/activities/14701990198  Valid   161143528   \n", "716      https://www.strava.com/activities/14699992258  Valid   161216121   \n", "717      https://www.strava.com/activities/14703762366  Valid   161376292   \n", "718      https://www.strava.com/activities/14704011151  Valid   161216030   \n", "719  https://www.strava.com/activities/14706038064?...  Valid   161402876   \n", "\n", "        phonenumber  \n", "0     6285179935117  \n", "1     6289662315611  \n", "2    62895329746650  \n", "3     6285722341788  \n", "4     6282232023945  \n", "..              ...  \n", "715   6288970788847  \n", "716   6285860602948  \n", "717   6289685587547  \n", "718   6289604482359  \n", "719   6282161299141  \n", "\n", "[630 rows x 14 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_strava_valid"]}, {"cell_type": "code", "execution_count": 9, "id": "ae6f8cb7", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id              0\n", "activity_id      0\n", "picture          0\n", "name             0\n", "title            0\n", "date_time        0\n", "type_sport       0\n", "distance         0\n", "moving_time      0\n", "elevation        0\n", "link_activity    0\n", "status           0\n", "athlete_id       0\n", "phonenumber      0\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_strava_valid.isna().sum()"]}, {"cell_type": "code", "execution_count": 10, "id": "2ab1918d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37796\\1295534464.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_strava_valid['sport_score'] = df_strava_valid['type_sport'].map({\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "      <th>sport_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67cefaa28418358cd683286b</td>\n", "      <td>13840759213</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocIpjC...</td>\n", "      <td>Bagas IP</td>\n", "      <td>Mencoba Hal baru</td>\n", "      <td>10 Mar 2025 16:10 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>31m 14s</td>\n", "      <td>38 m</td>\n", "      <td>https://www.strava.com/activities/13840759213</td>\n", "      <td>Valid</td>\n", "      <td>160605189</td>\n", "      <td>6285179935117</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064?utm_source=com.strava&amp;utm_medium=r...</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064?...</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>630 rows × 15 columns</p>\n", "</div>"], "text/plain": ["                          _id  \\\n", "0    67cefaa28418358cd683286b   \n", "1    67cfa9eb40da00d8c6402d4a   \n", "2    67cfaa1240da00d8c6402d4c   \n", "3    67cfb76340da00d8c6402d86   \n", "4    67d01612b249b1388ac7c4a7   \n", "..                        ...   \n", "715  68415245c28c453ed838d46b   \n", "716  684163322a714c784c3741f8   \n", "717  684189e4bb465f4e88bad56a   \n", "718  684190e3bb465f4e88bad575   \n", "719  6841c9894e147b00850eb771   \n", "\n", "                                           activity_id  \\\n", "0                                          13840759213   \n", "1                                          13848439185   \n", "2                                          13848440241   \n", "3                                          13848516402   \n", "4                                          13850512258   \n", "..                                                 ...   \n", "715                                        14701990198   \n", "716                                        14699992258   \n", "717                                        14703762366   \n", "718                                        14704011151   \n", "719  14706038064?utm_source=com.strava&utm_medium=r...   \n", "\n", "                                               picture  \\\n", "0    https://lh3.googleusercontent.com/a/ACg8ocIpjC...   \n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "..                                                 ...   \n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "0                   Bagas IP  Mencoba Hal baru  10 Mar 2025 16:10 WIB   \n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "715        <PERSON><PERSON>        Going home  05 Jun 2025 15:06 WIB   \n", "716           Raditya  Rizki             5 Jun  05 Jun 2025 07:29 WIB   \n", "717       <PERSON>            c<PERSON>  05 Jun 2025 19:10 WIB   \n", "718    <PERSON><PERSON><PERSON>      Evening Walk  05 Jun 2025 19:37 WIB   \n", "719              hasan pohan     <PERSON>  05 Jun 2025 23:43 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "0         Walk   3.0 km     31m 14s      38 m   \n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "..         ...      ...         ...       ...   \n", "715        Run   2.2 km      5m 49s       0 m   \n", "716        Run   7.8 km     20m 46s     121 m   \n", "717        Run  11.3 km      2m 20s     151 m   \n", "718       Walk   3.0 km      5m 17s      60 m   \n", "719        Run   7.0 km         34s      10 m   \n", "\n", "                                         link_activity status  athlete_id  \\\n", "0        https://www.strava.com/activities/13840759213  Valid   160605189   \n", "1        https://www.strava.com/activities/13848439185  Valid   144975651   \n", "2        https://www.strava.com/activities/13848440241  Valid   161163973   \n", "3        https://www.strava.com/activities/13848516402  Valid   161200570   \n", "4        https://www.strava.com/activities/13850512258  Valid   161209993   \n", "..                                                 ...    ...         ...   \n", "715      https://www.strava.com/activities/14701990198  Valid   161143528   \n", "716      https://www.strava.com/activities/14699992258  Valid   161216121   \n", "717      https://www.strava.com/activities/14703762366  Valid   161376292   \n", "718      https://www.strava.com/activities/14704011151  Valid   161216030   \n", "719  https://www.strava.com/activities/14706038064?...  Valid   161402876   \n", "\n", "        phonenumber  sport_score  \n", "0     6285179935117          1.0  \n", "1     6289662315611          1.0  \n", "2    62895329746650          1.0  \n", "3     6285722341788          1.0  \n", "4     6282232023945          1.0  \n", "..              ...          ...  \n", "715   6288970788847          3.0  \n", "716   6285860602948          3.0  \n", "717   6289685587547          3.0  \n", "718   6289604482359          1.0  \n", "719   6282161299141          3.0  \n", "\n", "[630 rows x 15 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_strava_valid['sport_score'] = df_strava_valid['type_sport'].map({\n", "    'Ride': 0,   # a<PERSON><PERSON><PERSON> (bermotor)\n", "    'Walk': 1,\n", "    'Hike': 2,\n", "    'Run': 3\n", "})\n", "\n", "df_strava_valid"]}, {"cell_type": "code", "execution_count": 11, "id": "e012ec18", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37796\\1279007818.py:6: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_strava_valid[kolom] = df_strava_valid[kolom].str.replace(\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "      <th>sport_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67cefaa28418358cd683286b</td>\n", "      <td>13840759213</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocIpjC...</td>\n", "      <td>Bagas IP</td>\n", "      <td>Mencoba Hal baru</td>\n", "      <td>10 Mar 2025 16:10 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>31m 14s</td>\n", "      <td>38 m</td>\n", "      <td>https://www.strava.com/activities/13840759213</td>\n", "      <td>Valid</td>\n", "      <td>160605189</td>\n", "      <td>6285179935117</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>630 rows × 15 columns</p>\n", "</div>"], "text/plain": ["                          _id  activity_id  \\\n", "0    67cefaa28418358cd683286b  13840759213   \n", "1    67cfa9eb40da00d8c6402d4a  13848439185   \n", "2    67cfaa1240da00d8c6402d4c  13848440241   \n", "3    67cfb76340da00d8c6402d86  13848516402   \n", "4    67d01612b249b1388ac7c4a7  13850512258   \n", "..                        ...          ...   \n", "715  68415245c28c453ed838d46b  14701990198   \n", "716  684163322a714c784c3741f8  14699992258   \n", "717  684189e4bb465f4e88bad56a  14703762366   \n", "718  684190e3bb465f4e88bad575  14704011151   \n", "719  6841c9894e147b00850eb771  14706038064   \n", "\n", "                                               picture  \\\n", "0    https://lh3.googleusercontent.com/a/ACg8ocIpjC...   \n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "..                                                 ...   \n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "0                   Bagas IP  Mencoba Hal baru  10 Mar 2025 16:10 WIB   \n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "715        <PERSON><PERSON>        Going home  05 Jun 2025 15:06 WIB   \n", "716           Raditya  Rizki             5 Jun  05 Jun 2025 07:29 WIB   \n", "717       <PERSON>            c<PERSON>  05 Jun 2025 19:10 WIB   \n", "718    <PERSON><PERSON><PERSON>      Evening Walk  05 Jun 2025 19:37 WIB   \n", "719              hasan pohan     <PERSON>  05 Jun 2025 23:43 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "0         Walk   3.0 km     31m 14s      38 m   \n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "..         ...      ...         ...       ...   \n", "715        Run   2.2 km      5m 49s       0 m   \n", "716        Run   7.8 km     20m 46s     121 m   \n", "717        Run  11.3 km      2m 20s     151 m   \n", "718       Walk   3.0 km      5m 17s      60 m   \n", "719        Run   7.0 km         34s      10 m   \n", "\n", "                                     link_activity status  athlete_id  \\\n", "0    https://www.strava.com/activities/13840759213  Valid   160605189   \n", "1    https://www.strava.com/activities/13848439185  Valid   144975651   \n", "2    https://www.strava.com/activities/13848440241  Valid   161163973   \n", "3    https://www.strava.com/activities/13848516402  Valid   161200570   \n", "4    https://www.strava.com/activities/13850512258  Valid   161209993   \n", "..                                             ...    ...         ...   \n", "715  https://www.strava.com/activities/14701990198  Valid   161143528   \n", "716  https://www.strava.com/activities/14699992258  Valid   161216121   \n", "717  https://www.strava.com/activities/14703762366  Valid   161376292   \n", "718  https://www.strava.com/activities/14704011151  Valid   161216030   \n", "719  https://www.strava.com/activities/14706038064  Valid   161402876   \n", "\n", "        phonenumber  sport_score  \n", "0     6285179935117          1.0  \n", "1     6289662315611          1.0  \n", "2    62895329746650          1.0  \n", "3     6285722341788          1.0  \n", "4     6282232023945          1.0  \n", "..              ...          ...  \n", "715   6288970788847          3.0  \n", "716   6285860602948          3.0  \n", "717   6289685587547          3.0  \n", "718   6289604482359          1.0  \n", "719   6282161299141          3.0  \n", "\n", "[630 rows x 15 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# <PERSON>lo<PERSON> yang ingin di<PERSON>\n", "kolom_target = ['activity_id', 'link_activity']\n", "\n", "# Hapus bagian query string dari setiap kolom\n", "for kolom in kolom_target:\n", "    df_strava_valid[kolom] = df_strava_valid[kolom].str.replace(\n", "        '?utm_source=com.strava&utm_medium=referral', '', regex=False\n", "    )\n", "\n", "df_strava_valid"]}, {"cell_type": "code", "execution_count": 12, "id": "20459a3a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "      <th>sport_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>715</th>\n", "      <td>68415245c28c453ed838d46b</td>\n", "      <td>14701990198</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Going home</td>\n", "      <td>05 Jun 2025 15:06 WIB</td>\n", "      <td>Run</td>\n", "      <td>2.2 km</td>\n", "      <td>5m 49s</td>\n", "      <td>0 m</td>\n", "      <td>https://www.strava.com/activities/14701990198</td>\n", "      <td>Valid</td>\n", "      <td>161143528</td>\n", "      <td>6288970788847</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>684163322a714c784c3741f8</td>\n", "      <td>14699992258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5 Jun</td>\n", "      <td>05 Jun 2025 07:29 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.8 km</td>\n", "      <td>20m 46s</td>\n", "      <td>121 m</td>\n", "      <td>https://www.strava.com/activities/14699992258</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>684189e4bb465f4e88bad56a</td>\n", "      <td>14703762366</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON></td>\n", "      <td>cimahi</td>\n", "      <td>05 Jun 2025 19:10 WIB</td>\n", "      <td>Run</td>\n", "      <td>11.3 km</td>\n", "      <td>2m 20s</td>\n", "      <td>151 m</td>\n", "      <td>https://www.strava.com/activities/14703762366</td>\n", "      <td>Valid</td>\n", "      <td>161376292</td>\n", "      <td>6289685587547</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>718</th>\n", "      <td>684190e3bb465f4e88bad575</td>\n", "      <td>14704011151</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocL9vU...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Evening Walk</td>\n", "      <td>05 Jun 2025 19:37 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>5m 17s</td>\n", "      <td>60 m</td>\n", "      <td>https://www.strava.com/activities/14704011151</td>\n", "      <td>Valid</td>\n", "      <td>161216030</td>\n", "      <td>6289604482359</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>719</th>\n", "      <td>6841c9894e147b00850eb771</td>\n", "      <td>14706038064</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>05 Jun 2025 23:43 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.0 km</td>\n", "      <td>34s</td>\n", "      <td>10 m</td>\n", "      <td>https://www.strava.com/activities/14706038064</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          _id  activity_id  \\\n", "715  68415245c28c453ed838d46b  14701990198   \n", "716  684163322a714c784c3741f8  14699992258   \n", "717  684189e4bb465f4e88bad56a  14703762366   \n", "718  684190e3bb465f4e88bad575  14704011151   \n", "719  6841c9894e147b00850eb771  14706038064   \n", "\n", "                                               picture                   name  \\\n", "715  https://dgalywyr863hv.cloudfront.net/pictures/...      <PERSON><PERSON>   \n", "716  https://lh3.googleusercontent.com/a/ACg8ocLhho...         Ra<PERSON>ya  Rizki   \n", "717  https://dgalywyr863hv.cloudfront.net/pictures/...     <PERSON>   \n", "718  https://lh3.googleusercontent.com/a/ACg8ocL9vU...  <PERSON><PERSON><PERSON>   \n", "719  https://lh3.googleusercontent.com/a/ACg8ocKHvM...            hasan pohan   \n", "\n", "             title              date_time type_sport distance moving_time  \\\n", "715     Going home  05 Jun 2025 15:06 WIB        Run   2.2 km      5m 49s   \n", "716          5 Jun  05 Jun 2025 07:29 WIB        Run   7.8 km     20m 46s   \n", "717         cimahi  05 Jun 2025 19:10 WIB        Run  11.3 km      2m 20s   \n", "718   Evening Walk  05 Jun 2025 19:37 WIB       Walk   3.0 km      5m 17s   \n", "719  Berlari Malam  05 Jun 2025 23:43 WIB        Run   7.0 km         34s   \n", "\n", "    elevation                                  link_activity status  \\\n", "715       0 m  https://www.strava.com/activities/14701990198  Valid   \n", "716     121 m  https://www.strava.com/activities/14699992258  Valid   \n", "717     151 m  https://www.strava.com/activities/14703762366  Valid   \n", "718      60 m  https://www.strava.com/activities/14704011151  Valid   \n", "719      10 m  https://www.strava.com/activities/14706038064  Valid   \n", "\n", "     athlete_id    phonenumber  sport_score  \n", "715   161143528  6288970788847          3.0  \n", "716   161216121  6285860602948          3.0  \n", "717   161376292  6289685587547          3.0  \n", "718   161216030  6289604482359          1.0  \n", "719   161402876  6282161299141          3.0  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df_strava_valid.tail()"]}, {"cell_type": "code", "execution_count": 13, "id": "cda4d0a5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37796\\3011612912.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_strava_valid['date_time_wib'] = pd.to_datetime(\n"]}], "source": ["# Ha<PERSON> \"WIB\" di akhir dan konversi ke datetime\n", "df_strava_valid['date_time_wib'] = pd.to_datetime(\n", "    df_strava_valid['date_time'].str.replace('WIB', '').str.strip(),\n", "    format='%d %b %Y %H:%M',\n", "    errors='coerce'\n", ")\n"]}, {"cell_type": "code", "execution_count": 14, "id": "bb821b04", "metadata": {}, "outputs": [], "source": ["start_date = pd.to_datetime('2025-03-11')\n", "end_date = pd.to_datetime('2025-06-05')\n", "\n", "df_strava_valid = df_strava_valid[\n", "    (df_strava_valid['date_time_wib'] >= start_date) &\n", "    (df_strava_valid['date_time_wib'] <= end_date)\n", "].copy()"]}, {"cell_type": "code", "execution_count": 15, "id": "21995b3e", "metadata": {}, "outputs": [], "source": ["df_strava_valid['date_time_wib'] = df_strava_valid['date_time_wib'].dt.tz_localize('Asia/Jakarta')"]}, {"cell_type": "code", "execution_count": 16, "id": "ed9ccb93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["date_time_wib:  datetime64[ns, Asia/Jakarta]\n"]}], "source": ["print('date_time_wib: ',df_strava_valid['date_time_wib'].dtype)"]}, {"cell_type": "code", "execution_count": 17, "id": "f4b4e00a", "metadata": {}, "outputs": [], "source": ["anchor_tuesday = pd.to_datetime('2025-03-11')\n", "df_strava_valid['week_custom'] = (\n", "    (df_strava_valid['date_time_wib'].dt.tz_convert(None) - anchor_tuesday).dt.days // 7 + 1\n", ")\n", "\n", "df_strava_valid['year_week'] = (\n", "    anchor_tuesday.strftime('%Y') + '-W' + df_strava_valid['week_custom'].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": 18, "id": "d5665501", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>activity_id</th>\n", "      <th>picture</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>date_time</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>link_activity</th>\n", "      <th>status</th>\n", "      <th>athlete_id</th>\n", "      <th>phonenumber</th>\n", "      <th>sport_score</th>\n", "      <th>date_time_wib</th>\n", "      <th>week_custom</th>\n", "      <th>year_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67cfa9eb40da00d8c6402d4a</td>\n", "      <td>13848439185</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Morning Walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848439185</td>\n", "      <td>Valid</td>\n", "      <td>144975651</td>\n", "      <td>6289662315611</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 09:16:00+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67cfaa1240da00d8c6402d4c</td>\n", "      <td>13848440241</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLYkf...</td>\n", "      <td>ismi nabilah</td>\n", "      <td>Morning walk</td>\n", "      <td>11 Mar 2025 09:16 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>https://www.strava.com/activities/13848440241</td>\n", "      <td>Valid</td>\n", "      <td>161163973</td>\n", "      <td>62895329746650</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 09:16:00+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67cfb76340da00d8c6402d86</td>\n", "      <td>13848516402</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocI5S2...</td>\n", "      <td>F<PERSON>zi  <PERSON>urfadl<PERSON></td>\n", "      <td>tugas 1</td>\n", "      <td>11 Mar 2025 10:06 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>https://www.strava.com/activities/13848516402</td>\n", "      <td>Valid</td>\n", "      <td>161200570</td>\n", "      <td>6285722341788</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 10:06:00+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d01612b249b1388ac7c4a7</td>\n", "      <td>13850512258</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocK0KD...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>https://www.strava.com/activities/13850512258</td>\n", "      <td>Valid</td>\n", "      <td>161209993</td>\n", "      <td>6282232023945</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 16:34:00+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>67d026f8b249b1388ac7c545</td>\n", "      <td>13850376810</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLhho...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Jalan 3Km Selasa</td>\n", "      <td>11 Mar 2025 16:34 WIB</td>\n", "      <td>Walk</td>\n", "      <td>3.6 km</td>\n", "      <td>54m 42s</td>\n", "      <td>51 m</td>\n", "      <td>https://www.strava.com/activities/13850376810</td>\n", "      <td>Valid</td>\n", "      <td>161216121</td>\n", "      <td>6285860602948</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 16:34:00+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>710</th>\n", "      <td>68403d01b82187eff094de08</td>\n", "      <td>14693364774</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocJypl...</td>\n", "      <td>Gading Khairlambang</td>\n", "      <td>Evening Ride</td>\n", "      <td>04 Jun 2025 18:38 WIB</td>\n", "      <td>Ride</td>\n", "      <td>21.1 km</td>\n", "      <td>48m 44s</td>\n", "      <td>124 m</td>\n", "      <td>https://www.strava.com/activities/14693364774</td>\n", "      <td>Valid</td>\n", "      <td>161937055</td>\n", "      <td>6281380817301</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-04 18:38:00+07:00</td>\n", "      <td>13</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>711</th>\n", "      <td>68404cb5b82187eff094de19</td>\n", "      <td>14568999378</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bersepeda Sore</td>\n", "      <td>23 May 2025 18:04 WIB</td>\n", "      <td>Ride</td>\n", "      <td>3.0 km</td>\n", "      <td>10m 52s</td>\n", "      <td>51 m</td>\n", "      <td>https://www.strava.com/activities/14568999378</td>\n", "      <td>Valid</td>\n", "      <td>161560144</td>\n", "      <td>62881023171632</td>\n", "      <td>0.0</td>\n", "      <td>2025-05-23 18:04:00+07:00</td>\n", "      <td>11</td>\n", "      <td>2025-W11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>712</th>\n", "      <td>68405596b82187eff094de21</td>\n", "      <td>14618353560</td>\n", "      <td>https://dgalywyr863hv.cloudfront.net/pictures/...</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>28 May 2025 15:27 WIB</td>\n", "      <td>Run</td>\n", "      <td>7.7 km</td>\n", "      <td>20m 22s</td>\n", "      <td>6 m</td>\n", "      <td>https://www.strava.com/activities/14618353560</td>\n", "      <td>Valid</td>\n", "      <td>161924090</td>\n", "      <td>6285159577009</td>\n", "      <td>3.0</td>\n", "      <td>2025-05-28 15:27:00+07:00</td>\n", "      <td>12</td>\n", "      <td>2025-W12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>713</th>\n", "      <td>68410e81a6bcd4841c36d211</td>\n", "      <td>13924127499</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocLA5W...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Afternoon Ride</td>\n", "      <td>19 Mar 2025 15:40 WIB</td>\n", "      <td>Ride</td>\n", "      <td>6.8 km</td>\n", "      <td>27m 46s</td>\n", "      <td>104 m</td>\n", "      <td>https://www.strava.com/activities/13924127499</td>\n", "      <td>Valid</td>\n", "      <td>153403538</td>\n", "      <td>6289622700077</td>\n", "      <td>0.0</td>\n", "      <td>2025-03-19 15:40:00+07:00</td>\n", "      <td>2</td>\n", "      <td>2025-W2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>714</th>\n", "      <td>6841206ea6bcd4841c36d243</td>\n", "      <td>14382323270</td>\n", "      <td>https://lh3.googleusercontent.com/a/ACg8ocKHvM...</td>\n", "      <td>hasan pohan</td>\n", "      <td>Afternoon Run</td>\n", "      <td>05 May 2025 15:20 WIB</td>\n", "      <td>Run</td>\n", "      <td>3.6 km</td>\n", "      <td>15m 5s</td>\n", "      <td>13 m</td>\n", "      <td>https://www.strava.com/activities/14382323270</td>\n", "      <td>Valid</td>\n", "      <td>161402876</td>\n", "      <td>6282161299141</td>\n", "      <td>3.0</td>\n", "      <td>2025-05-05 15:20:00+07:00</td>\n", "      <td>8</td>\n", "      <td>2025-W8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>611 rows × 18 columns</p>\n", "</div>"], "text/plain": ["                          _id  activity_id  \\\n", "1    67cfa9eb40da00d8c6402d4a  13848439185   \n", "2    67cfaa1240da00d8c6402d4c  13848440241   \n", "3    67cfb76340da00d8c6402d86  13848516402   \n", "4    67d01612b249b1388ac7c4a7  13850512258   \n", "5    67d026f8b249b1388ac7c545  13850376810   \n", "..                        ...          ...   \n", "710  68403d01b82187eff094de08  14693364774   \n", "711  68404cb5b82187eff094de19  14568999378   \n", "712  68405596b82187eff094de21  14618353560   \n", "713  68410e81a6bcd4841c36d211  13924127499   \n", "714  6841206ea6bcd4841c36d243  14382323270   \n", "\n", "                                               picture  \\\n", "1    https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "2    https://lh3.googleusercontent.com/a/ACg8ocLYkf...   \n", "3    https://lh3.googleusercontent.com/a/ACg8ocI5S2...   \n", "4    https://lh3.googleusercontent.com/a/ACg8ocK0KD...   \n", "5    https://lh3.googleusercontent.com/a/ACg8ocLhho...   \n", "..                                                 ...   \n", "710  https://lh3.googleusercontent.com/a/ACg8ocJypl...   \n", "711  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "712  https://dgalywyr863hv.cloudfront.net/pictures/...   \n", "713  https://lh3.googleusercontent.com/a/ACg8ocLA5W...   \n", "714  https://lh3.googleusercontent.com/a/ACg8ocKHvM...   \n", "\n", "                        name             title              date_time  \\\n", "1    <PERSON><PERSON><PERSON>      Morning Walk  11 Mar 2025 09:16 WIB   \n", "2               is<PERSON> na<PERSON><PERSON>      Morning walk  11 Mar 2025 09:16 WIB   \n", "3           Fauzi  Nurfadlah           tugas 1  11 Mar 2025 10:06 WIB   \n", "4               Wah<PERSON> <PERSON><PERSON>  11 Mar 2025 16:34 WIB   \n", "5             Raditya  Rizki  Jalan 3Km Selasa  11 Mar 2025 16:34 WIB   \n", "..                       ...               ...                    ...   \n", "710      Gading Khairlambang      Evening Ride  04 Jun 2025 18:38 WIB   \n", "711           <PERSON><PERSON> Andhi<PERSON>rsepeda Sore  23 May 2025 18:04 WIB   \n", "712   <PERSON><PERSON><PERSON><PERSON>yang      Berlari Sore  28 May 2025 15:27 WIB   \n", "713              <PERSON><PERSON>    Afternoon Ride  19 Mar 2025 15:40 WIB   \n", "714              hasan pohan     <PERSON> Run  05 May 2025 15:20 WIB   \n", "\n", "    type_sport distance moving_time elevation  \\\n", "1         Walk   3.0 km     41m 21s      12 m   \n", "2         Walk   3.1 km      40m 2s      12 m   \n", "3         Walk   3.2 km     36m 11s       9 m   \n", "4         Walk   4.3 km   1h 3m 32s      63 m   \n", "5         Walk   3.6 km     54m 42s      51 m   \n", "..         ...      ...         ...       ...   \n", "710       Ride  21.1 km     48m 44s     124 m   \n", "711       Ride   3.0 km     10m 52s      51 m   \n", "712        Run   7.7 km     20m 22s       6 m   \n", "713       Ride   6.8 km     27m 46s     104 m   \n", "714        Run   3.6 km      15m 5s      13 m   \n", "\n", "                                     link_activity status  athlete_id  \\\n", "1    https://www.strava.com/activities/13848439185  Valid   144975651   \n", "2    https://www.strava.com/activities/13848440241  Valid   161163973   \n", "3    https://www.strava.com/activities/13848516402  Valid   161200570   \n", "4    https://www.strava.com/activities/13850512258  Valid   161209993   \n", "5    https://www.strava.com/activities/13850376810  Valid   161216121   \n", "..                                             ...    ...         ...   \n", "710  https://www.strava.com/activities/14693364774  Valid   161937055   \n", "711  https://www.strava.com/activities/14568999378  Valid   161560144   \n", "712  https://www.strava.com/activities/14618353560  Valid   161924090   \n", "713  https://www.strava.com/activities/13924127499  Valid   153403538   \n", "714  https://www.strava.com/activities/14382323270  Valid   161402876   \n", "\n", "        phonenumber  sport_score             date_time_wib  week_custom  \\\n", "1     6289662315611          1.0 2025-03-11 09:16:00+07:00            1   \n", "2    62895329746650          1.0 2025-03-11 09:16:00+07:00            1   \n", "3     6285722341788          1.0 2025-03-11 10:06:00+07:00            1   \n", "4     6282232023945          1.0 2025-03-11 16:34:00+07:00            1   \n", "5     6285860602948          1.0 2025-03-11 16:34:00+07:00            1   \n", "..              ...          ...                       ...          ...   \n", "710   6281380817301          0.0 2025-06-04 18:38:00+07:00           13   \n", "711  62881023171632          0.0 2025-05-23 18:04:00+07:00           11   \n", "712   6285159577009          3.0 2025-05-28 15:27:00+07:00           12   \n", "713   6289622700077          0.0 2025-03-19 15:40:00+07:00            2   \n", "714   6282161299141          3.0 2025-05-05 15:20:00+07:00            8   \n", "\n", "    year_week  \n", "1     2025-W1  \n", "2     2025-W1  \n", "3     2025-W1  \n", "4     2025-W1  \n", "5     2025-W1  \n", "..        ...  \n", "710  2025-W13  \n", "711  2025-W11  \n", "712  2025-W12  \n", "713   2025-W2  \n", "714   2025-W8  \n", "\n", "[611 rows x 18 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_strava_valid"]}, {"cell_type": "code", "execution_count": 19, "id": "0ce9fbe0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data berhasil disimpan ke dataset/result/strava.csv\n"]}], "source": ["# simpan data ke file CSV baru\n", "df_strava_valid.to_csv(\"dataset/result/strava.csv\", index=False)\n", "print(\"Data berhasil disimpan ke dataset/result/strava.csv\")"]}, {"cell_type": "code", "execution_count": 20, "id": "373a1184", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>type_sport</th>\n", "      <th>distance</th>\n", "      <th>moving_time</th>\n", "      <th>elevation</th>\n", "      <th>phonenumber</th>\n", "      <th>sport_score</th>\n", "      <th>date_time_wib</th>\n", "      <th>year_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Morning Walk</td>\n", "      <td>Walk</td>\n", "      <td>3.0 km</td>\n", "      <td>41m 21s</td>\n", "      <td>12 m</td>\n", "      <td>6289662315611</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 09:16:00+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Morning walk</td>\n", "      <td>Walk</td>\n", "      <td>3.1 km</td>\n", "      <td>40m 2s</td>\n", "      <td>12 m</td>\n", "      <td>62895329746650</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 09:16:00+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>tugas 1</td>\n", "      <td>Walk</td>\n", "      <td>3.2 km</td>\n", "      <td>36m 11s</td>\n", "      <td>9 m</td>\n", "      <td>6285722341788</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 10:06:00+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Walk</td>\n", "      <td>4.3 km</td>\n", "      <td>1h 3m 32s</td>\n", "      <td>63 m</td>\n", "      <td>6282232023945</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 16:34:00+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Jalan 3Km Selasa</td>\n", "      <td>Walk</td>\n", "      <td>3.6 km</td>\n", "      <td>54m 42s</td>\n", "      <td>51 m</td>\n", "      <td>6285860602948</td>\n", "      <td>1.0</td>\n", "      <td>2025-03-11 16:34:00+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>710</th>\n", "      <td>Evening Ride</td>\n", "      <td>Ride</td>\n", "      <td>21.1 km</td>\n", "      <td>48m 44s</td>\n", "      <td>124 m</td>\n", "      <td>6281380817301</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-04 18:38:00+07:00</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>711</th>\n", "      <td>Bersepeda Sore</td>\n", "      <td>Ride</td>\n", "      <td>3.0 km</td>\n", "      <td>10m 52s</td>\n", "      <td>51 m</td>\n", "      <td>62881023171632</td>\n", "      <td>0.0</td>\n", "      <td>2025-05-23 18:04:00+07:00</td>\n", "      <td>2025-W11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>712</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Run</td>\n", "      <td>7.7 km</td>\n", "      <td>20m 22s</td>\n", "      <td>6 m</td>\n", "      <td>6285159577009</td>\n", "      <td>3.0</td>\n", "      <td>2025-05-28 15:27:00+07:00</td>\n", "      <td>2025-W12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>713</th>\n", "      <td>Afternoon Ride</td>\n", "      <td>Ride</td>\n", "      <td>6.8 km</td>\n", "      <td>27m 46s</td>\n", "      <td>104 m</td>\n", "      <td>6289622700077</td>\n", "      <td>0.0</td>\n", "      <td>2025-03-19 15:40:00+07:00</td>\n", "      <td>2025-W2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>714</th>\n", "      <td>Afternoon Run</td>\n", "      <td>Run</td>\n", "      <td>3.6 km</td>\n", "      <td>15m 5s</td>\n", "      <td>13 m</td>\n", "      <td>6282161299141</td>\n", "      <td>3.0</td>\n", "      <td>2025-05-05 15:20:00+07:00</td>\n", "      <td>2025-W8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>611 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                title type_sport distance moving_time elevation  \\\n", "1        Morning Walk       Walk   3.0 km     41m 21s      12 m   \n", "2        Morning walk       Walk   3.1 km      40m 2s      12 m   \n", "3             tugas 1       Walk   3.2 km     36m 11s       9 m   \n", "4          Ngabuburit       Walk   4.3 km   1h 3m 32s      63 m   \n", "5    Jalan 3Km Selasa       Walk   3.6 km     54m 42s      51 m   \n", "..                ...        ...      ...         ...       ...   \n", "710      Evening Ride       Ride  21.1 km     48m 44s     124 m   \n", "711    Bersepeda Sore       Ride   3.0 km     10m 52s      51 m   \n", "712      Be<PERSON><PERSON>        Run   7.7 km     20m 22s       6 m   \n", "713    Afternoon Ride       Ride   6.8 km     27m 46s     104 m   \n", "714     Afternoon Run        Run   3.6 km      15m 5s      13 m   \n", "\n", "        phonenumber  sport_score             date_time_wib year_week  \n", "1     6289662315611          1.0 2025-03-11 09:16:00+07:00   2025-W1  \n", "2    62895329746650          1.0 2025-03-11 09:16:00+07:00   2025-W1  \n", "3     6285722341788          1.0 2025-03-11 10:06:00+07:00   2025-W1  \n", "4     6282232023945          1.0 2025-03-11 16:34:00+07:00   2025-W1  \n", "5     6285860602948          1.0 2025-03-11 16:34:00+07:00   2025-W1  \n", "..              ...          ...                       ...       ...  \n", "710   6281380817301          0.0 2025-06-04 18:38:00+07:00  2025-W13  \n", "711  62881023171632          0.0 2025-05-23 18:04:00+07:00  2025-W11  \n", "712   6285159577009          3.0 2025-05-28 15:27:00+07:00  2025-W12  \n", "713   6289622700077          0.0 2025-03-19 15:40:00+07:00   2025-W2  \n", "714   6282161299141          3.0 2025-05-05 15:20:00+07:00   2025-W8  \n", "\n", "[611 rows x 9 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["feature = ['_id','activity_id','picture','name','date_time','link_activity','status','athlete_id','week_custom']\n", "df_strava_valid.drop(columns=feature, inplace=True)\n", "df_strava_valid"]}, {"cell_type": "code", "execution_count": 21, "id": "e43cebc5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data berhasil disimpan ke dataset/result1/strava.csv\n"]}], "source": ["# simpan data ke file CSV baru\n", "df_strava_valid.to_csv(\"dataset/result1/strava.csv\", index=False)\n", "print(\"Data berhasil disimpan ke dataset/result1/strava.csv\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}