# 3. METHODOLOGY

## 3.1 Study Design and Participants

This study employed a quantitative cross-sectional design with longitudinal elements to analyze student behavioral patterns over a 13-week period. The observational approach was chosen to capture naturalistic behavioral data without intervention, allowing for authentic representation of student activity patterns. The research utilized a mixed-methods framework integrating quantitative metrics from activity and productivity data with qualitative text-based feature extraction from activity descriptions. The study design follows established protocols for digital health research, incorporating real-world data collection through mobile applications and wearable technology platforms, aligning with contemporary digital epidemiology methodologies that leverage ubiquitous computing for health monitoring and behavioral analysis.

The study recruited university students who were actively using fitness tracking and productivity management applications. Participants were required to demonstrate active usage of the Strava platform for cardiovascular activity tracking and regular engagement with the Pomokit application for academic productivity monitoring. Additionally, participants needed to possess sufficient digital literacy to ensure consistent data generation throughout the study period. All participants provided informed consent for data collection and analysis procedures in accordance with institutional ethical guidelines.

## 3.2 Data Collection

Cardiovascular activity data were collected through the Strava platform, a widely-used fitness tracking application with high accuracy in recording physical activities using GPS and smartphone sensors. The platform captures comprehensive activity metrics including activity type and duration, distance covered and average speed, heart rate data when available from connected devices, user-generated activity descriptions and titles, and temporal patterns of activity engagement. This comprehensive data collection approach ensures a holistic view of participants' physical activity behaviors.

Academic productivity data were collected through Pomokit, a Pomodoro technique-based application that records focused work sessions. The application systematically captures the number of completed work cycles, session duration and timing information, task descriptions and categorization data, productivity patterns and consistency metrics, and user-generated activity titles and descriptions. This data provides detailed insights into participants' academic work patterns and productivity behaviors.

Data collection was implemented using automated scraping protocols through WhatsApp bot integration for Strava data and terminal-based extraction for Pomokit data. The infrastructure utilized do.my.id services to ensure reliable and consistent data acquisition over the 13-week study period.

## 3.3 Data Preprocessing

Comprehensive data preprocessing was implemented to ensure data quality and consistency across both platforms. The preprocessing pipeline implemented systematic identification of missing data patterns, with deletion strategies employed for extensive missing values exceeding 30% of observations to maintain data integrity. Minimal missing data were addressed through imputation techniques using domain-appropriate methods, with validation of imputation accuracy conducted through cross-validation procedures to ensure reliability of the reconstructed data.

Statistical outlier identification was performed using Interquartile Range (IQR) and Z-score methods to detect anomalous values. Domain knowledge-based validation was applied to assess realistic activity parameters, leading to the removal of physiologically implausible values such as unrealistic speeds or durations. All outlier patterns were systematically documented for potential insights into unusual behavioral patterns or data collection anomalies. Comprehensive validation procedures included temporal consistency validation across platforms to ensure synchronized data collection, with logical relationship verification conducted between related variables to identify potential inconsistencies.

Multi-platform data integration required sophisticated temporal alignment procedures. Timestamp-based synchronization was implemented between Strava and Pomokit data to ensure accurate temporal correspondence, with a weekly aggregation strategy adopted to maintain consistent temporal granularity across both platforms. Time zone normalization and standardization procedures were applied to account for potential geographical variations, while specialized protocols were developed for handling irregular data collection patterns that might occur due to user behavior variations.

Comprehensive feature harmonization was essential for creating a unified analytical framework. Standardization of data formats and units was implemented across both platforms to ensure consistency. Time representations were systematically converted from various formats to standardized minute-based measurements, while text fields and categorical variables underwent normalization procedures to maintain consistency. A unified user identification system was created to ensure proper anonymization while preserving the ability to link data across platforms.

## 3.4 Feature Engineering

Weekly aggregation features were derived from daily data collected from both platforms. For cardiovascular activity data from Strava, features included cumulative weekly distance (total_distance_km), average distance per activity session (avg_distance_km), total weekly activity duration (total_time_minutes), average duration per session (avg_time_minutes), number of active days per week (activity_days), and activity frequency (activity_frequency). Pomokit productivity data were aggregated to create metrics including total Pomodoro cycles completed weekly (total_cycles), average cycles per active day (avg_cycles), number of productive days per week (work_days), cycles per work day ratio (weekly_efficiency), and regularity of work patterns (productivity_consistency).

Gamification features were developed based on established behavioral psychology principles to capture motivational aspects of user behavior. Activity points were calculated as (total_distance_km / 6) × 100, capped at 100 points, based on WHO recommended weekly physical activity guidelines. Productivity points were calculated as (total_cycles / 5) × 100, capped at 100 points, representing focused work time targets. Additional metrics included achievement rate as percentage of maximum possible points achieved (achievement_rate), gamification balance as ratio between activity and productivity points (gamification_balance), physical consistency representing regularity of physical activity (physical_consistency), work consistency representing academic work regularity (work_consistency), and overall consistency scores across both domains (overall_consistency).

Advanced text mining techniques were applied to activity titles and descriptions to extract linguistic features. Structural features included character count of activity descriptions (title_length), total words in activity titles (word_count), number of unique words used (unique_words), and lexical diversity measures across activities (title_diversity). Dictionary-based keyword extraction was implemented using predefined fatigue-related vocabularies to identify stress-related keywords (stress_count), academic workload indicators (workload_count), negative sentiment markers (negative_emotion_count), and recovery-related terms (recovery_count). Cross-platform linguistic analysis features included combined lexical diversity across platforms (total_title_diversity), ratio of productivity to activity title complexity (title_balance_ratio), and platform-specific linguistic complexity measures (pomokit_unique_words).

## 3.5 Target Variable Construction

An independent external labeling approach was implemented to prevent data leakage in the fatigue risk classification. The labeling strategy incorporated temporal pattern analysis through weekly activity pattern assessment, productivity decline identification, recovery pattern recognition, and consistency deviation measurement. Domain knowledge rules were applied to simulate expert assessment, integrating physical activity guidelines, academic productivity benchmarks, behavioral pattern recognition, and multi-dimensional risk assessment.

The fatigue risk classification employed three levels: low_risk for balanced activity and productivity patterns, medium_risk for moderate deviations from optimal patterns, and high_risk for significant imbalances indicating fatigue risk. Label validation was conducted through cross-validation with temporal patterns, consistency checks across multiple weeks, expert review of edge cases, and statistical validation of label distribution to ensure reliability and clinical relevance of the classification system.

## 3.6 Feature Selection

SHapley Additive exPlanations (SHAP) was implemented for theoretically grounded feature selection based on game theory principles. The SHAP framework provided model-agnostic explainer creation, individual prediction contribution analysis, feature importance ranking based on absolute mean SHAP values, and cross-algorithm consistency validation. SHAP analysis was conducted across four machine learning algorithms: Logistic Regression for linear coefficient-based importance, Random Forest for impurity-based feature ranking, Gradient Boosting for gain-based importance calculation, and XGBoost for advanced gradient boosting importance.

The feature selection strategy employed consensus-based selection through cross-algorithm feature importance aggregation, robust ranking through multiple algorithm perspectives, top-k feature selection based on consensus scores, and validation through performance comparison. Strict feature filtering protocols were implemented to prevent data leakage, including identification of label-creation features, exclusion of target-dependent variables, validation of feature independence, and temporal consistency checks to ensure model validity and generalizability.

## 3.7 Model Development

Four complementary machine learning algorithms were selected to provide comprehensive analysis perspectives. Logistic Regression was configured with max_iter=1000 and random_state=42, serving as a baseline linear model with high interpretability, computational efficiency, and probabilistic output. Random Forest was implemented with n_estimators=100, max_depth=10, and random_state=42, providing an ensemble method with built-in feature importance that handles mixed data types and reduces overfitting. Gradient Boosting was configured with n_estimators=100 and random_state=42 for sequential learning and complex pattern recognition, offering high predictive accuracy and capability to handle non-linear relationships. XGBoost was implemented with n_estimators=100, eval_metric='mlogloss', and random_state=42, providing optimized gradient boosting with advanced regularization, superior performance on structured data, and built-in cross-validation capabilities.

Systematic hyperparameter tuning was conducted using GridSearchCV with 5-fold cross-validation to optimize model performance. For Random Forest, parameters included n_estimators [50, 100, 200], max_depth [5, 10, 15, None], min_samples_split [2, 5, 10], and min_samples_leaf [1, 2, 4]. Gradient Boosting optimization covered n_estimators [50, 100, 200], learning_rate [0.01, 0.1, 0.2], max_depth [3, 6, 9], and subsample [0.8, 0.9, 1.0]. This comprehensive optimization approach ensured optimal model configuration for the specific characteristics of the fatigue prediction dataset.

## 3.8 Model Evaluation

Stratified K-Fold Cross-Validation (k=5) was implemented to ensure preservation of class distribution across folds, robust performance estimation, generalization capability assessment, and overfitting detection. This approach was particularly important given the imbalanced nature of the fatigue risk categories in the dataset.

Comprehensive evaluation employed multiple metrics to assess classification performance. Accuracy measured overall prediction correctness, while F1-Score (Macro) provided the harmonic mean of precision and recall averaged across all classes. Precision (Macro) assessed average precision across classes, and Recall (Macro) measured average recall across classes. Model stability assessment included train-validation gap analysis, overfitting score calculation, cross-validation consistency measurement, and performance variance analysis to ensure robust model performance.

Statistical validation procedures included significance testing of performance differences between models, confidence interval calculation for all metrics, bootstrap validation for robust estimation, and multiple comparison correction to account for testing multiple algorithms simultaneously. This comprehensive evaluation framework ensured reliable assessment of model performance and statistical significance of observed differences.

## 3.9 Ethical Considerations

### 3.9.1 Data Privacy and Security

-   Anonymization of all personal identifiers
-   Secure data storage and transmission protocols
-   Compliance with data protection regulations
-   Participant consent for data usage

### 3.9.2 Research Ethics

-   Institutional Review Board approval
-   Transparent data collection procedures
-   Voluntary participation with withdrawal rights
-   Clear communication of research purposes and outcomes

This methodology provides a comprehensive framework for developing and validating machine learning models for student fatigue risk prediction using multi-modal behavioral data, ensuring scientific rigor and practical applicability.
