# 3. METHODOLOGY

## 3.1 Research Design

This study employed a quantitative cross-sectional design with longitudinal elements to analyze student behavioral patterns over a 13-week period. The observational approach was chosen to capture naturalistic behavioral data without intervention, allowing for authentic representation of student activity patterns. The research utilized a mixed-methods framework integrating quantitative metrics (activity and productivity data) with qualitative text-based feature extraction from activity descriptions.

The study design follows established protocols for digital health research, incorporating real-world data collection through mobile applications and wearable technology platforms. This approach aligns with contemporary digital epidemiology methodologies that leverage ubiquitous computing for health monitoring and behavioral analysis.

## 3.2 Participants and Data Collection

### 3.2.1 Study Population
The study recruited university students actively using fitness tracking and productivity management applications. Participants were required to have:
- Active usage of Strava platform for cardiovascular activity tracking
- Regular engagement with Pomokit application for academic productivity monitoring
- Sufficient digital literacy for consistent data generation
- Informed consent for data collection and analysis

### 3.2.2 Data Collection Platforms

**Strava Platform Integration**
Cardiovascular activity data were collected through the Strava platform, a widely-used fitness tracking application with high accuracy in recording physical activities using GPS and smartphone sensors. The platform captures comprehensive activity metrics including:
- Activity type and duration
- Distance covered and average speed
- Heart rate data (when available)
- User-generated activity descriptions and titles
- Temporal patterns of activity engagement

**Pomokit Productivity Tracking**
Academic productivity data were collected through Pomokit, a Pomodoro technique-based application that records focused work sessions. The application captures:
- Number of completed work cycles
- Session duration and timing
- Task descriptions and categorization
- Productivity patterns and consistency metrics
- User-generated activity titles and descriptions

### 3.2.3 Data Collection Infrastructure
Data collection was implemented using automated scraping protocols through WhatsApp bot integration for Strava data and terminal-based extraction for Pomokit data. The infrastructure utilized do.my.id services to ensure reliable and consistent data acquisition over the 13-week study period.

## 3.3 Data Preprocessing and Integration

### 3.3.1 Data Cleaning and Quality Assurance
Comprehensive data preprocessing was implemented to ensure data quality and consistency:

**Missing Value Treatment**
- Systematic identification of missing data patterns
- Implementation of deletion strategies for extensive missing values (>30%)
- Imputation techniques for minimal missing data using domain-appropriate methods
- Validation of imputation accuracy through cross-validation

**Outlier Detection and Management**
- Statistical outlier identification using Interquartile Range (IQR) and Z-score methods
- Domain knowledge-based validation for realistic activity parameters
- Removal of physiologically implausible values (e.g., unrealistic speeds or durations)
- Documentation of outlier patterns for potential insights

**Data Validation and Consistency Checks**
- Temporal consistency validation across platforms
- Logical relationship verification between related variables
- Cross-platform data alignment and synchronization
- Quality metrics calculation for data reliability assessment

### 3.3.2 Data Integration and Harmonization
Multi-platform data integration was achieved through:

**Temporal Alignment**
- Timestamp-based synchronization between Strava and Pomokit data
- Weekly aggregation strategy to ensure consistent temporal granularity
- Time zone normalization and standardization
- Handling of irregular data collection patterns

**Feature Harmonization**
- Standardization of data formats and units
- Conversion of time representations (e.g., "41m 21s" to minutes)
- Normalization of text fields and categorical variables
- Creation of unified user identification system for anonymization

## 3.4 Feature Engineering

### 3.4.1 Quantitative Feature Extraction

**Cardiovascular Activity Features**
Weekly aggregation features were derived from daily Strava data:
- `total_distance_km`: Cumulative weekly distance
- `avg_distance_km`: Average distance per activity session
- `total_time_minutes`: Total weekly activity duration
- `avg_time_minutes`: Average duration per session
- `activity_days`: Number of active days per week
- `activity_frequency`: Sessions per week

**Productivity Features**
Pomokit data were aggregated to create productivity metrics:
- `total_cycles`: Total Pomodoro cycles completed weekly
- `avg_cycles`: Average cycles per active day
- `work_days`: Number of productive days per week
- `weekly_efficiency`: Cycles per work day ratio
- `productivity_consistency`: Regularity of work patterns

### 3.4.2 Gamification-Based Features

**Achievement Metrics**
Gamification features were developed based on established behavioral psychology principles:

- `activity_points`: Calculated as (total_distance_km / 6) × 100, capped at 100 points, based on WHO recommended weekly physical activity guidelines
- `productivity_points`: Calculated as (total_cycles / 5) × 100, capped at 100 points, representing focused work time targets
- `achievement_rate`: Percentage of maximum possible points achieved
- `gamification_balance`: Ratio between activity and productivity points

**Consistency Metrics**
- `physical_consistency`: (activity_days / 7) representing regularity of physical activity
- `work_consistency`: (work_days / 5) representing academic work regularity
- `overall_consistency`: Combined consistency score across both domains

### 3.4.3 Text-Based Feature Engineering

**Linguistic Feature Extraction**
Advanced text mining techniques were applied to activity titles and descriptions:

**Structural Features**
- `title_length`: Character count of activity descriptions
- `word_count`: Total words in activity titles
- `unique_words`: Number of unique words used
- `title_diversity`: Lexical diversity measures across activities

**Semantic Features**
Dictionary-based keyword extraction was implemented using predefined fatigue-related vocabularies:
- `stress_count`: Frequency of stress-related keywords
- `workload_count`: Academic workload indicators
- `negative_emotion_count`: Negative sentiment markers
- `recovery_count`: Recovery and rest-related terms

**Cross-Platform Linguistic Analysis**
- `total_title_diversity`: Combined lexical diversity across platforms
- `title_balance_ratio`: Ratio of productivity to activity title complexity
- `pomokit_unique_words`: Platform-specific linguistic complexity measures

## 3.5 Target Variable Construction

### 3.5.1 Fatigue Risk Labeling Strategy
An independent external labeling approach was implemented to prevent data leakage:

**Temporal Pattern Analysis**
- Weekly activity pattern assessment
- Productivity decline identification
- Recovery pattern recognition
- Consistency deviation measurement

**Expert Knowledge Simulation**
Domain knowledge rules were applied to simulate expert assessment:
- Integration of physical activity guidelines
- Academic productivity benchmarks
- Behavioral pattern recognition
- Multi-dimensional risk assessment

**Risk Categories**
Three-level fatigue risk classification:
- `low_risk`: Balanced activity and productivity patterns
- `medium_risk`: Moderate deviations from optimal patterns
- `high_risk`: Significant imbalances indicating fatigue risk

### 3.5.2 Label Validation
- Cross-validation with temporal patterns
- Consistency checks across multiple weeks
- Expert review of edge cases
- Statistical validation of label distribution

## 3.6 Feature Selection Using SHAP

### 3.6.1 SHAP Implementation
SHapley Additive exPlanations (SHAP) was implemented for theoretically grounded feature selection:

**SHAP Value Calculation**
- Model-agnostic explainer creation
- Individual prediction contribution analysis
- Feature importance ranking based on absolute mean SHAP values
- Cross-algorithm consistency validation

**Multi-Algorithm SHAP Analysis**
SHAP analysis was conducted across four machine learning algorithms:
- Logistic Regression: Linear coefficient-based importance
- Random Forest: Impurity-based feature ranking
- Gradient Boosting: Gain-based importance calculation
- XGBoost: Advanced gradient boosting importance

### 3.6.2 Feature Selection Strategy
**Consensus-Based Selection**
- Cross-algorithm feature importance aggregation
- Robust ranking through multiple algorithm perspectives
- Top-k feature selection based on consensus scores
- Validation through performance comparison

**Data Leakage Prevention**
Strict feature filtering protocols were implemented:
- Identification of label-creation features
- Exclusion of target-dependent variables
- Validation of feature independence
- Temporal consistency checks

## 3.7 Model Development and Training

### 3.7.1 Algorithm Selection
Four complementary machine learning algorithms were selected:

**Logistic Regression**
- Configuration: max_iter=1000, random_state=42
- Rationale: Baseline linear model with high interpretability
- Advantages: Computational efficiency, probabilistic output

**Random Forest**
- Configuration: n_estimators=100, max_depth=10, random_state=42
- Rationale: Ensemble method with built-in feature importance
- Advantages: Handles mixed data types, reduces overfitting

**Gradient Boosting**
- Configuration: n_estimators=100, random_state=42
- Rationale: Sequential learning for complex pattern recognition
- Advantages: High predictive accuracy, handles non-linear relationships

**XGBoost**
- Configuration: n_estimators=100, eval_metric='mlogloss', random_state=42
- Rationale: Optimized gradient boosting with advanced regularization
- Advantages: Superior performance on structured data, built-in cross-validation

### 3.7.2 Hyperparameter Optimization
Systematic hyperparameter tuning was conducted using GridSearchCV with 5-fold cross-validation:

**Random Forest Parameters**
- n_estimators: [50, 100, 200]
- max_depth: [5, 10, 15, None]
- min_samples_split: [2, 5, 10]
- min_samples_leaf: [1, 2, 4]

**Gradient Boosting Parameters**
- n_estimators: [50, 100, 200]
- learning_rate: [0.01, 0.1, 0.2]
- max_depth: [3, 6, 9]
- subsample: [0.8, 0.9, 1.0]

## 3.8 Model Evaluation Framework

### 3.8.1 Cross-Validation Strategy
Stratified K-Fold Cross-Validation (k=5) was implemented to ensure:
- Preservation of class distribution across folds
- Robust performance estimation
- Generalization capability assessment
- Overfitting detection

### 3.8.2 Evaluation Metrics
Comprehensive evaluation using multiple metrics:

**Classification Performance**
- Accuracy: Overall prediction correctness
- F1-Score (Macro): Harmonic mean of precision and recall
- Precision (Macro): Average precision across classes
- Recall (Macro): Average recall across classes

**Model Stability Assessment**
- Train-Validation Gap Analysis
- Overfitting Score Calculation
- Cross-Validation Consistency Measurement
- Performance Variance Analysis

### 3.8.3 Statistical Validation
- Significance testing of performance differences
- Confidence interval calculation for metrics
- Bootstrap validation for robust estimation
- Multiple comparison correction

## 3.9 Ethical Considerations

### 3.9.1 Data Privacy and Security
- Anonymization of all personal identifiers
- Secure data storage and transmission protocols
- Compliance with data protection regulations
- Participant consent for data usage

### 3.9.2 Research Ethics
- Institutional Review Board approval
- Transparent data collection procedures
- Voluntary participation with withdrawal rights
- Clear communication of research purposes and outcomes

This methodology provides a comprehensive framework for developing and validating machine learning models for student fatigue risk prediction using multi-modal behavioral data, ensuring scientific rigor and practical applicability.
