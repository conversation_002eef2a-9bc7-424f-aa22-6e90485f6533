"""
Evaluation Utilities - Clean helper functions for model evaluation
Author: Research Team
Date: 2025-06-28
Purpose: Reusable utility functions for model evaluation, metrics, and reporting
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_validate
from sklearn.metrics import (
    accuracy_score, f1_score, precision_score, recall_score,
    classification_report, confusion_matrix, roc_auc_score
)
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)


def evaluate_model_cv(model: Any,
                     X: np.ndarray,
                     y: np.ndarray,
                     cv_folds: int = 5,
                     random_state: int = 42,
                     scoring: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Evaluate model using cross-validation.
    
    Args:
        model: Sklearn-compatible model or pipeline
        X: Feature matrix
        y: Target vector
        cv_folds: Number of CV folds
        random_state: Random seed
        scoring: List of scoring metrics
        
    Returns:
        Dictionary with CV results
    """
    if scoring is None:
        scoring = ['accuracy', 'f1_macro', 'precision_macro', 'recall_macro']
    
    # Setup cross-validation
    cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
    
    # Perform cross-validation
    cv_results = cross_validate(
        model, X, y,
        cv=cv,
        scoring=scoring,
        return_train_score=True,
        n_jobs=-1
    )
    
    # Compile results
    results = {
        'cv_folds': cv_folds,
        'test_scores': {},
        'train_scores': {},
        'overfitting_scores': {}
    }
    
    for metric in scoring:
        test_key = f'test_{metric}'
        train_key = f'train_{metric}'
        
        if test_key in cv_results:
            test_scores = cv_results[test_key]
            train_scores = cv_results[train_key]
            
            results['test_scores'][metric] = {
                'mean': test_scores.mean(),
                'std': test_scores.std(),
                'scores': test_scores.tolist()
            }
            
            results['train_scores'][metric] = {
                'mean': train_scores.mean(),
                'std': train_scores.std(),
                'scores': train_scores.tolist()
            }
            
            # Calculate overfitting (train - test)
            overfitting = train_scores.mean() - test_scores.mean()
            results['overfitting_scores'][metric] = overfitting
    
    logger.info(f"CV evaluation completed: {cv_folds} folds")
    
    return results


def evaluate_model_holdout(model: Any,
                          X_train: np.ndarray,
                          X_test: np.ndarray,
                          y_train: np.ndarray,
                          y_test: np.ndarray) -> Dict[str, Any]:
    """
    Evaluate model on holdout test set.
    
    Args:
        model: Sklearn-compatible model
        X_train: Training features
        X_test: Test features
        y_train: Training targets
        y_test: Test targets
        
    Returns:
        Dictionary with evaluation results
    """
    # Train model
    model.fit(X_train, y_train)
    
    # Make predictions
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)
    
    # Calculate metrics
    results = {
        'train_metrics': _calculate_metrics(y_train, y_train_pred),
        'test_metrics': _calculate_metrics(y_test, y_test_pred),
        'overfitting': {}
    }
    
    # Calculate overfitting scores
    for metric in results['train_metrics']:
        if metric in results['test_metrics']:
            overfitting = results['train_metrics'][metric] - results['test_metrics'][metric]
            results['overfitting'][metric] = overfitting
    
    logger.info("Holdout evaluation completed")
    
    return results


def _calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """Calculate standard classification metrics."""
    return {
        'accuracy': accuracy_score(y_true, y_pred),
        'f1_macro': f1_score(y_true, y_pred, average='macro'),
        'precision_macro': precision_score(y_true, y_pred, average='macro'),
        'recall_macro': recall_score(y_true, y_pred, average='macro')
    }


def compare_models(model_results: Dict[str, Dict]) -> pd.DataFrame:
    """
    Compare multiple model evaluation results.
    
    Args:
        model_results: Dictionary with model names as keys and evaluation results as values
        
    Returns:
        DataFrame with comparison results
    """
    comparison_data = []
    
    for model_name, results in model_results.items():
        if 'test_scores' in results:
            # Cross-validation results
            row = {'model_name': model_name}
            
            for metric, scores in results['test_scores'].items():
                row[f'{metric}_mean'] = scores['mean']
                row[f'{metric}_std'] = scores['std']
            
            for metric, overfitting in results['overfitting_scores'].items():
                row[f'{metric}_overfitting'] = overfitting
                
        elif 'test_metrics' in results:
            # Holdout results
            row = {'model_name': model_name}
            
            for metric, value in results['test_metrics'].items():
                row[f'{metric}_mean'] = value
                row[f'{metric}_std'] = 0.0  # No std for single evaluation
            
            for metric, overfitting in results['overfitting'].items():
                row[f'{metric}_overfitting'] = overfitting
        
        comparison_data.append(row)
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # Sort by accuracy (or first metric)
    if 'accuracy_mean' in comparison_df.columns:
        comparison_df = comparison_df.sort_values('accuracy_mean', ascending=False)
    
    # Add ranking
    comparison_df['rank'] = range(1, len(comparison_df) + 1)
    
    return comparison_df


def create_confusion_matrix_plot(y_true: np.ndarray,
                                y_pred: np.ndarray,
                                class_names: Optional[List[str]] = None,
                                title: str = "Confusion Matrix",
                                save_path: Optional[str] = None) -> plt.Figure:
    """
    Create confusion matrix visualization.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        class_names: Names of classes
        title: Plot title
        save_path: Path to save plot
        
    Returns:
        Matplotlib figure
    """
    # Calculate confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    
    # Create plot
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # Plot heatmap
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names, ax=ax)
    
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xlabel('Predicted Label', fontsize=12)
    ax.set_ylabel('True Label', fontsize=12)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Confusion matrix saved to {save_path}")
    
    return fig


def create_performance_comparison_plot(comparison_df: pd.DataFrame,
                                     metric: str = 'accuracy',
                                     title: Optional[str] = None,
                                     save_path: Optional[str] = None) -> plt.Figure:
    """
    Create performance comparison bar plot.
    
    Args:
        comparison_df: DataFrame from compare_models()
        metric: Metric to plot
        title: Plot title
        save_path: Path to save plot
        
    Returns:
        Matplotlib figure
    """
    if title is None:
        title = f"Model Performance Comparison - {metric.title()}"
    
    # Prepare data
    mean_col = f'{metric}_mean'
    std_col = f'{metric}_std'
    
    if mean_col not in comparison_df.columns:
        raise ValueError(f"Metric '{metric}' not found in comparison data")
    
    # Create plot
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Bar plot with error bars
    x_pos = range(len(comparison_df))
    means = comparison_df[mean_col]
    stds = comparison_df[std_col] if std_col in comparison_df.columns else None
    
    bars = ax.bar(x_pos, means, yerr=stds, capsize=5, alpha=0.7,
                  color=['#2E8B57' if i == 0 else '#4682B4' for i in range(len(comparison_df))])
    
    # Customize plot
    ax.set_xlabel('Models', fontsize=12)
    ax.set_ylabel(f'{metric.title()} Score', fontsize=12)
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xticks(x_pos)
    ax.set_xticklabels(comparison_df['model_name'], rotation=45, ha='right')
    ax.grid(axis='y', alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, mean_val) in enumerate(zip(bars, means)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{mean_val:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Performance comparison plot saved to {save_path}")
    
    return fig


def generate_classification_report_dict(y_true: np.ndarray,
                                       y_pred: np.ndarray,
                                       class_names: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Generate detailed classification report.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        class_names: Names of classes
        
    Returns:
        Dictionary with classification metrics
    """
    # Get classification report as dict
    report = classification_report(y_true, y_pred, output_dict=True,
                                 target_names=class_names)
    
    # Add confusion matrix
    report['confusion_matrix'] = confusion_matrix(y_true, y_pred).tolist()
    
    # Add overall metrics
    report['overall'] = {
        'accuracy': accuracy_score(y_true, y_pred),
        'total_samples': len(y_true),
        'correct_predictions': (y_true == y_pred).sum(),
        'incorrect_predictions': (y_true != y_pred).sum()
    }
    
    return report


def calculate_statistical_significance(results1: Dict[str, Any],
                                     results2: Dict[str, Any],
                                     metric: str = 'accuracy') -> Dict[str, Any]:
    """
    Calculate statistical significance between two model results.
    
    Args:
        results1: First model CV results
        results2: Second model CV results
        metric: Metric to compare
        
    Returns:
        Dictionary with statistical test results
    """
    from scipy import stats
    
    # Extract scores
    scores1 = results1['test_scores'][metric]['scores']
    scores2 = results2['test_scores'][metric]['scores']
    
    # Perform paired t-test
    t_stat, p_value = stats.ttest_rel(scores1, scores2)
    
    # Calculate effect size (Cohen's d)
    pooled_std = np.sqrt((np.var(scores1) + np.var(scores2)) / 2)
    cohens_d = (np.mean(scores1) - np.mean(scores2)) / pooled_std
    
    # Interpret results
    significance_level = 0.05
    is_significant = p_value < significance_level
    
    # Effect size interpretation
    if abs(cohens_d) < 0.2:
        effect_size = "Small"
    elif abs(cohens_d) < 0.5:
        effect_size = "Medium"
    else:
        effect_size = "Large"
    
    return {
        'metric': metric,
        't_statistic': t_stat,
        'p_value': p_value,
        'is_significant': is_significant,
        'cohens_d': cohens_d,
        'effect_size': effect_size,
        'mean_difference': np.mean(scores1) - np.mean(scores2),
        'confidence_interval_95': stats.t.interval(
            0.95, len(scores1)-1,
            loc=np.mean(scores1) - np.mean(scores2),
            scale=stats.sem(np.array(scores1) - np.array(scores2))
        )
    }


def save_evaluation_results(results: Dict[str, Any],
                           output_path: str,
                           include_timestamp: bool = True) -> str:
    """
    Save evaluation results to file.
    
    Args:
        results: Evaluation results dictionary
        output_path: Output file path
        include_timestamp: Whether to include timestamp
        
    Returns:
        Actual file path where results were saved
    """
    import json
    from datetime import datetime
    
    output_path = Path(output_path)
    
    if include_timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        stem = output_path.stem
        suffix = output_path.suffix
        output_path = output_path.parent / f"{stem}_{timestamp}{suffix}"
    
    # Create directory if needed
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save results
    if output_path.suffix.lower() == '.json':
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
    elif output_path.suffix.lower() == '.csv':
        # Convert to DataFrame if possible
        if isinstance(results, dict) and 'test_scores' in results:
            df_data = []
            for metric, scores in results['test_scores'].items():
                df_data.append({
                    'metric': metric,
                    'mean': scores['mean'],
                    'std': scores['std']
                })
            pd.DataFrame(df_data).to_csv(output_path, index=False)
        else:
            # Fallback to JSON for complex structures
            output_path = output_path.with_suffix('.json')
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
    
    logger.info(f"Evaluation results saved to {output_path}")
    
    return str(output_path)
