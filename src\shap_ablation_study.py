"""
SHAP Ablation Study for Fatigue Prediction
Comprehensive SHAP-based feature importance analysis using <PERSON><PERSON><PERSON><PERSON> values

SHAP ADVANTAGES OVER RFE:
1. Individual prediction explanations
2. Feature interaction detection
3. Global and local interpretability
4. Theoretically grounded (game theory)
5. Model-agnostic explanations
6. Positive/negative contribution analysis

ANALYSIS COMPONENTS:
- SHAP TreeExplainer for tree-based models
- SHAP LinearExplainer for linear models
- Global feature importance ranking
- Local prediction explanations
- Feature interaction analysis
- Waterfall plots for individual predictions
- Summary plots for global insights
"""

import pandas as pd
import numpy as np
import logging
import shap
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_validate, train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, f1_score, classification_report

# Import model saving utility
try:
    from .utils.data_utils import save_model_artifacts
except ImportError:
    from utils.data_utils import save_model_artifacts

# Configure logging
logger = logging.getLogger(__name__)

try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    logger.warning("XGBoost not available. Install with: pip install xgboost")

class SHAPAblationStudy:
    """
    Comprehensive SHAP-based ablation study for fatigue prediction
    """
    
    def __init__(self, data_path: str, target_column: str, random_state: int = 42):
        """
        Initialize SHAP ablation study
        
        Args:
            data_path: Path to dataset
            target_column: Name of target column
            random_state: Random state for reproducibility
        """
        self.data_path = data_path
        self.target_column = target_column
        self.random_state = random_state
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.label_encoder = LabelEncoder()
        
        # SHAP explainers for different model types
        self.explainers = {}
        self.shap_values = {}
        self.models = {}
        
        # Algorithm configurations
        self.algorithms = {
            'logistic_regression': {
                'name': 'Logistic Regression',
                'model': LogisticRegression(random_state=random_state, max_iter=1000),
                'explainer_type': 'linear'
            },
            'random_forest': {
                'name': 'Random Forest',
                'model': RandomForestClassifier(random_state=random_state, n_estimators=100),
                'explainer_type': 'tree'
            },
            'gradient_boosting': {
                'name': 'Gradient Boosting',
                'model': GradientBoostingClassifier(random_state=random_state, n_estimators=100),
                'explainer_type': 'tree'
            }
        }

        # Add XGBoost if available
        if XGBOOST_AVAILABLE:
            self.algorithms['xgboost'] = {
                'name': 'XGBoost',
                'model': XGBClassifier(random_state=random_state, n_estimators=100, eval_metric='mlogloss'),
                'explainer_type': 'tree'
            }
        
        # Set random seeds
        np.random.seed(random_state)
        
    def load_data(self):
        """Load and prepare data for SHAP analysis"""
        logger.info(f"Loading data from {self.data_path}")
        
        self.data = pd.read_csv(self.data_path)
        logger.info(f"Data loaded: {self.data.shape}")
        
        # Prepare features and target
        self.X = self.data.drop(columns=[self.target_column])
        self.y = self.data[self.target_column]
        
        # Remove non-numeric columns for SHAP analysis
        numeric_columns = self.X.select_dtypes(include=[np.number]).columns
        self.X = self.X[numeric_columns]
        self.feature_names = list(self.X.columns)
        
        logger.info(f"Features: {self.feature_names}")
        logger.info(f"Target distribution: {self.y.value_counts().to_dict()}")
        
        # Encode target
        self.y_encoded = self.label_encoder.fit_transform(self.y)
        
    def train_models(self):
        """Train models for SHAP analysis with cross-validation"""
        logger.info("Training models for SHAP analysis with cross-validation...")

        # Split data for training
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y_encoded, test_size=0.2,
            random_state=self.random_state, stratify=self.y_encoded
        )

        self.X_train, self.X_test = X_train, X_test
        self.y_train, self.y_test = y_train, y_test

        # Cross-validation setup
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)

        # Train each model
        for algo_key, config in self.algorithms.items():
            logger.info(f"Training {config['name']} with cross-validation...")

            model = config['model']

            # For linear models, use pipeline with scaling
            if algo_key == 'logistic_regression':
                pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('model', model)
                ])
            else:
                pipeline = model

            # Perform cross-validation
            cv_scores = cross_validate(
                pipeline, X_train, y_train, cv=cv,
                scoring=['accuracy', 'f1_weighted'],
                return_train_score=True
            )

            # Train final model on full training set
            pipeline.fit(X_train, y_train)

            # Evaluate on test set
            y_pred = pipeline.predict(X_test)
            test_accuracy = accuracy_score(y_test, y_pred)
            test_f1 = f1_score(y_test, y_pred, average='weighted')

            logger.info(f"{config['name']} - CV Accuracy: {cv_scores['test_accuracy'].mean():.4f} (±{cv_scores['test_accuracy'].std():.4f})")
            logger.info(f"{config['name']} - Test Accuracy: {test_accuracy:.4f}, Test F1: {test_f1:.4f}")

            self.models[algo_key] = {
                'pipeline': pipeline,
                'model': model,
                'accuracy': test_accuracy,
                'f1_score': test_f1,
                'cv_scores': cv_scores,
                'cv_accuracy_mean': cv_scores['test_accuracy'].mean(),
                'cv_accuracy_std': cv_scores['test_accuracy'].std(),
                'cv_f1_mean': cv_scores['test_f1_weighted'].mean(),
                'cv_f1_std': cv_scores['test_f1_weighted'].std(),
                'config': config
            }
    
    def create_shap_explainers(self):
        """Create SHAP explainers for each model"""
        logger.info("Creating SHAP explainers...")

        for algo_key, model_info in self.models.items():
            config = model_info['config']
            pipeline = model_info['pipeline']

            logger.info(f"Creating SHAP explainer for {config['name']}...")

            try:
                # Use KernelExplainer for all models (more robust but slower)
                # Sample background data for efficiency
                background_data = self.X_train.iloc[:50]

                def model_predict(X):
                    """Wrapper function for model prediction"""
                    if hasattr(pipeline, 'predict_proba'):
                        return pipeline.predict_proba(X)
                    else:
                        return pipeline.predict(X)

                explainer = shap.KernelExplainer(model_predict, background_data)
                self.explainers[algo_key] = explainer
                logger.info(f"✅ SHAP KernelExplainer created for {config['name']}")

            except Exception as e:
                logger.error(f"❌ Failed to create SHAP explainer for {config['name']}: {str(e)}")
                # Skip this model
                continue
    
    def calculate_shap_values(self):
        """Calculate SHAP values for each model"""
        logger.info("Calculating SHAP values...")

        # Use a small subset for SHAP calculation (KernelExplainer is slow)
        X_shap = self.X_test.iloc[:20] if len(self.X_test) > 20 else self.X_test

        for algo_key, explainer in self.explainers.items():
            config = self.models[algo_key]['config']
            logger.info(f"Calculating SHAP values for {config['name']}...")

            try:
                # KernelExplainer works with original data
                shap_values = explainer.shap_values(X_shap)

                # Validate SHAP values
                if shap_values is None:
                    logger.warning(f"SHAP values are None for {config['name']}")
                    continue

                # Handle different SHAP value formats
                if isinstance(shap_values, list):
                    if len(shap_values) == 0:
                        logger.warning(f"Empty SHAP values list for {config['name']}")
                        continue
                    # Check if any of the arrays in the list are empty
                    if any(sv.size == 0 for sv in shap_values):
                        logger.warning(f"Empty SHAP value arrays for {config['name']}")
                        continue
                else:
                    if shap_values.size == 0:
                        logger.warning(f"Empty SHAP values array for {config['name']}")
                        continue

                self.shap_values[algo_key] = {
                    'values': shap_values,
                    'data': X_shap,
                    'feature_names': self.feature_names
                }

                logger.info(f"✅ SHAP values calculated for {config['name']}")

            except Exception as e:
                logger.error(f"❌ Failed to calculate SHAP values for {config['name']}: {str(e)}")
                continue
    
    def analyze_global_importance(self) -> Dict:
        """Analyze global feature importance using SHAP values"""
        logger.info("Analyzing global feature importance...")
        
        global_importance = {}
        
        for algo_key, shap_data in self.shap_values.items():
            config = self.models[algo_key]['config']
            shap_vals = shap_data['values']
            
            # Handle multi-class SHAP values
            if isinstance(shap_vals, list):
                # For multi-class, take mean absolute SHAP values across classes
                if len(shap_vals) > 0:
                    mean_shap = np.mean([np.abs(sv) for sv in shap_vals], axis=0)
                else:
                    logger.warning(f"Empty SHAP values list for {config['name']}")
                    continue
            else:
                mean_shap = np.abs(shap_vals)

            # Ensure mean_shap is not empty
            if mean_shap.size == 0:
                logger.warning(f"Empty SHAP values for {config['name']}")
                continue

            # Calculate global importance (mean absolute SHAP value)
            global_imp = np.mean(mean_shap, axis=0)
            
            # Create feature importance ranking
            feature_importance = []
            for i, feature in enumerate(self.feature_names):
                # Ensure shap_importance is a scalar value
                importance_val = global_imp[i]

                # Handle different numpy array shapes and types
                if isinstance(importance_val, np.ndarray):
                    if importance_val.size == 1:
                        importance_val = float(importance_val.item())  # Single element array
                    elif importance_val.size > 1:
                        importance_val = float(np.mean(importance_val))  # Multi-element array, take mean
                    else:
                        importance_val = 0.0  # Empty array
                elif hasattr(importance_val, 'item'):
                    try:
                        importance_val = float(importance_val.item())  # Numpy scalar
                    except ValueError:
                        importance_val = float(np.mean(importance_val))  # If item() fails, take mean
                else:
                    importance_val = float(importance_val)  # Regular Python number

                feature_importance.append({
                    'feature': feature,
                    'shap_importance': importance_val,
                    'rank': 0  # Will be set after sorting
                })

            # Sort by importance (now safe since all values are scalars)
            feature_importance.sort(key=lambda x: x['shap_importance'], reverse=True)
            
            # Update ranks
            for i, item in enumerate(feature_importance):
                item['rank'] = i + 1
            
            global_importance[algo_key] = {
                'algorithm': config['name'],
                'feature_importance': feature_importance,
                'total_features': len(self.feature_names)
            }
            
            logger.info(f"Global importance calculated for {config['name']}")
        
        return global_importance
    
    def run_complete_shap_study(self) -> Dict:
        """Run complete SHAP ablation study"""
        logger.info("Starting complete SHAP ablation study")
        
        # Load data
        self.load_data()
        
        # Train models
        self.train_models()
        
        # Create SHAP explainers
        self.create_shap_explainers()
        
        # Calculate SHAP values
        self.calculate_shap_values()
        
        # Analyze global importance
        global_importance = self.analyze_global_importance()
        
        # Compile results
        results = {
            'dataset_info': {
                'path': self.data_path,
                'shape': self.data.shape,
                'target_column': self.target_column,
                'features': self.feature_names,
                'target_distribution': self.y.value_counts().to_dict()
            },
            'model_performance': {
                algo_key: {
                    'accuracy': info['accuracy'],
                    'f1_score': info['f1_score'],
                    'algorithm_name': info['config']['name'],
                    'cv_accuracy_mean': info.get('cv_accuracy_mean', info['accuracy']),
                    'cv_accuracy_std': info.get('cv_accuracy_std', 0.0),
                    'cv_f1_mean': info.get('cv_f1_mean', info['f1_score']),
                    'cv_f1_std': info.get('cv_f1_std', 0.0)
                }
                for algo_key, info in self.models.items()
            },
            'global_importance': global_importance,
            'shap_analysis_completed': True,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info("SHAP ablation study completed")
        return results

    def save_models(self, results: Dict, output_dir: str = "results/clean_production_model") -> Dict[str, str]:
        """
        Save all trained models and the best performing model

        Args:
            results: Results from SHAP ablation study
            output_dir: Directory to save models

        Returns:
            Dictionary with paths of saved model files
        """
        logger.info("Saving trained models...")

        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        saved_model_paths = {}

        # Save all models
        for algo_key, model_info in self.models.items():
            logger.info(f"Saving {model_info['config']['name']} model...")

            # Prepare model info for saving
            model_save_info = {
                'pipeline': model_info['pipeline'],
                'model': model_info['model'],
                'label_encoder': self.label_encoder,
                'feature_names': self.feature_names,
                'accuracy': model_info['accuracy'],
                'f1_score': model_info['f1_score'],
                'cv_accuracy_mean': model_info.get('cv_accuracy_mean', model_info['accuracy']),
                'cv_accuracy_std': model_info.get('cv_accuracy_std', 0.0),
                'cv_f1_mean': model_info.get('cv_f1_mean', model_info['f1_score']),
                'cv_f1_std': model_info.get('cv_f1_std', 0.0),
                'algorithm_name': model_info['config']['name'],
                'metadata': {
                    'algorithm_key': algo_key,
                    'random_state': self.random_state,
                    'dataset_path': self.data_path,
                    'target_column': self.target_column,
                    'train_samples': len(self.X_train),
                    'test_samples': len(self.X_test),
                    'feature_count': len(self.feature_names)
                }
            }

            # Save model artifacts
            model_files = save_model_artifacts(
                model_save_info,
                output_dir,
                model_name=f"{algo_key}_model",
                include_timestamp=True
            )

            saved_model_paths[algo_key] = model_files
            logger.info(f"✅ {model_info['config']['name']} model saved successfully")

        # Find and save the best performing model separately
        if 'model_performance' in results:
            performance = results['model_performance']
            best_algo = max(performance.items(), key=lambda x: x[1]['accuracy'])
            best_algo_key, best_perf = best_algo

            logger.info(f"Saving best model: {best_perf['algorithm_name']} (Accuracy: {best_perf['accuracy']:.4f})")

            best_model_info = self.models[best_algo_key].copy()
            best_model_save_info = {
                'pipeline': best_model_info['pipeline'],
                'model': best_model_info['model'],
                'label_encoder': self.label_encoder,
                'feature_names': self.feature_names,
                'accuracy': best_model_info['accuracy'],
                'f1_score': best_model_info['f1_score'],
                'cv_accuracy_mean': best_model_info.get('cv_accuracy_mean', best_model_info['accuracy']),
                'cv_accuracy_std': best_model_info.get('cv_accuracy_std', 0.0),
                'cv_f1_mean': best_model_info.get('cv_f1_mean', best_model_info['f1_score']),
                'cv_f1_std': best_model_info.get('cv_f1_std', 0.0),
                'algorithm_name': best_model_info['config']['name'],
                'metadata': {
                    'is_best_model': True,
                    'algorithm_key': best_algo_key,
                    'random_state': self.random_state,
                    'dataset_path': self.data_path,
                    'target_column': self.target_column,
                    'train_samples': len(self.X_train),
                    'test_samples': len(self.X_test),
                    'feature_count': len(self.feature_names),
                    'shap_top_features': [item['feature'] for item in results['global_importance'][best_algo_key]['feature_importance'][:5]]
                }
            }

            # Save best model with special naming
            best_model_files = save_model_artifacts(
                best_model_save_info,
                output_dir,
                model_name="best_model",
                include_timestamp=True
            )

            saved_model_paths['best_model'] = best_model_files
            logger.info(f"✅ Best model ({best_perf['algorithm_name']}) saved successfully")

        logger.info(f"🎉 All models saved to {output_dir}")
        return saved_model_paths

    def save_results(self, results: Dict, prefix: str = "") -> Tuple[str, str]:
        """Save SHAP analysis results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = Path("results/shap_ablation_study")
        results_dir.mkdir(parents=True, exist_ok=True)

        # Create feature selection directories
        feature_selection_dir = Path("results/feature_selection")
        feature_selection_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories for different types of feature selection results
        (feature_selection_dir / "shap_features").mkdir(parents=True, exist_ok=True)
        (feature_selection_dir / "selected_features").mkdir(parents=True, exist_ok=True)
        (feature_selection_dir / "feature_rankings").mkdir(parents=True, exist_ok=True)
        (feature_selection_dir / "feature_importance").mkdir(parents=True, exist_ok=True)

        # Add prefix if provided
        file_prefix = f"{prefix}_" if prefix else ""

        # Save main results CSV
        results_file = results_dir / f"{file_prefix}shap_results_{timestamp}.csv"

        # Compile all feature importance data
        all_importance_data = []
        for algo_key, importance_data in results['global_importance'].items():
            for item in importance_data['feature_importance']:
                all_importance_data.append({
                    'algorithm': importance_data['algorithm'],
                    'algorithm_key': algo_key,
                    'feature': item['feature'],
                    'shap_importance': item['shap_importance'],
                    'rank': item['rank']
                })

        importance_df = pd.DataFrame(all_importance_data)
        importance_df.to_csv(results_file, index=False)

        # Save detailed report
        report_file = results_dir / f"{file_prefix}shap_report_{timestamp}.txt"
        report_content = self._generate_shap_report(results)

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # Save feature importance by algorithm
        for algo_key, importance_data in results['global_importance'].items():
            algo_file = results_dir / f"{file_prefix}shap_{algo_key}_{timestamp}.csv"
            algo_df = pd.DataFrame(importance_data['feature_importance'])
            algo_df.to_csv(algo_file, index=False)

        # Generate Python code for optimal features
        code_file = results_dir / f"{file_prefix}shap_features_{timestamp}.py"
        code_content = self._generate_shap_features_code(results, timestamp)

        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(code_content)

        # Save feature selection results to dedicated folders
        self._save_feature_selection_results(results, timestamp, file_prefix)

        # Save trained models
        logger.info("Saving trained models...")
        model_paths = self.save_models(results)

        logger.info(f"SHAP results saved:")
        logger.info(f"  - Main results: {results_file}")
        logger.info(f"  - Report: {report_file}")
        logger.info(f"  - Features code: {code_file}")
        logger.info(f"  - Feature Selection: results/feature_selection/")
        logger.info(f"  - Models: results/clean_production_model/")

        return str(results_file), str(report_file)

    def _save_feature_selection_results(self, results: Dict, timestamp: str, file_prefix: str = ""):
        """Save feature selection results to dedicated folders"""
        try:
            feature_selection_dir = Path("results/feature_selection")

            # 1. Save SHAP features (top features from each algorithm)
            shap_features_dir = feature_selection_dir / "shap_features"
            for algo_key, importance_data in results['global_importance'].items():
                # Save top 10, 15, 20 features for each algorithm
                for top_n in [10, 15, 20]:
                    top_features = importance_data['feature_importance'][:top_n]
                    features_list = [item['feature'] for item in top_features]

                    # Save as text file
                    features_file = shap_features_dir / f"{file_prefix}top_{top_n}_{algo_key}_{timestamp}.txt"
                    with open(features_file, 'w') as f:
                        f.write(f"# Top {top_n} SHAP Features - {importance_data['algorithm']}\n")
                        f.write(f"# Generated: {timestamp}\n")
                        f.write(f"# Total features analyzed: {len(importance_data['feature_importance'])}\n\n")
                        for i, feature in enumerate(features_list, 1):
                            importance = next(item['shap_importance'] for item in top_features if item['feature'] == feature)
                            f.write(f"{i:2d}. {feature:<30} (SHAP: {importance:.6f})\n")

                    # Save as CSV
                    csv_file = shap_features_dir / f"{file_prefix}top_{top_n}_{algo_key}_{timestamp}.csv"
                    top_df = pd.DataFrame(top_features)
                    top_df.to_csv(csv_file, index=False)

            # 2. Save selected features (consensus features across algorithms)
            selected_features_dir = feature_selection_dir / "selected_features"

            # Find consensus features (appear in top 10 of multiple algorithms)
            consensus_features = {}
            for _, importance_data in results['global_importance'].items():
                top10 = [item['feature'] for item in importance_data['feature_importance'][:10]]
                for feature in top10:
                    consensus_features[feature] = consensus_features.get(feature, 0) + 1

            # Save consensus features
            consensus_file = selected_features_dir / f"{file_prefix}consensus_features_{timestamp}.txt"
            with open(consensus_file, 'w') as f:
                f.write(f"# Consensus SHAP Features\n")
                f.write(f"# Generated: {timestamp}\n")
                f.write(f"# Features that appear in top 10 of multiple algorithms\n\n")

                sorted_consensus = sorted(consensus_features.items(), key=lambda x: x[1], reverse=True)
                for feature, count in sorted_consensus:
                    if count > 1:  # Appears in multiple algorithms
                        f.write(f"{feature:<30} (appears in {count} algorithms)\n")

            # 3. Save feature rankings
            rankings_dir = feature_selection_dir / "feature_rankings"

            # Create comprehensive ranking comparison
            all_features = set()
            for _, importance_data in results['global_importance'].items():
                for item in importance_data['feature_importance']:
                    all_features.add(item['feature'])

            ranking_data = []
            for feature in sorted(all_features):
                row = {'feature': feature}
                for algo_key, importance_data in results['global_importance'].items():
                    # Find feature rank in this algorithm
                    rank = None
                    importance = 0.0
                    for item in importance_data['feature_importance']:
                        if item['feature'] == feature:
                            rank = item['rank']
                            importance = item['shap_importance']
                            break

                    row[f'{algo_key}_rank'] = rank if rank else len(importance_data['feature_importance']) + 1
                    row[f'{algo_key}_importance'] = importance

                # Calculate average rank
                ranks = [row[col] for col in row.keys() if col.endswith('_rank')]
                row['avg_rank'] = sum(ranks) / len(ranks) if ranks else float('inf')
                ranking_data.append(row)

            # Sort by average rank
            ranking_data.sort(key=lambda x: x['avg_rank'])

            # Save ranking comparison
            ranking_file = rankings_dir / f"{file_prefix}feature_rankings_{timestamp}.csv"
            ranking_df = pd.DataFrame(ranking_data)
            ranking_df.to_csv(ranking_file, index=False)

            # 4. Save feature importance matrices
            importance_dir = feature_selection_dir / "feature_importance"

            # Create importance matrix (features x algorithms)
            importance_matrix = []
            for feature in sorted(all_features):
                row = {'feature': feature}
                for algo_key, importance_data in results['global_importance'].items():
                    importance = 0.0
                    for item in importance_data['feature_importance']:
                        if item['feature'] == feature:
                            importance = item['shap_importance']
                            break
                    row[f'{algo_key}_shap'] = importance
                importance_matrix.append(row)

            # Save importance matrix
            importance_file = importance_dir / f"{file_prefix}shap_importance_matrix_{timestamp}.csv"
            importance_df = pd.DataFrame(importance_matrix)
            importance_df.to_csv(importance_file, index=False)

            logger.info(f"✅ Feature selection results saved to results/feature_selection/")

        except Exception as e:
            logger.error(f"❌ Failed to save feature selection results: {str(e)}")

    def _generate_shap_report(self, results: Dict) -> str:
        """Generate comprehensive SHAP analysis report"""
        report = []
        report.append("=" * 80)
        report.append("🔍 SHAP ABLATION STUDY REPORT")
        report.append("=" * 80)

        # Dataset information
        dataset_info = results['dataset_info']
        report.append(f"\n📋 DATASET INFORMATION:")
        report.append(f"   • Dataset Path: {dataset_info['path']}")
        report.append(f"   • Target Column: {dataset_info['target_column']}")
        report.append(f"   • Total Features: {len(dataset_info['features'])}")
        report.append(f"   • Total Samples: {dataset_info['shape'][0]}")
        report.append(f"   • Target Distribution: {dataset_info['target_distribution']}")

        # Model performance
        report.append(f"\n🤖 MODEL PERFORMANCE:")
        performance = results['model_performance']
        for _, perf in performance.items():
            report.append(f"   • {perf['algorithm_name']}:")
            report.append(f"     - Test Accuracy: {perf['accuracy']:.4f}")
            report.append(f"     - Test F1-Score: {perf['f1_score']:.4f}")
            if 'cv_accuracy_mean' in perf:
                report.append(f"     - CV Accuracy: {perf['cv_accuracy_mean']:.4f} (±{perf['cv_accuracy_std']:.4f})")
                report.append(f"     - CV F1-Score: {perf['cv_f1_mean']:.4f} (±{perf['cv_f1_std']:.4f})")

        # Global SHAP importance
        report.append(f"\n🎯 GLOBAL SHAP FEATURE IMPORTANCE:")

        for algo_key, importance_data in results['global_importance'].items():
            report.append(f"\n   📊 {importance_data['algorithm']}:")

            # Top 10 features
            top_features = importance_data['feature_importance']
            for i, item in enumerate(top_features, 1):
                importance = item['shap_importance']
                feature = item['feature']

                # Add visual indicator
                if importance >= 0.1:
                    icon = "🔥"  # Very high
                elif importance >= 0.05:
                    icon = "⭐"  # High
                elif importance >= 0.02:
                    icon = "🔸"  # Medium
                else:
                    icon = "▫️"   # Low

                report.append(f"     {i:2d}. {icon} {feature}: {importance:.4f}")

        # SHAP vs other methods comparison
        report.append(f"\n🔬 SHAP ANALYSIS ADVANTAGES:")
        report.append(f"   • Theoretically grounded (Shapley values from game theory)")
        report.append(f"   • Individual prediction explanations available")
        report.append(f"   • Captures feature interactions")
        report.append(f"   • Model-agnostic interpretability")
        report.append(f"   • Positive/negative contribution analysis")

        # Feature consistency across algorithms
        report.append(f"\n🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:")

        # Find features that appear in top 5 for multiple algorithms
        top5_features = {}
        for algo_key, importance_data in results['global_importance'].items():
            top5 = [item['feature'] for item in importance_data['feature_importance'][:5]]
            for feature in top5:
                top5_features[feature] = top5_features.get(feature, 0) + 1

        consistent_features = [(f, count) for f, count in top5_features.items() if count > 1]
        consistent_features.sort(key=lambda x: x[1], reverse=True)

        if consistent_features:
            report.append(f"   Most consistent features (appearing in multiple top-5 lists):")
            for feature, count in consistent_features:
                algorithms_count = len(results['global_importance'])
                percentage = (count / algorithms_count) * 100
                report.append(f"     • {feature}: {count}/{algorithms_count} algorithms ({percentage:.1f}%)")
        else:
            report.append(f"   No features consistently appear in top-5 across algorithms")

        # Recommendations
        report.append(f"\n✅ SHAP-BASED RECOMMENDATIONS:")

        # Find the best performing algorithm
        best_algo = max(performance.items(), key=lambda x: x[1]['accuracy'])
        best_algo_key, best_perf = best_algo
        best_importance = results['global_importance'][best_algo_key]

        report.append(f"   • Best performing algorithm: {best_perf['algorithm_name']} ({best_perf['accuracy']:.4f} accuracy)")
        report.append(f"   • Top 5 SHAP features for production:")

        for i, item in enumerate(best_importance['feature_importance'][:5], 1):
            report.append(f"     {i}. {item['feature']} (SHAP: {item['shap_importance']:.4f})")

        report.append(f"\n📊 SHAP ANALYSIS SUMMARY:")
        report.append(f"   • Total algorithms analyzed: {len(results['global_importance'])}")
        report.append(f"   • Total features analyzed: {len(dataset_info['features'])}")
        report.append(f"   • Best accuracy achieved: {best_perf['accuracy']:.4f}")
        report.append(f"   • Analysis timestamp: {results['timestamp']}")

        return "\n".join(report)

    def _generate_shap_features_code(self, results: Dict, timestamp: str) -> str:
        """Generate Python code with SHAP-based feature selections"""

        # Find best performing algorithm
        performance = results['model_performance']
        best_algo = max(performance.items(), key=lambda x: x[1]['accuracy'])
        best_algo_key, best_perf = best_algo
        best_importance = results['global_importance'][best_algo_key]

        code_lines = []
        code_lines.append('"""')
        code_lines.append(f'SHAP-Based Optimal Features - Generated {timestamp}')
        code_lines.append(f'Best Algorithm: {best_perf["algorithm_name"]} (Accuracy: {best_perf["accuracy"]:.4f})')
        code_lines.append('"""')
        code_lines.append('')

        # All features ranked by SHAP importance
        code_lines.append('# All features ranked by SHAP importance')
        code_lines.append('SHAP_RANKED_FEATURES = [')
        for item in best_importance['feature_importance']:
            code_lines.append(f'    "{item["feature"]}",  # SHAP: {item["shap_importance"]:.4f}')
        code_lines.append(']')
        code_lines.append('')

        # Top features by different thresholds
        thresholds = [5, 10, 15]
        for threshold in thresholds:
            if threshold <= len(best_importance['feature_importance']):
                code_lines.append(f'# Top {threshold} SHAP features')
                code_lines.append(f'TOP_{threshold}_SHAP_FEATURES = [')
                for item in best_importance['feature_importance'][:threshold]:
                    code_lines.append(f'    "{item["feature"]}",  # SHAP: {item["shap_importance"]:.4f}')
                code_lines.append(']')
                code_lines.append('')

        # SHAP importance dictionary
        code_lines.append('# SHAP importance scores')
        code_lines.append('SHAP_IMPORTANCE_SCORES = {')
        for item in best_importance['feature_importance']:
            code_lines.append(f'    "{item["feature"]}": {item["shap_importance"]:.6f},')
        code_lines.append('}')
        code_lines.append('')

        # Usage example
        code_lines.append('# Usage example:')
        code_lines.append('# X_shap = X[TOP_5_SHAP_FEATURES]')
        code_lines.append('# model.fit(X_shap, y)')

        return '\n'.join(code_lines)

    def create_comprehensive_shap_report(self, results: Dict, save_plots: bool = True):
        """Create comprehensive SHAP visualizations and report"""
        logger.info("Creating comprehensive SHAP visualizations...")

        if not save_plots:
            logger.info("Visualization saving disabled")
            return

        try:
            import matplotlib
            matplotlib.use('Agg')
            import matplotlib.pyplot as plt
            try:
                import seaborn as sns
                sns.set_style("whitegrid")
            except ImportError:
                logger.warning("Seaborn not available, using matplotlib only")
                sns = None
            from pathlib import Path
            import warnings
            warnings.filterwarnings('ignore')

            # Create visualization directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 1. Create SHAP feature importance comparison plot
            self._create_shap_importance_plots(results, viz_dir, timestamp)

            # 2. Create cross-validation plots
            self._create_cv_plots(results, viz_dir, timestamp)

            # 3. Create SHAP summary plots for each algorithm
            self._create_shap_summary_plots(results, viz_dir, timestamp)

            # 4. Create feature consistency analysis
            self._create_feature_consistency_plot(results, viz_dir, timestamp)

            # 5. Create SHAP waterfall plots
            self.create_shap_waterfall_plots(results, save_plots=True)

            logger.info("✅ SHAP comprehensive visualizations completed")

        except Exception as e:
            logger.error(f"❌ Failed to create SHAP visualizations: {str(e)}")
            logger.warning("Continuing without visualizations...")

    def _create_shap_importance_plots(self, results: Dict, viz_dir: Path, timestamp: str):
        """Create SHAP feature importance comparison plots"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd

            # Create main comparison plot
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('SHAP Feature Importance Analysis', fontsize=16, fontweight='bold')

            # Plot 1: Feature importance heatmap
            if 'global_importance' in results:
                importance_data = []
                for _, importance_info in results['global_importance'].items():
                    for item in importance_info['feature_importance'][:10]:  # Top 10 features
                        importance_data.append({
                            'Algorithm': importance_info['algorithm'],
                            'Feature': item['feature'],
                            'SHAP_Importance': item['shap_importance'],
                            'Rank': item['rank']
                        })

                if importance_data:
                    df_importance = pd.DataFrame(importance_data)

                    # Create pivot table for heatmap
                    pivot_data = df_importance.pivot(index='Feature', columns='Algorithm', values='SHAP_Importance')

                    # Use seaborn if available, otherwise matplotlib
                    try:
                        import seaborn as sns
                        sns.heatmap(pivot_data, annot=True, fmt='.4f', cmap='YlOrRd', ax=axes[0, 0])
                    except ImportError:
                        im = axes[0, 0].imshow(pivot_data.values, cmap='YlOrRd', aspect='auto')
                        axes[0, 0].set_xticks(range(len(pivot_data.columns)))
                        axes[0, 0].set_yticks(range(len(pivot_data.index)))
                        axes[0, 0].set_xticklabels(pivot_data.columns)
                        axes[0, 0].set_yticklabels(pivot_data.index)
                        plt.colorbar(im, ax=axes[0, 0])

                    axes[0, 0].set_title('SHAP Feature Importance Heatmap')
                    axes[0, 0].set_xlabel('Algorithm')
                    axes[0, 0].set_ylabel('Feature')

            # Plot 2: Model performance comparison with CV
            if 'model_performance' in results:
                algorithms = []
                accuracies = []
                f1_scores = []
                cv_acc_means = []
                cv_acc_stds = []

                for _, perf in results['model_performance'].items():
                    algorithms.append(perf['algorithm_name'])
                    accuracies.append(perf['accuracy'])
                    f1_scores.append(perf['f1_score'])
                    cv_acc_means.append(perf.get('cv_accuracy_mean', perf['accuracy']))
                    cv_acc_stds.append(perf.get('cv_accuracy_std', 0))

                x = range(len(algorithms))
                width = 0.35

                axes[0, 1].bar([i - width/2 for i in x], accuracies, width, label='Test Accuracy', alpha=0.8)
                axes[0, 1].errorbar(x, cv_acc_means, yerr=cv_acc_stds, fmt='ro', label='CV Accuracy', capsize=5)

                axes[0, 1].set_xlabel('Algorithm')
                axes[0, 1].set_ylabel('Accuracy')
                axes[0, 1].set_title('Model Performance: Test vs CV')
                axes[0, 1].set_xticks(x)
                axes[0, 1].set_xticklabels(algorithms, rotation=45)
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)

            # Plot 3: Top features consistency
            if 'global_importance' in results:
                # Find features that appear in top 5 for multiple algorithms
                top5_features = {}
                for _, importance_data in results['global_importance'].items():
                    top5 = [item['feature'] for item in importance_data['feature_importance'][:5]]
                    for feature in top5:
                        top5_features[feature] = top5_features.get(feature, 0) + 1

                consistent_features = [(f, count) for f, count in top5_features.items() if count > 1]
                consistent_features.sort(key=lambda x: x[1], reverse=True)

                if consistent_features:
                    features, counts = zip(*consistent_features[:10])  # Top 10 consistent features
                    axes[1, 0].barh(features, counts, alpha=0.7, color='skyblue')
                    axes[1, 0].set_xlabel('Number of Algorithms')
                    axes[1, 0].set_title('Feature Consistency Across Algorithms')
                    axes[1, 0].grid(True, alpha=0.3)

            # Plot 4: SHAP importance distribution
            if 'global_importance' in results:
                all_importances = []
                for _, importance_info in results['global_importance'].items():
                    for item in importance_info['feature_importance']:
                        all_importances.append(item['shap_importance'])

                if all_importances and len(all_importances) > 0:
                    axes[1, 1].hist(all_importances, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
                    axes[1, 1].axvline(np.mean(all_importances), color='red', linestyle='--',
                                      label=f'Mean: {np.mean(all_importances):.4f}')
                    axes[1, 1].set_xlabel('SHAP Importance')
                    axes[1, 1].set_ylabel('Frequency')
                    axes[1, 1].set_title('SHAP Importance Distribution')
                    axes[1, 1].legend()
                    axes[1, 1].grid(True, alpha=0.3)
                else:
                    axes[1, 1].text(0.5, 0.5, 'No SHAP importance data',
                                    transform=axes[1, 1].transAxes, ha='center', va='center')
                    axes[1, 1].set_title('SHAP Importance Distribution')

            plt.tight_layout()

            # Save plot
            plot_path = viz_dir / f'shap_analysis_{timestamp}.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"✅ Saved SHAP analysis plot to {plot_path}")

            plt.close()

            # Create individual feature importance plots for each algorithm
            for _, importance_info in results.get('global_importance', {}).items():
                fig, ax = plt.subplots(figsize=(12, 8))

                top_features = importance_info['feature_importance'][:15]  # Top 15 features
                features = [item['feature'] for item in top_features]
                importances = [item['shap_importance'] for item in top_features]

                bars = ax.barh(features, importances, alpha=0.7)
                ax.set_xlabel('SHAP Importance')
                ax.set_title(f'Top 15 SHAP Features - {importance_info["algorithm"]}')
                ax.grid(True, alpha=0.3)

                # Color bars based on importance
                for i, bar in enumerate(bars):
                    if importances[i] >= 0.1:
                        bar.set_color('red')  # Very high
                    elif importances[i] >= 0.05:
                        bar.set_color('orange')  # High
                    elif importances[i] >= 0.02:
                        bar.set_color('yellow')  # Medium
                    else:
                        bar.set_color('lightblue')  # Low

                plt.tight_layout()

                # Save individual plot
                algo_name = importance_info['algorithm'].replace(' ', '_').lower()
                individual_plot_path = viz_dir / f'shap_{algo_name}_{timestamp}.png'
                plt.savefig(individual_plot_path, dpi=300, bbox_inches='tight')
                logger.info(f"✅ Saved {importance_info['algorithm']} SHAP plot to {individual_plot_path}")

                plt.close()

        except Exception as e:
            logger.error(f"❌ Failed to create SHAP importance plots: {str(e)}")

    def _create_cv_plots(self, results: Dict, viz_dir: Path, timestamp: str):
        """Create cross-validation performance plots"""
        try:
            import matplotlib.pyplot as plt

            if 'model_performance' not in results:
                return

            # Create CV performance comparison
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Cross-Validation Performance Analysis', fontsize=16, fontweight='bold')

            algorithms = []
            cv_acc_means = []
            cv_acc_stds = []
            cv_f1_means = []
            cv_f1_stds = []
            test_accuracies = []
            test_f1s = []

            for _, perf in results['model_performance'].items():
                algorithms.append(perf['algorithm_name'])
                cv_acc_means.append(perf.get('cv_accuracy_mean', perf['accuracy']))
                cv_acc_stds.append(perf.get('cv_accuracy_std', 0))
                cv_f1_means.append(perf.get('cv_f1_mean', perf['f1_score']))
                cv_f1_stds.append(perf.get('cv_f1_std', 0))
                test_accuracies.append(perf['accuracy'])
                test_f1s.append(perf['f1_score'])

            x = range(len(algorithms))

            # Plot 1: CV Accuracy with error bars
            axes[0, 0].bar(x, cv_acc_means, yerr=cv_acc_stds, capsize=5, alpha=0.7, color='lightblue')
            axes[0, 0].set_xlabel('Algorithm')
            axes[0, 0].set_ylabel('CV Accuracy')
            axes[0, 0].set_title('Cross-Validation Accuracy')
            axes[0, 0].set_xticks(x)
            axes[0, 0].set_xticklabels(algorithms, rotation=45)
            axes[0, 0].grid(True, alpha=0.3)

            # Plot 2: CV F1-Score with error bars
            axes[0, 1].bar(x, cv_f1_means, yerr=cv_f1_stds, capsize=5, alpha=0.7, color='lightcoral')
            axes[0, 1].set_xlabel('Algorithm')
            axes[0, 1].set_ylabel('CV F1-Score')
            axes[0, 1].set_title('Cross-Validation F1-Score')
            axes[0, 1].set_xticks(x)
            axes[0, 1].set_xticklabels(algorithms, rotation=45)
            axes[0, 1].grid(True, alpha=0.3)

            # Plot 3: CV vs Test Accuracy comparison
            width = 0.35
            axes[1, 0].bar([i - width/2 for i in x], cv_acc_means, width, label='CV Accuracy', alpha=0.8)
            axes[1, 0].bar([i + width/2 for i in x], test_accuracies, width, label='Test Accuracy', alpha=0.8)
            axes[1, 0].set_xlabel('Algorithm')
            axes[1, 0].set_ylabel('Accuracy')
            axes[1, 0].set_title('CV vs Test Accuracy')
            axes[1, 0].set_xticks(x)
            axes[1, 0].set_xticklabels(algorithms, rotation=45)
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # Plot 4: Performance variance (CV std)
            axes[1, 1].bar(x, cv_acc_stds, alpha=0.7, color='orange')
            axes[1, 1].set_xlabel('Algorithm')
            axes[1, 1].set_ylabel('CV Accuracy Std')
            axes[1, 1].set_title('Model Stability (Lower is Better)')
            axes[1, 1].set_xticks(x)
            axes[1, 1].set_xticklabels(algorithms, rotation=45)
            axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()

            # Save plot
            cv_plot_path = viz_dir / f'cv_analysis_{timestamp}.png'
            plt.savefig(cv_plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"✅ Saved CV analysis plot to {cv_plot_path}")

            plt.close()

        except Exception as e:
            logger.error(f"❌ Failed to create CV plots: {str(e)}")

    def _create_shap_summary_plots(self, results: Dict, viz_dir: Path, timestamp: str):
        """Create SHAP summary plots for each algorithm"""
        try:
            import matplotlib.pyplot as plt

            if not hasattr(self, 'shap_values') or not self.shap_values:
                logger.warning("No SHAP values available for summary plots")
                return

            for algo_key, shap_data in self.shap_values.items():
                try:
                    config = self.models[algo_key]['config']
                    shap_vals = shap_data['values']
                    data = shap_data['data']

                    # Create SHAP summary plot
                    plt.figure(figsize=(12, 8))

                    # Handle multi-class SHAP values
                    if isinstance(shap_vals, list):
                        # For multi-class, use the first class or average
                        if len(shap_vals) > 0:
                            plot_vals = shap_vals[0] if len(shap_vals) == 2 else np.mean(shap_vals, axis=0)
                        else:
                            continue
                    else:
                        plot_vals = shap_vals

                    # Create summary plot using matplotlib (since shap.summary_plot might not work)
                    if plot_vals.size > 0:
                        feature_importance = np.abs(plot_vals).mean(axis=0)
                        feature_names = self.feature_names

                        # Sort features by importance
                        sorted_idx = np.argsort(feature_importance)[-15:]  # Top 15 features

                        if len(sorted_idx) > 0:
                            plt.barh(range(len(sorted_idx)), feature_importance[sorted_idx])
                            plt.yticks(range(len(sorted_idx)), [feature_names[i] for i in sorted_idx])
                            plt.xlabel('Mean |SHAP Value|')
                            plt.title(f'SHAP Feature Importance - {config["name"]}')
                            plt.grid(True, alpha=0.3)
                        else:
                            plt.text(0.5, 0.5, 'No features to display', ha='center', va='center')
                            plt.title(f'SHAP Feature Importance - {config["name"]}')
                    else:
                        plt.text(0.5, 0.5, 'No SHAP values available', ha='center', va='center')
                        plt.title(f'SHAP Feature Importance - {config["name"]}')

                    plt.tight_layout()

                    # Save plot
                    summary_plot_path = viz_dir / f'shap_summary_{algo_key}_{timestamp}.png'
                    plt.savefig(summary_plot_path, dpi=300, bbox_inches='tight')
                    logger.info(f"✅ Saved SHAP summary plot for {config['name']} to {summary_plot_path}")

                    plt.close()

                except Exception as e:
                    logger.warning(f"Failed to create SHAP summary plot for {algo_key}: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"❌ Failed to create SHAP summary plots: {str(e)}")

    def _create_feature_consistency_plot(self, results: Dict, viz_dir: Path, timestamp: str):
        """Create feature consistency analysis plot"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd

            if 'global_importance' not in results:
                return

            # Create feature ranking comparison
            fig, axes = plt.subplots(2, 1, figsize=(14, 10))
            fig.suptitle('Feature Ranking Consistency Analysis', fontsize=16, fontweight='bold')

            # Prepare data for ranking comparison
            all_features = set()
            algorithm_rankings = {}

            for algo_key, importance_info in results['global_importance'].items():
                algorithm_rankings[importance_info['algorithm']] = {}
                for item in importance_info['feature_importance']:
                    feature = item['feature']
                    rank = item['rank']
                    all_features.add(feature)
                    algorithm_rankings[importance_info['algorithm']][feature] = rank

            # Create ranking matrix
            ranking_data = []
            for feature in sorted(all_features):
                row = {'Feature': feature}
                for algo_name in algorithm_rankings.keys():
                    row[algo_name] = algorithm_rankings[algo_name].get(feature, len(all_features) + 1)
                ranking_data.append(row)

            df_rankings = pd.DataFrame(ranking_data)

            # Plot 1: Top 20 features ranking heatmap
            top_features = df_rankings.head(20)  # Top 20 features by average rank

            # Calculate average rank for sorting
            algo_cols = [col for col in top_features.columns if col != 'Feature']
            top_features['avg_rank'] = top_features[algo_cols].mean(axis=1)
            top_features = top_features.sort_values('avg_rank')

            # Create heatmap data
            heatmap_data = top_features[algo_cols].values

            im = axes[0].imshow(heatmap_data, cmap='RdYlBu_r', aspect='auto')
            axes[0].set_xticks(range(len(algo_cols)))
            axes[0].set_yticks(range(len(top_features)))
            axes[0].set_xticklabels(algo_cols, rotation=45)
            axes[0].set_yticklabels(top_features['Feature'].values)
            axes[0].set_title('Feature Ranking Heatmap (Top 20 Features)')
            axes[0].set_xlabel('Algorithm')
            axes[0].set_ylabel('Feature')

            # Add colorbar
            cbar = plt.colorbar(im, ax=axes[0])
            cbar.set_label('Rank (Lower is Better)')

            # Plot 2: Ranking variance analysis
            ranking_variance = []
            feature_names = []

            for _, row in top_features.iterrows():
                ranks = row[algo_cols].values
                variance = np.var(ranks)
                ranking_variance.append(variance)
                feature_names.append(row['Feature'])

            # Sort by variance (most consistent first)
            sorted_indices = np.argsort(ranking_variance)
            sorted_variance = [ranking_variance[i] for i in sorted_indices]
            sorted_names = [feature_names[i] for i in sorted_indices]

            axes[1].barh(range(len(sorted_variance)), sorted_variance, alpha=0.7, color='lightcoral')
            axes[1].set_yticks(range(len(sorted_names)))
            axes[1].set_yticklabels(sorted_names)
            axes[1].set_xlabel('Ranking Variance')
            axes[1].set_title('Feature Ranking Consistency (Lower Variance = More Consistent)')
            axes[1].grid(True, alpha=0.3)

            plt.tight_layout()

            # Save plot
            consistency_plot_path = viz_dir / f'feature_consistency_{timestamp}.png'
            plt.savefig(consistency_plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"✅ Saved feature consistency plot to {consistency_plot_path}")

            plt.close()

        except Exception as e:
            logger.error(f"❌ Failed to create feature consistency plot: {str(e)}")

    def create_shap_waterfall_plots(self, results: Dict, save_plots: bool = True):
        """Create SHAP waterfall plots for individual predictions"""
        if not save_plots:
            return

        try:
            import matplotlib.pyplot as plt
            from pathlib import Path

            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if not hasattr(self, 'shap_values') or not self.shap_values:
                logger.warning("No SHAP values available for waterfall plots")
                return

            # Create waterfall plots for first few predictions of each algorithm
            for algo_key, shap_data in self.shap_values.items():
                try:
                    config = self.models[algo_key]['config']
                    shap_vals = shap_data['values']
                    data = shap_data['data']

                    # Handle multi-class SHAP values
                    if isinstance(shap_vals, list):
                        if len(shap_vals) > 0:
                            plot_vals = shap_vals[0] if len(shap_vals) == 2 else shap_vals[0]
                        else:
                            continue
                    else:
                        plot_vals = shap_vals

                    # Create waterfall plot for first prediction
                    if len(plot_vals) > 0:
                        fig, ax = plt.subplots(figsize=(12, 8))

                        # Get first prediction's SHAP values
                        first_pred_shap = plot_vals[0]
                        first_pred_data = data.iloc[0]

                        # Sort features by absolute SHAP value
                        sorted_idx = np.argsort(np.abs(first_pred_shap))[-15:]  # Top 15 features

                        # Create waterfall-like plot
                        cumulative = 0
                        positions = []
                        values = []
                        colors = []
                        labels = []

                        for i, idx in enumerate(sorted_idx):
                            shap_val = first_pred_shap[idx]
                            feature_name = self.feature_names[idx]
                            feature_val = first_pred_data.iloc[idx]

                            positions.append(i)
                            values.append(abs(shap_val))
                            colors.append('red' if shap_val > 0 else 'blue')
                            labels.append(f'{feature_name}\n({feature_val:.3f})')

                        bars = ax.bar(positions, values, color=colors, alpha=0.7)
                        ax.set_xticks(positions)
                        ax.set_xticklabels(labels, rotation=45, ha='right')
                        ax.set_ylabel('|SHAP Value|')
                        ax.set_title(f'SHAP Waterfall Plot - {config["name"]}\n(Sample Prediction)')
                        ax.grid(True, alpha=0.3)

                        # Add legend
                        red_patch = plt.Rectangle((0, 0), 1, 1, fc="red", alpha=0.7)
                        blue_patch = plt.Rectangle((0, 0), 1, 1, fc="blue", alpha=0.7)
                        ax.legend([red_patch, blue_patch], ['Positive Impact', 'Negative Impact'])

                        plt.tight_layout()

                        # Save plot
                        waterfall_path = viz_dir / f'shap_waterfall_{algo_key}_{timestamp}.png'
                        plt.savefig(waterfall_path, dpi=300, bbox_inches='tight')
                        logger.info(f"✅ Saved SHAP waterfall plot for {config['name']} to {waterfall_path}")

                        plt.close()

                except Exception as e:
                    logger.warning(f"Failed to create SHAP waterfall plot for {algo_key}: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"❌ Failed to create SHAP waterfall plots: {str(e)}")



            logger.info("✅ SHAP comprehensive visualizations completed")

        except Exception as e:
            logger.error(f"❌ Failed to create SHAP visualizations: {str(e)}")
            logger.warning("Continuing without visualizations...")
