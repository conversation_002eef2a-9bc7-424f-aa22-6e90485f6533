"""
SHAP-Based Optimal Features - Generated 20250722_030308
Best Algorithm: XGBoost (Accuracy: 0.7966)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0554
    "total_title_diversity",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0518
    "avg_time_minutes",  # SHAP: 0.0472
    "total_time_minutes",  # SHAP: 0.0399
    "work_days",  # SHAP: 0.0349
    "consistency_score",  # SHAP: 0.0309
    "avg_distance_km",  # SHAP: 0.0286
    "gamification_balance",  # SHAP: 0.0285
    "activity_points",  # SHAP: 0.0273
    "achievement_rate",  # SHAP: 0.0218
    "total_distance_km",  # SHAP: 0.0122
    "productivity_points",  # SHAP: 0.0076
    "strava_title_count",  # SHAP: 0.0028
    "strava_unique_words",  # SHAP: 0.0021
    "total_cycles",  # SHAP: 0.0000
    "pomokit_title_count",  # SHAP: 0.0000
    "avg_cycles",  # SHAP: 0.0000
    "activity_days",  # SHAP: 0.0000
    "weekly_efficiency",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0554
    "total_title_diversity",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0518
    "avg_time_minutes",  # SHAP: 0.0472
    "total_time_minutes",  # SHAP: 0.0399
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0554
    "total_title_diversity",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0518
    "avg_time_minutes",  # SHAP: 0.0472
    "total_time_minutes",  # SHAP: 0.0399
    "work_days",  # SHAP: 0.0349
    "consistency_score",  # SHAP: 0.0309
    "avg_distance_km",  # SHAP: 0.0286
    "gamification_balance",  # SHAP: 0.0285
    "activity_points",  # SHAP: 0.0273
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0554
    "total_title_diversity",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0518
    "avg_time_minutes",  # SHAP: 0.0472
    "total_time_minutes",  # SHAP: 0.0399
    "work_days",  # SHAP: 0.0349
    "consistency_score",  # SHAP: 0.0309
    "avg_distance_km",  # SHAP: 0.0286
    "gamification_balance",  # SHAP: 0.0285
    "activity_points",  # SHAP: 0.0273
    "achievement_rate",  # SHAP: 0.0218
    "total_distance_km",  # SHAP: 0.0122
    "productivity_points",  # SHAP: 0.0076
    "strava_title_count",  # SHAP: 0.0028
    "strava_unique_words",  # SHAP: 0.0021
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "pomokit_unique_words": 0.055416,
    "total_title_diversity": 0.053034,
    "title_balance_ratio": 0.051779,
    "avg_time_minutes": 0.047197,
    "total_time_minutes": 0.039930,
    "work_days": 0.034910,
    "consistency_score": 0.030875,
    "avg_distance_km": 0.028570,
    "gamification_balance": 0.028528,
    "activity_points": 0.027347,
    "achievement_rate": 0.021829,
    "total_distance_km": 0.012213,
    "productivity_points": 0.007593,
    "strava_title_count": 0.002769,
    "strava_unique_words": 0.002110,
    "total_cycles": 0.000015,
    "pomokit_title_count": 0.000005,
    "avg_cycles": 0.000000,
    "activity_days": 0.000000,
    "weekly_efficiency": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)