#!/usr/bin/env python3
"""
Test script for model saving functionality
Tests the new model saving and loading capabilities
"""

import sys
import logging
from pathlib import Path
import pandas as pd
import numpy as np

# Add src to path
sys.path.append('src')

from utils.data_utils import save_model_artifacts, load_model_artifacts
from shap_ablation_study import SHAPAblationStudy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_model_saving_loading():
    """Test model saving and loading functionality"""
    logger.info("🧪 Testing Model Saving and Loading Functionality")
    logger.info("="*60)
    
    # Check if required dataset exists
    safe_dataset_path = "dataset/processed/safe_ml_fatigue_dataset.csv"
    
    if not Path(safe_dataset_path).exists():
        logger.error(f"❌ Required dataset not found: {safe_dataset_path}")
        logger.info("Please run the main pipeline first to generate the dataset:")
        logger.info("python main1.py --fatigue-only")
        return False
    
    try:
        # Step 1: Train models using SHAP ablation study
        logger.info("Step 1: Training models using SHAP ablation study...")
        shap_study = SHAPAblationStudy(
            data_path=safe_dataset_path,
            target_column='fatigue_risk',
            random_state=42
        )
        
        # Run complete study (this will save models automatically)
        study_results = shap_study.run_complete_shap_study()
        
        # Save results and models
        results_file, report_file = shap_study.save_results(study_results)
        
        logger.info("✅ Models trained and saved successfully")
        
        # Step 2: Test loading saved models
        logger.info("\nStep 2: Testing model loading...")
        
        model_dir = Path("results/clean_production_model")
        if not model_dir.exists():
            logger.error(f"❌ Model directory not found: {model_dir}")
            return False
        
        # Find saved model directories
        model_files = list(model_dir.glob("*best_model_pipeline*.pkl"))
        if not model_files:
            logger.error("❌ No saved model files found")
            return False
        
        # Load the best model artifacts
        logger.info("Loading best model artifacts...")
        try:
            artifacts = load_model_artifacts(model_dir)
            
            if 'pipeline' in artifacts:
                logger.info("✅ Model pipeline loaded successfully")
                logger.info(f"   Model type: {type(artifacts['pipeline']).__name__}")
            
            if 'metadata' in artifacts:
                logger.info("✅ Model metadata loaded successfully")
                metadata = artifacts['metadata']
                logger.info(f"   Algorithm: {metadata.get('algorithm_name', 'Unknown')}")
                logger.info(f"   Accuracy: {metadata.get('accuracy', 0.0):.4f}")
                logger.info(f"   Features: {metadata.get('feature_count', 0)}")
            
            if 'features' in artifacts:
                logger.info("✅ Feature names loaded successfully")
                logger.info(f"   Feature count: {len(artifacts['features'])}")
            
            if 'label_encoder' in artifacts:
                logger.info("✅ Label encoder loaded successfully")
                logger.info(f"   Classes: {list(artifacts['label_encoder'].classes_)}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load model artifacts: {str(e)}")
            return False
        
        # Step 3: Test model prediction
        logger.info("\nStep 3: Testing model prediction...")
        
        try:
            # Load test data
            data = pd.read_csv(safe_dataset_path)
            
            # Get features and target
            feature_names = artifacts['features']
            X_test = data[feature_names].head(5)  # Test with first 5 samples
            y_true = data['fatigue_risk'].head(5)
            
            # Make predictions
            model = artifacts['pipeline']
            y_pred = model.predict(X_test)
            
            # Decode predictions if label encoder is available
            if 'label_encoder' in artifacts:
                y_pred_decoded = artifacts['label_encoder'].inverse_transform(y_pred)
                logger.info("✅ Model predictions successful")
                logger.info("   Sample predictions:")
                for i, (true_val, pred_val) in enumerate(zip(y_true, y_pred_decoded)):
                    logger.info(f"     Sample {i+1}: True={true_val}, Predicted={pred_val}")
            else:
                logger.info("✅ Model predictions successful (encoded)")
                logger.info(f"   Predictions: {y_pred}")
            
        except Exception as e:
            logger.error(f"❌ Failed to make predictions: {str(e)}")
            return False
        
        # Step 4: Verify file structure
        logger.info("\nStep 4: Verifying saved file structure...")
        
        expected_files = [
            "*best_model_pipeline*.pkl",
            "*best_model_metadata*.pkl", 
            "*best_model_metadata*.json",
            "*best_model_features*.pkl",
            "*best_model_label_encoder*.pkl"
        ]
        
        for pattern in expected_files:
            files = list(model_dir.glob(pattern))
            if files:
                logger.info(f"✅ Found {pattern}: {files[0].name}")
            else:
                logger.warning(f"⚠️  Missing {pattern}")
        
        logger.info("\n🎉 Model Saving and Loading Test Completed Successfully!")
        logger.info("="*60)
        
        # Summary
        logger.info("\n📋 SUMMARY:")
        logger.info(f"   • Models saved to: {model_dir}")
        logger.info(f"   • Best model algorithm: {metadata.get('algorithm_name', 'Unknown')}")
        logger.info(f"   • Model accuracy: {metadata.get('accuracy', 0.0):.4f}")
        logger.info(f"   • Feature count: {metadata.get('feature_count', 0)}")
        logger.info(f"   • Model can make predictions: ✅")
        logger.info(f"   • All artifacts loadable: ✅")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        logger.error("Full error details:", exc_info=True)
        return False

def main():
    """Main test function"""
    logger.info("🚀 Starting Model Saving Test Suite")
    
    success = test_model_saving_loading()
    
    if success:
        logger.info("\n✅ All tests passed! Model saving functionality is working correctly.")
        return 0
    else:
        logger.error("\n❌ Tests failed! Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
