{"cells": [{"cell_type": "markdown", "id": "b48e3661", "metadata": {}, "source": ["Data dari 10 maret sampai 2 mei 2025"]}, {"cell_type": "code", "execution_count": 37, "id": "66e6ca89", "metadata": {}, "outputs": [], "source": ["# import dependensi\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "1e321c1b", "metadata": {}, "source": ["## Dataset Strava"]}, {"cell_type": "code", "execution_count": 38, "id": "ea37e8fa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "      <th>gtmetrix_grade</th>\n", "      <th>gtmetrix_performance</th>\n", "      <th>gtmetrix_structure</th>\n", "      <th>lcp</th>\n", "      <th>tbt</th>\n", "      <th>cls</th>\n", "      <th>gtmetrix_url_target</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67d03bd5b249b1388ac7c61d</td>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>Laptop-mahalIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>edit content web portofolio</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIyOjMwOjE0...</td>\n", "      <td>https://venalism.github.io/</td>\n", "      <td>2025-03-11T13:34:13.688Z</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67d04b7855dfbb2c3f9d9265</td>\n", "      <td>6283137760847</td>\n", "      <td>1</td>\n", "      <td>SyalwalyrhIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>mining, buat website, sambungin webhook, dll</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjIxOjUx...</td>\n", "      <td>https://hp-pinjam.github.io/proposal-hp-pinjam/</td>\n", "      <td>2025-03-11T14:40:56.585Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67d04f9355dfbb2c3f9d926c</td>\n", "      <td>6282116301579</td>\n", "      <td>1</td>\n", "      <td>V<PERSON>a_AlmirahIP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>buat website, mining, materi buku,</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjQ1OjI3...</td>\n", "      <td>https://hp-pinjam.github.io/proposal-hp-pinjam/</td>\n", "      <td>2025-03-11T14:58:27.187Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67d0505a55dfbb2c3f9d926f</td>\n", "      <td>6285722341788</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-EME1OPIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td>membuat website dan maining</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjU2OjE2...</td>\n", "      <td>https://fauzinrfdlh10.github.io/</td>\n", "      <td>2025-03-11T15:01:46.410Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d052f755dfbb2c3f9d9274</td>\n", "      <td>6285158354600</td>\n", "      <td>1</td>\n", "      <td>SahaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>3</td>\n", "      <td>memperbaiki tampilan web</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTEyVDAwOjA5OjUw...</td>\n", "      <td>https://saha-hub123.github.io/Isa.github.io/</td>\n", "      <td>2025-03-11T15:12:55.597Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1233</th>\n", "      <td>68415d202a714c784c3741e7</td>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>NOGIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:8000:102...</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> tugas dan melanjutkan tugas</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcHJveWVrMXdl...</td>\n", "      <td>https://proyek1webs.github.io/BananaChip/</td>\n", "      <td>2025-06-05T09:02:24.748Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1234</th>\n", "      <td>6841846cbb465f4e88bad561</td>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>Laptop-mahalIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2402:5680:932...</td>\n", "      <td>4</td>\n", "      <td>weekly progress report</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/raoz-kitchen-test...</td>\n", "      <td>2025-06-05T11:50:04.240Z</td>\n", "      <td>1.203634e+17</td>\n", "      <td>Nadi</td>\n", "      <td>A</td>\n", "      <td>99%</td>\n", "      <td>100%</td>\n", "      <td>837ms</td>\n", "      <td>0ms</td>\n", "      <td>0.0</td>\n", "      <td>https://raoz-kitchen-test.sgp.dom.my.id/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1235</th>\n", "      <td>6841a6c1251c46cfa1da0c65</td>\n", "      <td>6289685587547</td>\n", "      <td>1</td>\n", "      <td>WIN-VM3JP598T2GIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>lanjut</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/laundryu9.shop/uT...</td>\n", "      <td>2025-06-05T14:16:33.438Z</td>\n", "      <td>1.203634e+17</td>\n", "      <td>richard</td>\n", "      <td>A</td>\n", "      <td>100%</td>\n", "      <td>95%</td>\n", "      <td>486ms</td>\n", "      <td>0ms</td>\n", "      <td>0.0</td>\n", "      <td>http://laundryu9.shop/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1236</th>\n", "      <td>6841aade251c46cfa1da0c6a</td>\n", "      <td>6282258120851</td>\n", "      <td>1</td>\n", "      <td>lifyaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:c0:2d10:...</td>\n", "      <td>4</td>\n", "      <td>mengecek gtmetrix lagi</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/alifyazz.github.i...</td>\n", "      <td>2025-06-05T14:34:06.848Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>A</td>\n", "      <td>100%</td>\n", "      <td>99%</td>\n", "      <td>203ms</td>\n", "      <td>0ms</td>\n", "      <td>0.0</td>\n", "      <td>https://alifyazz.github.io/landingpage-yulis.io/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1237</th>\n", "      <td>6841f694ca2e662ccd018e55</td>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>NOGIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:8000:102...</td>\n", "      <td>4</td>\n", "      <td>lanjut</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/nogi3201.github.i...</td>\n", "      <td>2025-06-05T19:57:08.882Z</td>\n", "      <td>1.203634e+17</td>\n", "      <td><PERSON></td>\n", "      <td>A</td>\n", "      <td>100%</td>\n", "      <td>98%</td>\n", "      <td>194ms</td>\n", "      <td>0ms</td>\n", "      <td>0.0</td>\n", "      <td>https://nogi3201.github.io/</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1238 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                           _id    phonenumber  cycle           hostname  \\\n", "0     67d03bd5b249b1388ac7c61d  6288970788847      1     Laptop-mahalIP   \n", "1     67d04b7855dfbb2c3f9d9265  6283137760847      1       SyalwalyrhIP   \n", "2     67d04f9355dfbb2c3f9d926c  6282116301579      1    Virna_AlmirahIP   \n", "3     67d0505a55dfbb2c3f9d926f  6285722341788      1  DESKTOP-EME1OPIIP   \n", "4     67d052f755dfbb2c3f9d9274  6285158354600      1             SahaIP   \n", "...                        ...            ...    ...                ...   \n", "1233  68415d202a714c784c3741e7  6282161299141      1             NOGIIP   \n", "1234  6841846cbb465f4e88bad561  6288970788847      1     Laptop-mahalIP   \n", "1235  6841a6c1251c46cfa1da0c65  6289685587547      1  WIN-VM3JP598T2GIP   \n", "1236  6841aade251c46cfa1da0c6a  6282258120851      1            lifyaIP   \n", "1237  6841f694ca2e662ccd018e55  6282161299141      1             NOGIIP   \n", "\n", "                                                     ip  screenshots  \\\n", "0      https://whatismyipaddress.com/ip/***************            4   \n", "1        https://whatismyipaddress.com/ip/*************            4   \n", "2                                                   NaN            4   \n", "3       https://whatismyipaddress.com/ip/**************            4   \n", "4       https://whatismyipaddress.com/ip/**************            3   \n", "...                                                 ...          ...   \n", "1233  https://whatismyipaddress.com/ip/2404:8000:102...            4   \n", "1234  https://whatismyipaddress.com/ip/2402:5680:932...            4   \n", "1235     https://whatismyipaddress.com/ip/*************            4   \n", "1236  https://whatismyipaddress.com/ip/2404:c0:2d10:...            4   \n", "1237  https://whatismyipaddress.com/ip/2404:8000:102...            4   \n", "\n", "                                         p<PERSON><PERSON><PERSON><PERSON>  \\\n", "0                      edit content web portofolio   \n", "1     mining, buat website, sambungin webhook, dll   \n", "2               buat website, mining, materi buku,   \n", "3                      membuat website dan maining   \n", "4                         memperbaiki tampilan web   \n", "...                                            ...   \n", "1233       mengerjakan tugas dan melanjutkan tugas   \n", "1234                        weekly progress report   \n", "1235                                        lanjut   \n", "1236                        mengecek gtmetrix lagi   \n", "1237                                        lanjut   \n", "\n", "                                                  token  \\\n", "0     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIyOjMwOjE0...   \n", "1     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjIxOjUx...   \n", "2     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjQ1OjI3...   \n", "3     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjU2OjE2...   \n", "4     v4.public.eyJleHAiOiIyMDI1LTAzLTEyVDAwOjA5OjUw...   \n", "...                                                 ...   \n", "1233  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcHJveWVrMXdl...   \n", "1234  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1235  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1236  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1237  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "\n", "                                           url<PERSON><PERSON><PERSON>an  \\\n", "0                           https://venalism.github.io/   \n", "1       https://hp-pinjam.github.io/proposal-hp-pinjam/   \n", "2       https://hp-pinjam.github.io/proposal-hp-pinjam/   \n", "3                      https://fauzinrfdlh10.github.io/   \n", "4          https://saha-hub123.github.io/Isa.github.io/   \n", "...                                                 ...   \n", "1233          https://proyek1webs.github.io/BananaChip/   \n", "1234  https://gtmetrix.com/reports/raoz-kitchen-test...   \n", "1235  https://gtmetrix.com/reports/laundryu9.shop/uT...   \n", "1236  https://gtmetrix.com/reports/alifyazz.github.i...   \n", "1237  https://gtmetrix.com/reports/nogi3201.github.i...   \n", "\n", "                     createdAt     wagroupid     name gtmetrix_grade  \\\n", "0     2025-03-11T13:34:13.688Z           NaN      NaN            NaN   \n", "1     2025-03-11T14:40:56.585Z  1.203630e+17      NaN            NaN   \n", "2     2025-03-11T14:58:27.187Z  1.203630e+17      NaN            NaN   \n", "3     2025-03-11T15:01:46.410Z  1.203630e+17      NaN            NaN   \n", "4     2025-03-11T15:12:55.597Z  1.203630e+17      NaN            NaN   \n", "...                        ...           ...      ...            ...   \n", "1233  2025-06-05T09:02:24.748Z  1.203630e+17    <PERSON>   \n", "1234  2025-06-05T11:50:04.240Z  1.203634e+17     Nadi              A   \n", "1235  2025-06-05T14:16:33.438Z  1.203634e+17  richard              A   \n", "1236  2025-06-05T14:34:06.848Z  1.203630e+17   <PERSON><PERSON><PERSON>              A   \n", "1237  2025-06-05T19:57:08.882Z  1.203634e+17    <PERSON>   \n", "\n", "     gtmetrix_performance gtmetrix_structure    lcp  tbt  cls  \\\n", "0                     NaN                NaN    NaN  NaN  NaN   \n", "1                     NaN                NaN    NaN  NaN  NaN   \n", "2                     NaN                NaN    NaN  NaN  NaN   \n", "3                     NaN                NaN    NaN  NaN  NaN   \n", "4                     NaN                NaN    NaN  NaN  NaN   \n", "...                   ...                ...    ...  ...  ...   \n", "1233                  NaN                NaN    NaN  NaN  NaN   \n", "1234                  99%               100%  837ms  0ms  0.0   \n", "1235                 100%                95%  486ms  0ms  0.0   \n", "1236                 100%                99%  203ms  0ms  0.0   \n", "1237                 100%                98%  194ms  0ms  0.0   \n", "\n", "                                   gtmetrix_url_target  \n", "0                                                  NaN  \n", "1                                                  NaN  \n", "2                                                  NaN  \n", "3                                                  NaN  \n", "4                                                  NaN  \n", "...                                                ...  \n", "1233                                               NaN  \n", "1234          https://raoz-kitchen-test.sgp.dom.my.id/  \n", "1235                            http://laundryu9.shop/  \n", "1236  https://alifyazz.github.io/landingpage-yulis.io/  \n", "1237                       https://nogi3201.github.io/  \n", "\n", "[1238 rows x 19 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# read csv file\n", "file_path_pomokit = 'dataset/pomokit1.csv'\n", "df_pomokit = pd.read_csv(file_path_pomokit)\n", "df_pomokit"]}, {"cell_type": "code", "execution_count": 39, "id": "bf55e341", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id                       0\n", "phonenumber               0\n", "cycle                     0\n", "hostname                  0\n", "ip                       94\n", "screenshots               0\n", "pekerjaan                 1\n", "token                     0\n", "urlpe<PERSON><PERSON><PERSON>             36\n", "createdAt                 0\n", "wagroupid                 9\n", "name                     21\n", "gtmetrix_grade          708\n", "gtmetrix_performance    718\n", "gtmetrix_structure      718\n", "lcp                     724\n", "tbt                     878\n", "cls                     717\n", "gtmetrix_url_target     758\n", "dtype: int64"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# cek data null atau NaN\n", "df_pomokit.isna().sum()"]}, {"cell_type": "code", "execution_count": 40, "id": "00bbfc44", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id              0\n", "phonenumber      0\n", "cycle            0\n", "hostname         0\n", "ip              94\n", "screenshots      0\n", "pekerjaan        1\n", "token            0\n", "urlpe<PERSON><PERSON><PERSON>    36\n", "createdAt        0\n", "wagroupid        9\n", "name            21\n", "dtype: int64"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["kolom_gtmetrix = [\n", "    'gtmetrix_grade',\n", "    'gtmetrix_performance',\n", "    'gtmetrix_structure',\n", "    'lcp',\n", "    'tbt',\n", "    'cls',\n", "    'gtmetrix_url_target'\n", "]\n", "\n", "df_pomokit.drop(columns=kolom_gtmetrix, inplace=True)\n", "df_pomokit.isna().sum()\n"]}, {"cell_type": "code", "execution_count": 41, "id": "d9373d7a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_43988\\953923173.py:9: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df_pomokit[kolom] = df_pomokit.groupby('phonenumber')[kolom].transform(lambda x: x.ffill().bfill())\n"]}, {"data": {"text/plain": ["_id              0\n", "phonenumber      0\n", "cycle            0\n", "hostname         0\n", "ip               6\n", "screenshots      0\n", "pekerjaan        1\n", "token            0\n", "urlpe<PERSON><PERSON><PERSON>    36\n", "createdAt        0\n", "wagroupid        0\n", "name             0\n", "dtype: int64"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# <PERSON>lo<PERSON> yang ingin diisi jika kosong\n", "target_kolom = ['wagroupid', 'name', 'ip']\n", "\n", "# <PERSON><PERSON><PERSON>, ubah string kosong jadi NaN supaya mudah diisi\n", "df_pomokit[target_kolom] = df_pomokit[target_kolom].replace('', pd.NA)\n", "\n", "# Gunakan groupby + transform untuk mengisi nilai dari baris lain yang punya phonenumber sama\n", "for kolom in target_kolom:\n", "    df_pomokit[kolom] = df_pomokit.groupby('phonenumber')[kolom].transform(lambda x: x.ffill().bfill())\n", "\n", "df_pomokit.isna().sum()"]}, {"cell_type": "code", "execution_count": 42, "id": "6a98b1b3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>67d2b324679260dea62135c0</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>lan<PERSON><PERSON> ker<PERSON>an se<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/dashboard/#proyek/anggota</td>\n", "      <td>2025-03-13T10:27:48.663Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>67d46f7d4a403afebb3abba9</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>menambahkan file dan mengubah tampilan web</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/dashboard/#proyek/anggota</td>\n", "      <td>2025-03-14T18:03:41.344Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>67d5a9939da274d2a2c1a728</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> buku dasar-dasar fundamental</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/dashboard/#proyek/anggota</td>\n", "      <td>2025-03-15T16:23:47.501Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261</th>\n", "      <td>67d5e2bc783281544f580132</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>be<PERSON><PERSON> keras sampai tuntaas</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/mining/</td>\n", "      <td>2025-03-15T20:27:40.671Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>67d6c7d98638681f3f936dd6</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>lan<PERSON><PERSON> ker<PERSON> se<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...</td>\n", "      <td>https://raihanhidayat02.github.io/berproseshub/#</td>\n", "      <td>2025-03-16T12:45:13.287Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>317</th>\n", "      <td>67d7141f2a09e4a00b2acbe2</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>paksa sampai selesai</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...</td>\n", "      <td>https://raihanhidayat02.github.io/berproseshub/</td>\n", "      <td>2025-03-16T18:10:39.132Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          _id    phonenumber  cycle           hostname   ip  \\\n", "73   67d2b324679260dea62135c0  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "172  67d46f7d4a403afebb3abba9  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "250  67d5a9939da274d2a2c1a728  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "261  67d5e2bc783281544f580132  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "301  67d6c7d98638681f3f936dd6  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "317  67d7141f2a09e4a00b2acbe2  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "\n", "     screenshots                                   p<PERSON><PERSON><PERSON><PERSON>  \\\n", "73             4                 lan<PERSON><PERSON> ker<PERSON> se<PERSON>   \n", "172            4  menambahkan file dan mengubah tampilan web   \n", "250            4    men<PERSON><PERSON><PERSON> buku dasar-dasar fundamental   \n", "261            4                bekerja keras sampai tuntaas   \n", "301            4                    lan<PERSON><PERSON> ker<PERSON><PERSON> se<PERSON>   \n", "317            4                        paksa sampai selesai   \n", "\n", "                                                 token  \\\n", "73   v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "172  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "250  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "261  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "301  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...   \n", "317  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...   \n", "\n", "                                         url<PERSON><PERSON><PERSON>an  \\\n", "73     https://www.do.my.id/dashboard/#proyek/anggota   \n", "172    https://www.do.my.id/dashboard/#proyek/anggota   \n", "250    https://www.do.my.id/dashboard/#proyek/anggota   \n", "261                      https://www.do.my.id/mining/   \n", "301  https://raihanhidayat02.github.io/berproseshub/#   \n", "317   https://raihanhidayat02.github.io/berproseshub/   \n", "\n", "                    createdAt     wagroupid                name  \n", "73   2025-03-13T10:27:48.663Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "172  2025-03-14T18:03:41.344Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "250  2025-03-15T16:23:47.501Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "261  2025-03-15T20:27:40.671Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "301  2025-03-16T12:45:13.287Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "317  2025-03-16T18:10:39.132Z  1.203630e+17  M RAIHAN HIDAYAT A  "]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit[df_pomokit['ip'].isna()]"]}, {"cell_type": "code", "execution_count": 43, "id": "c17cf5ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>67d2b324679260dea62135c0</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>lan<PERSON><PERSON> ker<PERSON>an se<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/dashboard/#proyek/anggota</td>\n", "      <td>2025-03-13T10:27:48.663Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>67d46f7d4a403afebb3abba9</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>menambahkan file dan mengubah tampilan web</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/dashboard/#proyek/anggota</td>\n", "      <td>2025-03-14T18:03:41.344Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>67d5a9939da274d2a2c1a728</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> buku dasar-dasar fundamental</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/dashboard/#proyek/anggota</td>\n", "      <td>2025-03-15T16:23:47.501Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261</th>\n", "      <td>67d5e2bc783281544f580132</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>be<PERSON><PERSON> keras sampai tuntaas</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...</td>\n", "      <td>https://www.do.my.id/mining/</td>\n", "      <td>2025-03-15T20:27:40.671Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>67d6c7d98638681f3f936dd6</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>lan<PERSON><PERSON> ker<PERSON> se<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...</td>\n", "      <td>https://raihanhidayat02.github.io/berproseshub/#</td>\n", "      <td>2025-03-16T12:45:13.287Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>317</th>\n", "      <td>67d7141f2a09e4a00b2acbe2</td>\n", "      <td>6289604239732</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JJASI1M7IP</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>paksa sampai selesai</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...</td>\n", "      <td>https://raihanhidayat02.github.io/berproseshub/</td>\n", "      <td>2025-03-16T18:10:39.132Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td>M RAIHAN HIDAYAT A</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          _id    phonenumber  cycle           hostname   ip  \\\n", "73   67d2b324679260dea62135c0  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "172  67d46f7d4a403afebb3abba9  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "250  67d5a9939da274d2a2c1a728  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "261  67d5e2bc783281544f580132  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "301  67d6c7d98638681f3f936dd6  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "317  67d7141f2a09e4a00b2acbe2  6289604239732      1  LAPTOP-JJASI1M7IP  NaN   \n", "\n", "     screenshots                                   p<PERSON><PERSON><PERSON><PERSON>  \\\n", "73             4                 lan<PERSON><PERSON> ker<PERSON> se<PERSON>   \n", "172            4  menambahkan file dan mengubah tampilan web   \n", "250            4    men<PERSON><PERSON><PERSON> buku dasar-dasar fundamental   \n", "261            4                bekerja keras sampai tuntaas   \n", "301            4                    lan<PERSON><PERSON> ker<PERSON><PERSON> se<PERSON>   \n", "317            4                        paksa sampai selesai   \n", "\n", "                                                 token  \\\n", "73   v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "172  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "250  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "261  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vd3d3LmRvLm15...   \n", "301  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...   \n", "317  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcmFpaGFuaGlk...   \n", "\n", "                                         url<PERSON><PERSON><PERSON>an  \\\n", "73     https://www.do.my.id/dashboard/#proyek/anggota   \n", "172    https://www.do.my.id/dashboard/#proyek/anggota   \n", "250    https://www.do.my.id/dashboard/#proyek/anggota   \n", "261                      https://www.do.my.id/mining/   \n", "301  https://raihanhidayat02.github.io/berproseshub/#   \n", "317   https://raihanhidayat02.github.io/berproseshub/   \n", "\n", "                    createdAt     wagroupid                name  \n", "73   2025-03-13T10:27:48.663Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "172  2025-03-14T18:03:41.344Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "250  2025-03-15T16:23:47.501Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "261  2025-03-15T20:27:40.671Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "301  2025-03-16T12:45:13.287Z  1.203630e+17  M RAIHAN HIDAYAT A  \n", "317  2025-03-16T18:10:39.132Z  1.203630e+17  M RAIHAN HIDAYAT A  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit['phonenumber'] = df_pomokit['phonenumber'].astype(str).str.strip()\n", "result = df_pomokit[df_pomokit['phonenumber'] == '6289604239732']\n", "result"]}, {"cell_type": "code", "execution_count": 44, "id": "79ba88ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id              0\n", "phonenumber      0\n", "cycle            0\n", "hostname         0\n", "ip               0\n", "screenshots      0\n", "pekerjaan        1\n", "token            0\n", "urlpe<PERSON><PERSON><PERSON>    36\n", "createdAt        0\n", "wagroupid        0\n", "name             0\n", "dtype: int64"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit['ip'] = df_pomokit['ip'].fillna('unknown')\n", "df_pomokit.isna().sum()"]}, {"cell_type": "code", "execution_count": 45, "id": "dfe7e99b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>403</th>\n", "      <td>67db614f16958e5d563cda16</td>\n", "      <td>6289685587547</td>\n", "      <td>1</td>\n", "      <td>WIN-VM3JP598T2GIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vY29wZXRwYXNh...</td>\n", "      <td>https://copetpasarsenin.github.io/</td>\n", "      <td>2025-03-20T00:29:03.914Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          _id    phonenumber  cycle           hostname  \\\n", "403  67db614f16958e5d563cda16  6289685587547      1  WIN-VM3JP598T2GIP   \n", "\n", "                                                  ip  screenshots p<PERSON><PERSON><PERSON><PERSON>  \\\n", "403  https://whatismyipaddress.com/ip/**************            4       NaN   \n", "\n", "                                                 token  \\\n", "403  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vY29wZXRwYXNh...   \n", "\n", "                           url<PERSON><PERSON><PERSON><PERSON>  \\\n", "403  https://copetpasarsenin.github.io/  2025-03-20T00:29:03.914Z   \n", "\n", "        wagroupid                name  \n", "403  1.203630e+17  <PERSON>  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit[df_pomokit['pek<PERSON><PERSON><PERSON>'].isna()]"]}, {"cell_type": "code", "execution_count": 46, "id": "57eed3b0", "metadata": {}, "outputs": [], "source": ["def isi_p<PERSON><PERSON><PERSON><PERSON>(row):\n", "    if pd.isna(row['pekerjaan']) or row['pekerjaan'] == '':\n", "        ph = row['phonenumber']\n", "        url = row['url<PERSON><PERSON><PERSON><PERSON>']\n", "        # Cari baris lain yang phonenumber & urlpekerjaan sama dan pekerjaan tidak kosong\n", "        pekerjaan_baru = df_pomokit.loc[\n", "            (df_pomokit['phonenumber'] == ph) &\n", "            (df_pomokit['url<PERSON><PERSON><PERSON><PERSON>'] == url) &\n", "            (df_pomokit['pek<PERSON><PERSON><PERSON>'].notna()) &\n", "            (df_pomokit['peker<PERSON><PERSON>'] != ''),\n", "            'pek<PERSON><PERSON><PERSON>'\n", "        ]\n", "        if not peker<PERSON><PERSON>_baru.empty:\n", "            return pekerjaan_baru.iloc[0]\n", "    return row['pek<PERSON><PERSON><PERSON>']\n", "\n", "df_pomokit['pekerja<PERSON>'] = df_pomokit.apply(isi_pekerjaan, axis=1)"]}, {"cell_type": "code", "execution_count": 47, "id": "8d45946d", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id              0\n", "phonenumber      0\n", "cycle            0\n", "hostname         0\n", "ip               0\n", "screenshots      0\n", "pekerjaan        0\n", "token            0\n", "urlpe<PERSON><PERSON><PERSON>    36\n", "createdAt        0\n", "wagroupid        0\n", "name             0\n", "dtype: int64"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit.isna().sum()"]}, {"cell_type": "code", "execution_count": 48, "id": "2e9580cf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>67d3a7cc0b680d3634e16803</td>\n", "      <td>6285692322461</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-KM1M9QSIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> As<PERSON> k<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T03:51:40.617Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>dewi k<PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>67d3a8ea0b680d3634e1680d</td>\n", "      <td>6285759043680</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-KM1M9QSIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>mengerjakan assessment kelas kec<PERSON>n buatan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T03:56:26.118Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>67d3a8f70b680d3634e1680f</td>\n", "      <td>6285797042305</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-9O5CC3QIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Pretest kelas kecerdasan buatan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T03:56:39.975Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>Salwa Mutfia Indah Putri</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>67d3a9770b680d3634e16815</td>\n", "      <td>6283195800022</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-AMAG0RPIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> assesment kela<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T03:58:47.620Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>67d3a9810b680d3634e16817</td>\n", "      <td>62887435359524</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-4U3812LIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Pretest Kelas Kecerdasan Buatan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T03:58:57.952Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td>67d3a9d30b680d3634e1681a</td>\n", "      <td>6285159577009</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-BKOR3064IP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> assesment kelas kec<PERSON>asan buatan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T04:00:19.417Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>balqisrosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>67d3aae20b680d3634e16832</td>\n", "      <td>628814570820</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-KM1M9QSIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T04:04:50.667Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>67d3ae590b680d3634e16856</td>\n", "      <td>6285921240822</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-90OLVN26IP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>pre-test kelas kec<PERSON>n buatan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T04:19:37.429Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>67d3b6360b680d3634e16892</td>\n", "      <td>6285922024007</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-KM1M9QSIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>3</td>\n", "      <td>Mengerjakan Assessment k<PERSON><PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T04:53:10.383Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>Даитои</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>67d3b8f60b680d3634e1689b</td>\n", "      <td>6285894383455</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-FU7I6BFIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON>jakan Pre Assesment AI Kelas 3A</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T05:04:54.218Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>UKASYAH ABDULLOH AZZAM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>67d437854fb3aa876bdf08c8</td>\n", "      <td>6281312844675</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-JL28JS49IP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>mining dan edit web</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly95b25hbGRpZXAu...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-14T14:04:53.339Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>278</th>\n", "      <td>67d686ce6977718cf3386564</td>\n", "      <td>6285299899161</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-TQ0G8P38IP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:303...</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> assesment kelas kec<PERSON>asan buatan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-16T08:07:42.635Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>67d729ff44d02104364155e7</td>\n", "      <td>6285320634667</td>\n", "      <td>1</td>\n", "      <td>MSIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> pra assement pemograman 3</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-16T19:43:59.333Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>re<PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>446</th>\n", "      <td>67dcddb4bd3463c7ecf4bb63</td>\n", "      <td>62895331942686</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-N0KNCOVIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON> Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T03:32:04.418Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>Gai<PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>458</th>\n", "      <td>67dcec16bd3463c7ecf4bb88</td>\n", "      <td>6285159577009</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-BKOR3064IP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Belajar Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T04:33:26.617Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>balqisrosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>461</th>\n", "      <td>67dcecb9bd3463c7ecf4bb90</td>\n", "      <td>6285759043680</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-5TTIDSNIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Belajar tentang Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T04:36:09.717Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>462</th>\n", "      <td>67dcef0fbd3463c7ecf4bba2</td>\n", "      <td>628814570820</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-ICGAORLIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>Belajar tentang Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T04:46:07.314Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>463</th>\n", "      <td>67dcf265bd3463c7ecf4bba5</td>\n", "      <td>62887435359524</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-4U3812LIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON>i Artifial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T05:00:21.480Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>464</th>\n", "      <td>67dcf42fbd3463c7ecf4bba8</td>\n", "      <td>6285797042305</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-9O5CC3QIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Belajar Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T05:07:59.716Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>Sal<PERSON> Putri</td>\n", "    </tr>\n", "    <tr>\n", "      <th>465</th>\n", "      <td>67dcf90bfc8a721640386116</td>\n", "      <td>6285692322461</td>\n", "      <td>1</td>\n", "      <td>DewiKresnaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td>Belajar tentang Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T05:28:43.998Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>d</td>\n", "    </tr>\n", "    <tr>\n", "      <th>468</th>\n", "      <td>67dd17dfd056ed84ecea51f7</td>\n", "      <td>6283195800022</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-AMAG0RPIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:303...</td>\n", "      <td>4</td>\n", "      <td>Belajar tentang Artificial Intelligence</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T07:40:15.784Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>67dd364d20d6b83adbb62751</td>\n", "      <td>6287749808936</td>\n", "      <td>1</td>\n", "      <td>acerIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Membuat kartu nama</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T09:50:05.991Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>67dd367a20d6b83adbb62753</td>\n", "      <td>6287771339356</td>\n", "      <td>1</td>\n", "      <td>Sam-LOQIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>pembelajaran pemograman 3 (web service) 21-03-25</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T09:50:50.986Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>Sam</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td>67dd36bd20d6b83adbb62755</td>\n", "      <td>6285863936544</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-FTTSPJPDIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Membuat kartu nama digital</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T09:51:57.684Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>67dd386520d6b83adbb62762</td>\n", "      <td>6282117676005</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-ILC7MNSIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Membuat kartu nama digital</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T09:59:01.590Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td>67dd38682b07a081a32f23bf</td>\n", "      <td>6282116725971</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-V8KVJCRIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Membuat kartu nama digital</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T09:59:04.193Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>478</th>\n", "      <td>67dd388420d6b83adbb62764</td>\n", "      <td>6288222353212</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-TCH674O9IP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Membuat kartu nama digital</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T09:59:32.984Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>484</th>\n", "      <td>67dd780309b9c8e122e2e49c</td>\n", "      <td>62895365662586</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-5JK27CAIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>8</td>\n", "      <td>Mengulangi Aktifitas Pomodoro Pertemuan minggu 2</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T14:30:27.029Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>489</th>\n", "      <td>67dd8ece8979d9fad52943c1</td>\n", "      <td>6285320634667</td>\n", "      <td>1</td>\n", "      <td>MSIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>membuat kartu digital pertemuan 2</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T16:07:42.335Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>re<PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>490</th>\n", "      <td>67dda7cfe74ee4593e5c7c3f</td>\n", "      <td>6285694749240</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-16UGM1RIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>Mengulangi aktifitas minggu kedua pomokit</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-21T17:54:23.941Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>ne<PERSON>a salma</td>\n", "    </tr>\n", "    <tr>\n", "      <th>536</th>\n", "      <td>67e8d43463de9c42d3772f1b</td>\n", "      <td>6282245870175</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-SD019HBDIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:511...</td>\n", "      <td>4</td>\n", "      <td>cek cek website, belajar aja</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly93d3cuZG8ubXku...</td>\n", "      <td>NaN</td>\n", "      <td>2025-03-30T05:18:44.080Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>543</th>\n", "      <td>67f5f2f77b6af735065da4f2</td>\n", "      <td>6282245870175</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-SD019HBDIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>membuat tugas yang di<PERSON></td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly93d3cuZG8ubXku...</td>\n", "      <td>NaN</td>\n", "      <td>2025-04-09T04:09:27.791Z</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>556</th>\n", "      <td>68137921dcacf294623d16e6</td>\n", "      <td>6282116301579</td>\n", "      <td>1</td>\n", "      <td>V<PERSON>a_AlmirahIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:303...</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> tugas bimb<PERSON>n</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90LmlmLmNvLmlk...</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-01T13:37:37.590Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>562</th>\n", "      <td>681881f7a1c8ad546129f879</td>\n", "      <td>6282116301579</td>\n", "      <td>1</td>\n", "      <td>V<PERSON>a_AlmirahIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:303...</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON>an bimbingan mingguan</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90LmlmLmNvLmlk...</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-05T09:16:39.115Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>604</th>\n", "      <td>681ca9ffe7a1213faa1e44c0</td>\n", "      <td>6287771339356</td>\n", "      <td>1</td>\n", "      <td>Sam-LOQIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td>Membuat tutorial youtube dan Medium</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-08T12:56:31.208Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>675</th>\n", "      <td>681f1d115f1d7ca77294e6a0</td>\n", "      <td>6285159577009</td>\n", "      <td>1</td>\n", "      <td>LAPTOP-BKOR3064IP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Video Tutorial YouTube</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHA6Ly9ndG1ldHJpeC5j...</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-10T09:32:01.668Z</td>\n", "      <td>1.203633e+17</td>\n", "      <td>balqisrosa</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          _id     phonenumber  cycle           hostname  \\\n", "115  67d3a7cc0b680d3634e16803   6285692322461      1  DESKTOP-KM1M9QSIP   \n", "118  67d3a8ea0b680d3634e1680d   6285759043680      1  DESKTOP-KM1M9QSIP   \n", "119  67d3a8f70b680d3634e1680f   6285797042305      1  DESKTOP-9O5CC3QIP   \n", "120  67d3a9770b680d3634e16815   6283195800022      1  DESKTOP-AMAG0RPIP   \n", "121  67d3a9810b680d3634e16817  62887435359524      1  DESKTOP-4U3812LIP   \n", "122  67d3a9d30b680d3634e1681a   6285159577009      1  LAPTOP-BKOR3064IP   \n", "123  67d3aae20b680d3634e16832    628814570820      1  DESKTOP-KM1M9QSIP   \n", "126  67d3ae590b680d3634e16856   6285921240822      1  LAPTOP-90OLVN26IP   \n", "129  67d3b6360b680d3634e16892   6285922024007      1  DESKTOP-KM1M9QSIP   \n", "130  67d3b8f60b680d3634e1689b   6285894383455      1  DESKTOP-FU7I6BFIP   \n", "151  67d437854fb3aa876bdf08c8   6281312844675      1  LAPTOP-JL28JS49IP   \n", "278  67d686ce6977718cf3386564   6285299899161      1  LAPTOP-TQ0G8P38IP   \n", "322  67d729ff44d02104364155e7   6285320634667      1              MSIIP   \n", "446  67dcddb4bd3463c7ecf4bb63  62895331942686      1  DESKTOP-N0KNCOVIP   \n", "458  67dcec16bd3463c7ecf4bb88   6285159577009      1  LAPTOP-BKOR3064IP   \n", "461  67dcecb9bd3463c7ecf4bb90   6285759043680      1  DESKTOP-5TTIDSNIP   \n", "462  67dcef0fbd3463c7ecf4bba2    628814570820      1  DESKTOP-ICGAORLIP   \n", "463  67dcf265bd3463c7ecf4bba5  62887435359524      1  DESKTOP-4U3812LIP   \n", "464  67dcf42fbd3463c7ecf4bba8   6285797042305      1  DESKTOP-9O5CC3QIP   \n", "465  67dcf90bfc8a721640386116   6285692322461      1       DewiKresnaIP   \n", "468  67dd17dfd056ed84ecea51f7   6283195800022      1  DESKTOP-AMAG0RPIP   \n", "469  67dd364d20d6b83adbb62751   6287749808936      1             acerIP   \n", "471  67dd367a20d6b83adbb62753   6287771339356      1          Sam-LOQIP   \n", "472  67dd36bd20d6b83adbb62755   6285863936544      1  LAPTOP-FTTSPJPDIP   \n", "476  67dd386520d6b83adbb62762   6282117676005      1  DESKTOP-ILC7MNSIP   \n", "477  67dd38682b07a081a32f23bf   6282116725971      1  DESKTOP-V8KVJCRIP   \n", "478  67dd388420d6b83adbb62764   6288222353212      1  LAPTOP-TCH674O9IP   \n", "484  67dd780309b9c8e122e2e49c  62895365662586      1  DESKTOP-5JK27CAIP   \n", "489  67dd8ece8979d9fad52943c1   6285320634667      1              MSIIP   \n", "490  67dda7cfe74ee4593e5c7c3f   6285694749240      1  DESKTOP-16UGM1RIP   \n", "536  67e8d43463de9c42d3772f1b   6282245870175      1  LAPTOP-SD019HBDIP   \n", "543  67f5f2f77b6af735065da4f2   6282245870175      1  LAPTOP-SD019HBDIP   \n", "556  68137921dcacf294623d16e6   6282116301579      1    Virna_AlmirahIP   \n", "562  681881f7a1c8ad546129f879   6282116301579      1    Virna_AlmirahIP   \n", "604  681ca9ffe7a1213faa1e44c0   6287771339356      1          Sam-LOQIP   \n", "675  681f1d115f1d7ca77294e6a0   6285159577009      1  LAPTOP-BKOR3064IP   \n", "\n", "                                                    ip  screenshots  \\\n", "115   https://whatismyipaddress.com/ip/***************            4   \n", "118   https://whatismyipaddress.com/ip/***************            4   \n", "119   https://whatismyipaddress.com/ip/***************            4   \n", "120   https://whatismyipaddress.com/ip/***************            4   \n", "121   https://whatismyipaddress.com/ip/***************            4   \n", "122   https://whatismyipaddress.com/ip/***************            4   \n", "123   https://whatismyipaddress.com/ip/***************            4   \n", "126   https://whatismyipaddress.com/ip/***************            4   \n", "129   https://whatismyipaddress.com/ip/***************            3   \n", "130   https://whatismyipaddress.com/ip/***************            4   \n", "151     https://whatismyipaddress.com/ip/*************            4   \n", "278  https://whatismyipaddress.com/ip/2001:448a:303...            4   \n", "322   https://whatismyipaddress.com/ip/***************            4   \n", "446   https://whatismyipaddress.com/ip/***************            4   \n", "458   https://whatismyipaddress.com/ip/***************            4   \n", "461   https://whatismyipaddress.com/ip/***************            4   \n", "462     https://whatismyipaddress.com/ip/*************            4   \n", "463     https://whatismyipaddress.com/ip/*************            4   \n", "464   https://whatismyipaddress.com/ip/***************            4   \n", "465    https://whatismyipaddress.com/ip/**************            4   \n", "468  https://whatismyipaddress.com/ip/2001:448a:303...            4   \n", "469   https://whatismyipaddress.com/ip/***************            4   \n", "471   https://whatismyipaddress.com/ip/***************            4   \n", "472   https://whatismyipaddress.com/ip/***************            4   \n", "476   https://whatismyipaddress.com/ip/***************            4   \n", "477   https://whatismyipaddress.com/ip/***************            4   \n", "478   https://whatismyipaddress.com/ip/***************            4   \n", "484     https://whatismyipaddress.com/ip/*************            8   \n", "489   https://whatismyipaddress.com/ip/***************            4   \n", "490   https://whatismyipaddress.com/ip/***************            4   \n", "536  https://whatismyipaddress.com/ip/2001:448a:511...            4   \n", "543   https://whatismyipaddress.com/ip/***************            4   \n", "556  https://whatismyipaddress.com/ip/2001:448a:303...            4   \n", "562  https://whatismyipaddress.com/ip/2001:448a:303...            4   \n", "604    https://whatismyipaddress.com/ip/**************            4   \n", "675    https://whatismyipaddress.com/ip/**************            4   \n", "\n", "                                            p<PERSON><PERSON><PERSON><PERSON>  \\\n", "115      Megerjakan Assesment k<PERSON><PERSON> Buatan   \n", "118    mengerjakan assessment kelas kec<PERSON>n buatan   \n", "119       <PERSON><PERSON><PERSON>an Pretest kelas kecerdasan buatan   \n", "120     Mengerjakan assesment kelas <PERSON> Buatan   \n", "121       Mengerjakan Pretest Kelas Kecerdasan Buatan   \n", "122     mengerjakan assesment kelas kecerdasan buatan   \n", "123       <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Buatan   \n", "126                  pre-test kelas kec<PERSON>n buatan   \n", "129    Mengerjakan <PERSON> k<PERSON><PERSON>   \n", "130             Mengerjakan Pre Assesment AI Kelas 3A   \n", "151                               mining dan edit web   \n", "278     <PERSON><PERSON><PERSON>an assesment kelas kec<PERSON>n buatan   \n", "322             mengerjakan pra assement pemograman 3   \n", "446           <PERSON><PERSON><PERSON>i Artificial Intelligence   \n", "458                   Belajar Artificial Intelligence   \n", "461           Belajar tentang Artificial Intelligence   \n", "462           Belajar tentang Artificial Intelligence   \n", "463             <PERSON><PERSON><PERSON>i Artifial Intelligence   \n", "464                   Belajar Artificial Intelligence   \n", "465           Belajar tentang Artificial Intelligence   \n", "468           Belajar tentang Artificial Intelligence   \n", "469                                Membuat kartu nama   \n", "471  pembelajaran pemograman 3 (web service) 21-03-25   \n", "472                        Membuat kartu nama digital   \n", "476                        Membuat kartu nama digital   \n", "477                        Membuat kartu nama digital   \n", "478                        Membuat kartu nama digital   \n", "484  Mengulangi Aktifitas Pomodoro Pertemuan minggu 2   \n", "489                 membuat kartu digital pertemuan 2   \n", "490         Mengulangi aktifitas minggu kedua pomokit   \n", "536                      cek cek website, belajar aja   \n", "543                     membuat tugas yang di<PERSON>an   \n", "556                       mengerjakan tugas bimbingan   \n", "562                    mengerjakan bimbingan mingguan   \n", "604               Membuat tutorial youtube dan Medium   \n", "675          <PERSON><PERSON><PERSON><PERSON> Video Tutorial YouTube   \n", "\n", "                                                 token urlpekerjaan  \\\n", "115  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "118  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "119  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "120  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "121  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "122  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "123  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "126  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "129  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "130  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "151  v4.public.eyJhbGlhcyI6Imh0dHA6Ly95b25hbGRpZXAu...          NaN   \n", "278  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "322  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "446  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "458  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "461  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "462  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "463  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "464  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "465  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "468  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "469  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "471  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "472  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "476  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "477  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "478  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "484  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "489  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "490  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "536  v4.public.eyJhbGlhcyI6Imh0dHA6Ly93d3cuZG8ubXku...          NaN   \n", "543  v4.public.eyJhbGlhcyI6Imh0dHA6Ly93d3cuZG8ubXku...          NaN   \n", "556  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90LmlmLmNvLmlk...          NaN   \n", "562  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90LmlmLmNvLmlk...          NaN   \n", "604  v4.public.eyJhbGlhcyI6Imh0dHA6Ly90ZWFtLWluZm9y...          NaN   \n", "675  v4.public.eyJhbGlhcyI6Imh0dHA6Ly9ndG1ldHJpeC5j...          NaN   \n", "\n", "                    createdAt     wagroupid                       name  \n", "115  2025-03-14T03:51:40.617Z  1.203633e+17                dewi kresna  \n", "118  2025-03-14T03:56:26.118Z  1.203633e+17    <PERSON><PERSON><PERSON>  \n", "119  2025-03-14T03:56:39.975Z  1.203633e+17   Salwa Mutfia Indah Putri  \n", "120  2025-03-14T03:58:47.620Z  1.203633e+17              <PERSON><PERSON>  \n", "121  2025-03-14T03:58:57.952Z  1.203633e+17    <PERSON><PERSON><PERSON><PERSON> Nasywa Andini  \n", "122  2025-03-14T04:00:19.417Z  1.203633e+17                 balqisrosa  \n", "123  2025-03-14T04:04:50.667Z  1.203633e+17                <PERSON><PERSON>  \n", "126  2025-03-14T04:19:37.429Z  1.203633e+17               Nayaka Taqwa  \n", "129  2025-03-14T04:53:10.383Z  1.203633e+17                     Даитои  \n", "130  2025-03-14T05:04:54.218Z  1.203633e+17     UKASYAH ABDULLOH AZZAM  \n", "151  2025-03-14T14:04:53.339Z  1.203630e+17      <PERSON><PERSON><PERSON>  \n", "278  2025-03-16T08:07:42.635Z  1.203633e+17               <PERSON><PERSON>  \n", "322  2025-03-16T19:43:59.333Z  1.203633e+17           re<PERSON> ka<PERSON><PERSON>  \n", "446  2025-03-21T03:32:04.418Z  1.203633e+17       Gaiz<PERSON> <PERSON><PERSON><PERSON>  \n", "458  2025-03-21T04:33:26.617Z  1.203633e+17                 balqisrosa  \n", "461  2025-03-21T04:36:09.717Z  1.203633e+17    <PERSON><PERSON><PERSON>  \n", "462  2025-03-21T04:46:07.314Z  1.203633e+17                <PERSON><PERSON>  \n", "463  2025-03-21T05:00:21.480Z  1.203633e+17    <PERSON><PERSON><PERSON><PERSON> Nasywa Andini  \n", "464  2025-03-21T05:07:59.716Z  1.203633e+17                Salwa Putri  \n", "465  2025-03-21T05:28:43.998Z  1.203633e+17                          d  \n", "468  2025-03-21T07:40:15.784Z  1.203633e+17              <PERSON><PERSON>  \n", "469  2025-03-21T09:50:05.991Z  1.203633e+17     <PERSON><PERSON><PERSON><PERSON><PERSON>  \n", "471  2025-03-21T09:50:50.986Z  1.203633e+17                        Sam  \n", "472  2025-03-21T09:51:57.684Z  1.203633e+17               <PERSON><PERSON>  \n", "476  2025-03-21T09:59:01.590Z  1.203633e+17      <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>  \n", "477  2025-03-21T09:59:04.193Z  1.203633e+17       <PERSON>  \n", "478  2025-03-21T09:59:32.984Z  1.203633e+17        <PERSON><PERSON>  \n", "484  2025-03-21T14:30:27.029Z  1.203633e+17     <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  \n", "489  2025-03-21T16:07:42.335Z  1.203633e+17           re<PERSON> <PERSON><PERSON><PERSON>  \n", "490  2025-03-21T17:54:23.941Z  1.203633e+17                ne<PERSON>a salma  \n", "536  2025-03-30T05:18:44.080Z  1.203630e+17       <PERSON><PERSON>  \n", "543  2025-04-09T04:09:27.791Z  1.203630e+17       <PERSON><PERSON>  \n", "556  2025-05-01T13:37:37.590Z  1.203633e+17                      <PERSON><PERSON><PERSON>  \n", "562  2025-05-05T09:16:39.115Z  1.203633e+17                      <PERSON><PERSON><PERSON>  \n", "604  2025-05-08T12:56:31.208Z  1.203633e+17  <PERSON><PERSON> <PERSON><PERSON><PERSON>  \n", "675  2025-05-10T09:32:01.668Z  1.203633e+17                 balqisrosa  "]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit[df_pomokit['url<PERSON><PERSON><PERSON><PERSON>'].isna()]"]}, {"cell_type": "code", "execution_count": 49, "id": "e0d348a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id             0\n", "phonenumber     0\n", "cycle           0\n", "hostname        0\n", "ip              0\n", "screenshots     0\n", "pekerjaan       0\n", "token           0\n", "urlpekerjaan    0\n", "createdAt       0\n", "wagroupid       0\n", "name            0\n", "dtype: int64"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["# <PERSON>lo<PERSON> yang ingin diisi jika kosong\n", "target_kolom = ['url<PERSON><PERSON><PERSON><PERSON>']\n", "\n", "# <PERSON><PERSON><PERSON>, ubah string kosong jadi NaN supaya mudah diisi\n", "df_pomokit[target_kolom] = df_pomokit[target_kolom].replace('', pd.NA)\n", "\n", "# Gunakan groupby + transform untuk mengisi nilai dari baris lain yang punya phonenumber sama\n", "for kolom in target_kolom:\n", "    df_pomokit[kolom] = df_pomokit.groupby('phonenumber')[kolom].transform(lambda x: x.ffill().bfill())\n", "\n", "df_pomokit.isna().sum()"]}, {"cell_type": "code", "execution_count": 50, "id": "72d44cb2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "      <th>date_time_wib</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67d03bd5b249b1388ac7c61d</td>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>Laptop-mahalIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>edit content web portofolio</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIyOjMwOjE0...</td>\n", "      <td>https://venalism.github.io/</td>\n", "      <td>2025-03-11 13:34:13.688000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td>Nadi</td>\n", "      <td>2025-03-11 20:34:13.688000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67d04b7855dfbb2c3f9d9265</td>\n", "      <td>6283137760847</td>\n", "      <td>1</td>\n", "      <td>SyalwalyrhIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>mining, buat website, sambungin webhook, dll</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjIxOjUx...</td>\n", "      <td>https://hp-pinjam.github.io/proposal-hp-pinjam/</td>\n", "      <td>2025-03-11 14:40:56.585000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2025-03-11 21:40:56.585000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67d04f9355dfbb2c3f9d926c</td>\n", "      <td>6282116301579</td>\n", "      <td>1</td>\n", "      <td>V<PERSON>a_AlmirahIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:303...</td>\n", "      <td>4</td>\n", "      <td>buat website, mining, materi buku,</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjQ1OjI3...</td>\n", "      <td>https://hp-pinjam.github.io/proposal-hp-pinjam/</td>\n", "      <td>2025-03-11 14:58:27.187000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td>6282116301579</td>\n", "      <td>2025-03-11 21:58:27.187000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67d0505a55dfbb2c3f9d926f</td>\n", "      <td>6285722341788</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-EME1OPIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td>membuat website dan maining</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjU2OjE2...</td>\n", "      <td>https://fauzinrfdlh10.github.io/</td>\n", "      <td>2025-03-11 15:01:46.410000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td>Mo<PERSON>mmad Fauzi NF</td>\n", "      <td>2025-03-11 22:01:46.410000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d052f755dfbb2c3f9d9274</td>\n", "      <td>6285158354600</td>\n", "      <td>1</td>\n", "      <td>SahaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>3</td>\n", "      <td>memperbaiki tampilan web</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTEyVDAwOjA5OjUw...</td>\n", "      <td>https://saha-hub123.github.io/Isa.github.io/</td>\n", "      <td>2025-03-11 15:12:55.597000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON>isa</td>\n", "      <td>2025-03-11 22:12:55.597000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1233</th>\n", "      <td>68415d202a714c784c3741e7</td>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>NOGIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:8000:102...</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> tugas dan melanjutkan tugas</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcHJveWVrMXdl...</td>\n", "      <td>https://proyek1webs.github.io/BananaChip/</td>\n", "      <td>2025-06-05 09:02:24.748000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON></td>\n", "      <td>2025-06-05 16:02:24.748000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1234</th>\n", "      <td>6841846cbb465f4e88bad561</td>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>Laptop-mahalIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2402:5680:932...</td>\n", "      <td>4</td>\n", "      <td>weekly progress report</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/raoz-kitchen-test...</td>\n", "      <td>2025-06-05 11:50:04.240000+00:00</td>\n", "      <td>1.203634e+17</td>\n", "      <td>Nadi</td>\n", "      <td>2025-06-05 18:50:04.240000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1235</th>\n", "      <td>6841a6c1251c46cfa1da0c65</td>\n", "      <td>6289685587547</td>\n", "      <td>1</td>\n", "      <td>WIN-VM3JP598T2GIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>lanjut</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/laundryu9.shop/uT...</td>\n", "      <td>2025-06-05 14:16:33.438000+00:00</td>\n", "      <td>1.203634e+17</td>\n", "      <td>richard</td>\n", "      <td>2025-06-05 21:16:33.438000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1236</th>\n", "      <td>6841aade251c46cfa1da0c6a</td>\n", "      <td>6282258120851</td>\n", "      <td>1</td>\n", "      <td>lifyaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:c0:2d10:...</td>\n", "      <td>4</td>\n", "      <td>mengecek gtmetrix lagi</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/alifyazz.github.i...</td>\n", "      <td>2025-06-05 14:34:06.848000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2025-06-05 21:34:06.848000+07:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1237</th>\n", "      <td>6841f694ca2e662ccd018e55</td>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>NOGIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:8000:102...</td>\n", "      <td>4</td>\n", "      <td>lanjut</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/nogi3201.github.i...</td>\n", "      <td>2025-06-05 19:57:08.882000+00:00</td>\n", "      <td>1.203634e+17</td>\n", "      <td><PERSON></td>\n", "      <td>2025-06-06 02:57:08.882000+07:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1238 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                           _id    phonenumber  cycle           hostname  \\\n", "0     67d03bd5b249b1388ac7c61d  6288970788847      1     Laptop-mahalIP   \n", "1     67d04b7855dfbb2c3f9d9265  6283137760847      1       SyalwalyrhIP   \n", "2     67d04f9355dfbb2c3f9d926c  6282116301579      1    Virna_AlmirahIP   \n", "3     67d0505a55dfbb2c3f9d926f  6285722341788      1  DESKTOP-EME1OPIIP   \n", "4     67d052f755dfbb2c3f9d9274  6285158354600      1             SahaIP   \n", "...                        ...            ...    ...                ...   \n", "1233  68415d202a714c784c3741e7  6282161299141      1             NOGIIP   \n", "1234  6841846cbb465f4e88bad561  6288970788847      1     Laptop-mahalIP   \n", "1235  6841a6c1251c46cfa1da0c65  6289685587547      1  WIN-VM3JP598T2GIP   \n", "1236  6841aade251c46cfa1da0c6a  6282258120851      1            lifyaIP   \n", "1237  6841f694ca2e662ccd018e55  6282161299141      1             NOGIIP   \n", "\n", "                                                     ip  screenshots  \\\n", "0      https://whatismyipaddress.com/ip/***************            4   \n", "1        https://whatismyipaddress.com/ip/*************            4   \n", "2     https://whatismyipaddress.com/ip/2001:448a:303...            4   \n", "3       https://whatismyipaddress.com/ip/**************            4   \n", "4       https://whatismyipaddress.com/ip/**************            3   \n", "...                                                 ...          ...   \n", "1233  https://whatismyipaddress.com/ip/2404:8000:102...            4   \n", "1234  https://whatismyipaddress.com/ip/2402:5680:932...            4   \n", "1235     https://whatismyipaddress.com/ip/*************            4   \n", "1236  https://whatismyipaddress.com/ip/2404:c0:2d10:...            4   \n", "1237  https://whatismyipaddress.com/ip/2404:8000:102...            4   \n", "\n", "                                         p<PERSON><PERSON><PERSON><PERSON>  \\\n", "0                      edit content web portofolio   \n", "1     mining, buat website, sambungin webhook, dll   \n", "2               buat website, mining, materi buku,   \n", "3                      membuat website dan maining   \n", "4                         memperbaiki tampilan web   \n", "...                                            ...   \n", "1233       mengerjakan tugas dan melanjutkan tugas   \n", "1234                        weekly progress report   \n", "1235                                        lanjut   \n", "1236                        mengecek gtmetrix lagi   \n", "1237                                        lanjut   \n", "\n", "                                                  token  \\\n", "0     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIyOjMwOjE0...   \n", "1     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjIxOjUx...   \n", "2     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjQ1OjI3...   \n", "3     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjU2OjE2...   \n", "4     v4.public.eyJleHAiOiIyMDI1LTAzLTEyVDAwOjA5OjUw...   \n", "...                                                 ...   \n", "1233  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcHJveWVrMXdl...   \n", "1234  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1235  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1236  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1237  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "\n", "                                           url<PERSON><PERSON><PERSON>an  \\\n", "0                           https://venalism.github.io/   \n", "1       https://hp-pinjam.github.io/proposal-hp-pinjam/   \n", "2       https://hp-pinjam.github.io/proposal-hp-pinjam/   \n", "3                      https://fauzinrfdlh10.github.io/   \n", "4          https://saha-hub123.github.io/Isa.github.io/   \n", "...                                                 ...   \n", "1233          https://proyek1webs.github.io/BananaChip/   \n", "1234  https://gtmetrix.com/reports/raoz-kitchen-test...   \n", "1235  https://gtmetrix.com/reports/laundryu9.shop/uT...   \n", "1236  https://gtmetrix.com/reports/alifyazz.github.i...   \n", "1237  https://gtmetrix.com/reports/nogi3201.github.i...   \n", "\n", "                            createdAt     wagroupid                    name  \\\n", "0    2025-03-11 13:34:13.688000+00:00  1.203630e+17                    Nadi   \n", "1    2025-03-11 14:40:56.585000+00:00  1.203630e+17  <PERSON><PERSON><PERSON> <PERSON><PERSON>   \n", "2    2025-03-11 14:58:27.187000+00:00  1.203630e+17           6282116301579   \n", "3    2025-03-11 15:01:46.410000+00:00  1.203630e+17      <PERSON><PERSON><PERSON><PERSON> Fauzi NF   \n", "4    2025-03-11 15:12:55.597000+00:00  1.203630e+17                   M.isa   \n", "...                               ...           ...                     ...   \n", "1233 2025-06-05 09:02:24.748000+00:00  1.203630e+17                   Hasan   \n", "1234 2025-06-05 11:50:04.240000+00:00  1.203634e+17                    Nadi   \n", "1235 2025-06-05 14:16:33.438000+00:00  1.203634e+17                 richard   \n", "1236 2025-06-05 14:34:06.848000+00:00  1.203630e+17                  <PERSON><PERSON><PERSON>   \n", "1237 2025-06-05 19:57:08.882000+00:00  1.203634e+17                   Hasan   \n", "\n", "                        date_time_wib  \n", "0    2025-03-11 20:34:13.688000+07:00  \n", "1    2025-03-11 21:40:56.585000+07:00  \n", "2    2025-03-11 21:58:27.187000+07:00  \n", "3    2025-03-11 22:01:46.410000+07:00  \n", "4    2025-03-11 22:12:55.597000+07:00  \n", "...                               ...  \n", "1233 2025-06-05 16:02:24.748000+07:00  \n", "1234 2025-06-05 18:50:04.240000+07:00  \n", "1235 2025-06-05 21:16:33.438000+07:00  \n", "1236 2025-06-05 21:34:06.848000+07:00  \n", "1237 2025-06-06 02:57:08.882000+07:00  \n", "\n", "[1238 rows x 13 columns]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["# Pastikan kolom datetime sudah bertipe datetime dengan timezone\n", "df_pomokit['createdAt'] = pd.to_datetime(df_pomokit['createdAt'], utc=True)\n", "\n", "# Konversi ke WIB (UTC+7)\n", "df_pomokit['date_time_wib'] = df_pomokit['createdAt'].dt.tz_convert('Asia/Jakarta')\n", "\n", "df_pomokit"]}, {"cell_type": "code", "execution_count": 51, "id": "5046209c", "metadata": {}, "outputs": [], "source": ["anchor_tuesday = pd.to_datetime('2025-03-11')\n", "df_pomokit['week_custom'] = (\n", "    (df_pomokit['date_time_wib'].dt.tz_convert(None) - anchor_tuesday).dt.days // 7 + 1\n", ")\n", "\n", "df_pomokit['year_week'] = (\n", "    anchor_tuesday.strftime('%Y') + '-W' + df_pomokit['week_custom'].astype(str)\n", ")"]}, {"cell_type": "code", "execution_count": 52, "id": "3d7a4550", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>hostname</th>\n", "      <th>ip</th>\n", "      <th>screenshots</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>token</th>\n", "      <th>u<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>createdAt</th>\n", "      <th>wa<PERSON>id</th>\n", "      <th>name</th>\n", "      <th>date_time_wib</th>\n", "      <th>week_custom</th>\n", "      <th>year_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67d03bd5b249b1388ac7c61d</td>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>Laptop-mahalIP</td>\n", "      <td>https://whatismyipaddress.com/ip/***************</td>\n", "      <td>4</td>\n", "      <td>edit content web portofolio</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIyOjMwOjE0...</td>\n", "      <td>https://venalism.github.io/</td>\n", "      <td>2025-03-11 13:34:13.688000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td>Nadi</td>\n", "      <td>2025-03-11 20:34:13.688000+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67d04b7855dfbb2c3f9d9265</td>\n", "      <td>6283137760847</td>\n", "      <td>1</td>\n", "      <td>SyalwalyrhIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>mining, buat website, sambungin webhook, dll</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjIxOjUx...</td>\n", "      <td>https://hp-pinjam.github.io/proposal-hp-pinjam/</td>\n", "      <td>2025-03-11 14:40:56.585000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2025-03-11 21:40:56.585000+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67d04f9355dfbb2c3f9d926c</td>\n", "      <td>6282116301579</td>\n", "      <td>1</td>\n", "      <td>V<PERSON>a_AlmirahIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2001:448a:303...</td>\n", "      <td>4</td>\n", "      <td>buat website, mining, materi buku,</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjQ1OjI3...</td>\n", "      <td>https://hp-pinjam.github.io/proposal-hp-pinjam/</td>\n", "      <td>2025-03-11 14:58:27.187000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td>6282116301579</td>\n", "      <td>2025-03-11 21:58:27.187000+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67d0505a55dfbb2c3f9d926f</td>\n", "      <td>6285722341788</td>\n", "      <td>1</td>\n", "      <td>DESKTOP-EME1OPIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>4</td>\n", "      <td>membuat website dan maining</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjU2OjE2...</td>\n", "      <td>https://fauzinrfdlh10.github.io/</td>\n", "      <td>2025-03-11 15:01:46.410000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td>Mo<PERSON>mmad Fauzi NF</td>\n", "      <td>2025-03-11 22:01:46.410000+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67d052f755dfbb2c3f9d9274</td>\n", "      <td>6285158354600</td>\n", "      <td>1</td>\n", "      <td>SahaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/**************</td>\n", "      <td>3</td>\n", "      <td>memperbaiki tampilan web</td>\n", "      <td>v4.public.eyJleHAiOiIyMDI1LTAzLTEyVDAwOjA5OjUw...</td>\n", "      <td>https://saha-hub123.github.io/Isa.github.io/</td>\n", "      <td>2025-03-11 15:12:55.597000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON>isa</td>\n", "      <td>2025-03-11 22:12:55.597000+07:00</td>\n", "      <td>1</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1233</th>\n", "      <td>68415d202a714c784c3741e7</td>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>NOGIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:8000:102...</td>\n", "      <td>4</td>\n", "      <td>men<PERSON><PERSON><PERSON> tugas dan melanjutkan tugas</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcHJveWVrMXdl...</td>\n", "      <td>https://proyek1webs.github.io/BananaChip/</td>\n", "      <td>2025-06-05 09:02:24.748000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON></td>\n", "      <td>2025-06-05 16:02:24.748000+07:00</td>\n", "      <td>13</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1234</th>\n", "      <td>6841846cbb465f4e88bad561</td>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>Laptop-mahalIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2402:5680:932...</td>\n", "      <td>4</td>\n", "      <td>weekly progress report</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/raoz-kitchen-test...</td>\n", "      <td>2025-06-05 11:50:04.240000+00:00</td>\n", "      <td>1.203634e+17</td>\n", "      <td>Nadi</td>\n", "      <td>2025-06-05 18:50:04.240000+07:00</td>\n", "      <td>13</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1235</th>\n", "      <td>6841a6c1251c46cfa1da0c65</td>\n", "      <td>6289685587547</td>\n", "      <td>1</td>\n", "      <td>WIN-VM3JP598T2GIP</td>\n", "      <td>https://whatismyipaddress.com/ip/*************</td>\n", "      <td>4</td>\n", "      <td>lanjut</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/laundryu9.shop/uT...</td>\n", "      <td>2025-06-05 14:16:33.438000+00:00</td>\n", "      <td>1.203634e+17</td>\n", "      <td>richard</td>\n", "      <td>2025-06-05 21:16:33.438000+07:00</td>\n", "      <td>13</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1236</th>\n", "      <td>6841aade251c46cfa1da0c6a</td>\n", "      <td>6282258120851</td>\n", "      <td>1</td>\n", "      <td>lifyaIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:c0:2d10:...</td>\n", "      <td>4</td>\n", "      <td>mengecek gtmetrix lagi</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/alifyazz.github.i...</td>\n", "      <td>2025-06-05 14:34:06.848000+00:00</td>\n", "      <td>1.203630e+17</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2025-06-05 21:34:06.848000+07:00</td>\n", "      <td>13</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1237</th>\n", "      <td>6841f694ca2e662ccd018e55</td>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>NOGIIP</td>\n", "      <td>https://whatismyipaddress.com/ip/2404:8000:102...</td>\n", "      <td>4</td>\n", "      <td>lanjut</td>\n", "      <td>v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...</td>\n", "      <td>https://gtmetrix.com/reports/nogi3201.github.i...</td>\n", "      <td>2025-06-05 19:57:08.882000+00:00</td>\n", "      <td>1.203634e+17</td>\n", "      <td><PERSON></td>\n", "      <td>2025-06-06 02:57:08.882000+07:00</td>\n", "      <td>13</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1238 rows × 15 columns</p>\n", "</div>"], "text/plain": ["                           _id    phonenumber  cycle           hostname  \\\n", "0     67d03bd5b249b1388ac7c61d  6288970788847      1     Laptop-mahalIP   \n", "1     67d04b7855dfbb2c3f9d9265  6283137760847      1       SyalwalyrhIP   \n", "2     67d04f9355dfbb2c3f9d926c  6282116301579      1    Virna_AlmirahIP   \n", "3     67d0505a55dfbb2c3f9d926f  6285722341788      1  DESKTOP-EME1OPIIP   \n", "4     67d052f755dfbb2c3f9d9274  6285158354600      1             SahaIP   \n", "...                        ...            ...    ...                ...   \n", "1233  68415d202a714c784c3741e7  6282161299141      1             NOGIIP   \n", "1234  6841846cbb465f4e88bad561  6288970788847      1     Laptop-mahalIP   \n", "1235  6841a6c1251c46cfa1da0c65  6289685587547      1  WIN-VM3JP598T2GIP   \n", "1236  6841aade251c46cfa1da0c6a  6282258120851      1            lifyaIP   \n", "1237  6841f694ca2e662ccd018e55  6282161299141      1             NOGIIP   \n", "\n", "                                                     ip  screenshots  \\\n", "0      https://whatismyipaddress.com/ip/***************            4   \n", "1        https://whatismyipaddress.com/ip/*************            4   \n", "2     https://whatismyipaddress.com/ip/2001:448a:303...            4   \n", "3       https://whatismyipaddress.com/ip/**************            4   \n", "4       https://whatismyipaddress.com/ip/**************            3   \n", "...                                                 ...          ...   \n", "1233  https://whatismyipaddress.com/ip/2404:8000:102...            4   \n", "1234  https://whatismyipaddress.com/ip/2402:5680:932...            4   \n", "1235     https://whatismyipaddress.com/ip/*************            4   \n", "1236  https://whatismyipaddress.com/ip/2404:c0:2d10:...            4   \n", "1237  https://whatismyipaddress.com/ip/2404:8000:102...            4   \n", "\n", "                                         p<PERSON><PERSON><PERSON><PERSON>  \\\n", "0                      edit content web portofolio   \n", "1     mining, buat website, sambungin webhook, dll   \n", "2               buat website, mining, materi buku,   \n", "3                      membuat website dan maining   \n", "4                         memperbaiki tampilan web   \n", "...                                            ...   \n", "1233       mengerjakan tugas dan melanjutkan tugas   \n", "1234                        weekly progress report   \n", "1235                                        lanjut   \n", "1236                        mengecek gtmetrix lagi   \n", "1237                                        lanjut   \n", "\n", "                                                  token  \\\n", "0     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIyOjMwOjE0...   \n", "1     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjIxOjUx...   \n", "2     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjQ1OjI3...   \n", "3     v4.public.eyJleHAiOiIyMDI1LTAzLTExVDIzOjU2OjE2...   \n", "4     v4.public.eyJleHAiOiIyMDI1LTAzLTEyVDAwOjA5OjUw...   \n", "...                                                 ...   \n", "1233  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vcHJveWVrMXdl...   \n", "1234  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1235  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1236  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "1237  v4.public.eyJhbGlhcyI6Imh0dHBzOi8vZ3RtZXRyaXgu...   \n", "\n", "                                           url<PERSON><PERSON><PERSON>an  \\\n", "0                           https://venalism.github.io/   \n", "1       https://hp-pinjam.github.io/proposal-hp-pinjam/   \n", "2       https://hp-pinjam.github.io/proposal-hp-pinjam/   \n", "3                      https://fauzinrfdlh10.github.io/   \n", "4          https://saha-hub123.github.io/Isa.github.io/   \n", "...                                                 ...   \n", "1233          https://proyek1webs.github.io/BananaChip/   \n", "1234  https://gtmetrix.com/reports/raoz-kitchen-test...   \n", "1235  https://gtmetrix.com/reports/laundryu9.shop/uT...   \n", "1236  https://gtmetrix.com/reports/alifyazz.github.i...   \n", "1237  https://gtmetrix.com/reports/nogi3201.github.i...   \n", "\n", "                            createdAt     wagroupid                    name  \\\n", "0    2025-03-11 13:34:13.688000+00:00  1.203630e+17                    Nadi   \n", "1    2025-03-11 14:40:56.585000+00:00  1.203630e+17  <PERSON><PERSON><PERSON> <PERSON><PERSON>   \n", "2    2025-03-11 14:58:27.187000+00:00  1.203630e+17           6282116301579   \n", "3    2025-03-11 15:01:46.410000+00:00  1.203630e+17      <PERSON><PERSON><PERSON><PERSON> Fauzi NF   \n", "4    2025-03-11 15:12:55.597000+00:00  1.203630e+17                   M.isa   \n", "...                               ...           ...                     ...   \n", "1233 2025-06-05 09:02:24.748000+00:00  1.203630e+17                   Hasan   \n", "1234 2025-06-05 11:50:04.240000+00:00  1.203634e+17                    Nadi   \n", "1235 2025-06-05 14:16:33.438000+00:00  1.203634e+17                 richard   \n", "1236 2025-06-05 14:34:06.848000+00:00  1.203630e+17                  <PERSON><PERSON><PERSON>   \n", "1237 2025-06-05 19:57:08.882000+00:00  1.203634e+17                   Hasan   \n", "\n", "                        date_time_wib  week_custom year_week  \n", "0    2025-03-11 20:34:13.688000+07:00            1   2025-W1  \n", "1    2025-03-11 21:40:56.585000+07:00            1   2025-W1  \n", "2    2025-03-11 21:58:27.187000+07:00            1   2025-W1  \n", "3    2025-03-11 22:01:46.410000+07:00            1   2025-W1  \n", "4    2025-03-11 22:12:55.597000+07:00            1   2025-W1  \n", "...                               ...          ...       ...  \n", "1233 2025-06-05 16:02:24.748000+07:00           13  2025-W13  \n", "1234 2025-06-05 18:50:04.240000+07:00           13  2025-W13  \n", "1235 2025-06-05 21:16:33.438000+07:00           13  2025-W13  \n", "1236 2025-06-05 21:34:06.848000+07:00           13  2025-W13  \n", "1237 2025-06-06 02:57:08.882000+07:00           13  2025-W13  \n", "\n", "[1238 rows x 15 columns]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pomokit"]}, {"cell_type": "code", "execution_count": 53, "id": "c5b451f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data berhasil disimpan ke dataset/result/pomokit.csv\n"]}], "source": ["# simpan data ke file CSV baru\n", "df_pomokit.to_csv(\"dataset/result/pomokit.csv\", index=False)\n", "print(\"Data berhasil disimpan ke dataset/result/pomokit.csv\")"]}, {"cell_type": "code", "execution_count": 54, "id": "b5df7904", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>phonenumber</th>\n", "      <th>cycle</th>\n", "      <th>p<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>date_time_wib</th>\n", "      <th>year_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>edit content web portofolio</td>\n", "      <td>2025-03-11 20:34:13.688000+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6283137760847</td>\n", "      <td>1</td>\n", "      <td>mining, buat website, sambungin webhook, dll</td>\n", "      <td>2025-03-11 21:40:56.585000+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6282116301579</td>\n", "      <td>1</td>\n", "      <td>buat website, mining, materi buku,</td>\n", "      <td>2025-03-11 21:58:27.187000+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6285722341788</td>\n", "      <td>1</td>\n", "      <td>membuat website dan maining</td>\n", "      <td>2025-03-11 22:01:46.410000+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6285158354600</td>\n", "      <td>1</td>\n", "      <td>memperbaiki tampilan web</td>\n", "      <td>2025-03-11 22:12:55.597000+07:00</td>\n", "      <td>2025-W1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1233</th>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>men<PERSON><PERSON><PERSON> tugas dan melanjutkan tugas</td>\n", "      <td>2025-06-05 16:02:24.748000+07:00</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1234</th>\n", "      <td>6288970788847</td>\n", "      <td>1</td>\n", "      <td>weekly progress report</td>\n", "      <td>2025-06-05 18:50:04.240000+07:00</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1235</th>\n", "      <td>6289685587547</td>\n", "      <td>1</td>\n", "      <td>lanjut</td>\n", "      <td>2025-06-05 21:16:33.438000+07:00</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1236</th>\n", "      <td>6282258120851</td>\n", "      <td>1</td>\n", "      <td>mengecek gtmetrix lagi</td>\n", "      <td>2025-06-05 21:34:06.848000+07:00</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1237</th>\n", "      <td>6282161299141</td>\n", "      <td>1</td>\n", "      <td>lanjut</td>\n", "      <td>2025-06-06 02:57:08.882000+07:00</td>\n", "      <td>2025-W13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1238 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        phonenumber  cycle                                     p<PERSON><PERSON><PERSON><PERSON>  \\\n", "0     6288970788847      1                   edit content web portofolio   \n", "1     6283137760847      1  mining, buat website, sambungin webhook, dll   \n", "2     6282116301579      1            buat website, mining, materi buku,   \n", "3     6285722341788      1                   membuat website dan maining   \n", "4     6285158354600      1                      memperbaiki tampilan web   \n", "...             ...    ...                                           ...   \n", "1233  6282161299141      1       men<PERSON>jakan tugas dan melanjutkan tugas   \n", "1234  6288970788847      1                        weekly progress report   \n", "1235  6289685587547      1                                        lanjut   \n", "1236  6282258120851      1                        mengecek gtmetrix lagi   \n", "1237  6282161299141      1                                        lanjut   \n", "\n", "                        date_time_wib year_week  \n", "0    2025-03-11 20:34:13.688000+07:00   2025-W1  \n", "1    2025-03-11 21:40:56.585000+07:00   2025-W1  \n", "2    2025-03-11 21:58:27.187000+07:00   2025-W1  \n", "3    2025-03-11 22:01:46.410000+07:00   2025-W1  \n", "4    2025-03-11 22:12:55.597000+07:00   2025-W1  \n", "...                               ...       ...  \n", "1233 2025-06-05 16:02:24.748000+07:00  2025-W13  \n", "1234 2025-06-05 18:50:04.240000+07:00  2025-W13  \n", "1235 2025-06-05 21:16:33.438000+07:00  2025-W13  \n", "1236 2025-06-05 21:34:06.848000+07:00  2025-W13  \n", "1237 2025-06-06 02:57:08.882000+07:00  2025-W13  \n", "\n", "[1238 rows x 5 columns]"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["feature = ['_id','hostname','ip','screenshots','token','urlpekerja<PERSON>','createdAt','wagroupid','name', 'week_custom']\n", "df_pomokit.drop(columns=feature, inplace=True)\n", "df_pomokit"]}, {"cell_type": "code", "execution_count": 55, "id": "bd3fb110", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data berhasil disimpan ke dataset/result1/pomokit.csv\n"]}], "source": ["# simpan data ke file CSV baru\n", "df_pomokit.to_csv(\"dataset/result1/pomokit.csv\", index=False)\n", "print(\"Data berhasil disimpan ke dataset/result1/pomokit.csv\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}