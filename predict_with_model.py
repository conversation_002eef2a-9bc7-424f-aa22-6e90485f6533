#!/usr/bin/env python3
"""
Script untuk melakukan prediksi menggunakan model yang telah disimpan
Contoh penggunaan model machine learning untuk prediksi risiko kelelahan
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Add src to path
sys.path.append('src')

from utils.data_utils import load_model_artifacts

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FatigueRiskPredictor:
    """Class untuk melakukan prediksi risiko kelelahan"""
    
    def __init__(self, model_dir="results/clean_production_model"):
        """
        Initialize predictor dengan memuat model artifacts
        
        Args:
            model_dir: Directory yang berisi model artifacts
        """
        self.model_dir = Path(model_dir)
        self.artifacts = None
        self.model = None
        self.features = None
        self.label_encoder = None
        self.metadata = None
        
        self._load_model()
    
    def _load_model(self):
        """Load model artifacts"""
        try:
            logger.info(f"Loading model from {self.model_dir}")
            self.artifacts = load_model_artifacts(self.model_dir)
            
            # Extract components
            self.model = self.artifacts.get('pipeline')
            self.features = self.artifacts.get('features')
            self.label_encoder = self.artifacts.get('label_encoder')
            self.metadata = self.artifacts.get('metadata', {})
            
            if not self.model:
                raise ValueError("Model pipeline not found in artifacts")
            if not self.features:
                raise ValueError("Feature names not found in artifacts")
            if not self.label_encoder:
                raise ValueError("Label encoder not found in artifacts")
            
            logger.info(f"✅ Model loaded successfully!")
            logger.info(f"   Algorithm: {self.metadata.get('algorithm_name', 'Unknown')}")
            logger.info(f"   Accuracy: {self.metadata.get('accuracy', 0.0):.4f}")
            logger.info(f"   Features: {len(self.features)}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            raise
    
    def get_feature_info(self):
        """Menampilkan informasi fitur yang dibutuhkan"""
        print("\n📋 FITUR YANG DIBUTUHKAN UNTUK PREDIKSI:")
        print("="*60)
        for i, feature in enumerate(self.features, 1):
            print(f"{i:2d}. {feature}")
        print("="*60)
        return self.features
    
    def predict_single(self, data_dict):
        """
        Prediksi untuk satu sample data
        
        Args:
            data_dict: Dictionary berisi nilai fitur
            
        Returns:
            tuple: (predicted_class, prediction_probability)
        """
        try:
            # Convert to DataFrame
            df = pd.DataFrame([data_dict])
            
            # Ensure all required features are present
            missing_features = set(self.features) - set(df.columns)
            if missing_features:
                raise ValueError(f"Missing features: {missing_features}")
            
            # Select and order features
            X = df[self.features]
            
            # Make prediction
            prediction = self.model.predict(X)[0]
            prediction_proba = self.model.predict_proba(X)[0]
            
            # Decode prediction
            predicted_class = self.label_encoder.inverse_transform([prediction])[0]
            
            # Get class probabilities
            classes = self.label_encoder.classes_
            proba_dict = {cls: prob for cls, prob in zip(classes, prediction_proba)}
            
            return predicted_class, proba_dict
            
        except Exception as e:
            logger.error(f"Prediction failed: {str(e)}")
            raise
    
    def predict_batch(self, data_df):
        """
        Prediksi untuk multiple samples
        
        Args:
            data_df: DataFrame berisi data untuk prediksi
            
        Returns:
            DataFrame: Hasil prediksi dengan probabilitas
        """
        try:
            # Ensure all required features are present
            missing_features = set(self.features) - set(data_df.columns)
            if missing_features:
                raise ValueError(f"Missing features: {missing_features}")
            
            # Select and order features
            X = data_df[self.features]
            
            # Make predictions
            predictions = self.model.predict(X)
            prediction_probas = self.model.predict_proba(X)
            
            # Decode predictions
            predicted_classes = self.label_encoder.inverse_transform(predictions)
            
            # Create results DataFrame
            results = data_df.copy()
            results['predicted_fatigue_risk'] = predicted_classes
            
            # Add probability columns
            classes = self.label_encoder.classes_
            for i, cls in enumerate(classes):
                results[f'prob_{cls}'] = prediction_probas[:, i]
            
            return results
            
        except Exception as e:
            logger.error(f"Batch prediction failed: {str(e)}")
            raise
    
    def predict_from_csv(self, csv_path, output_path=None):
        """
        Prediksi dari file CSV
        
        Args:
            csv_path: Path ke file CSV input
            output_path: Path untuk menyimpan hasil (optional)
            
        Returns:
            DataFrame: Hasil prediksi
        """
        try:
            # Load data
            logger.info(f"Loading data from {csv_path}")
            data = pd.read_csv(csv_path)
            
            # Make predictions
            results = self.predict_batch(data)
            
            # Save results if output path provided
            if output_path:
                results.to_csv(output_path, index=False)
                logger.info(f"Results saved to {output_path}")
            
            return results
            
        except Exception as e:
            logger.error(f"CSV prediction failed: {str(e)}")
            raise

def demo_prediction():
    """Demo penggunaan predictor"""
    print("🚀 DEMO PREDIKSI RISIKO KELELAHAN")
    print("="*50)
    
    # Initialize predictor
    predictor = FatigueRiskPredictor()
    
    # Show required features
    predictor.get_feature_info()
    
    # Example 1: Single prediction
    print("\n🔮 CONTOH 1: PREDIKSI SINGLE SAMPLE")
    print("-"*40)
    
    # Sample data (you need to provide actual values)
    sample_data = {
        'productivity_points': 85.5,
        'strava_title_count': 12,
        'gamification_balance': 150.0,
        'strava_distance': 5.2,
        'strava_moving_time': 1800,
        'strava_total_elevation_gain': 45.0,
        'strava_average_speed': 10.4,
        'strava_max_speed': 15.8,
        'strava_average_heartrate': 145.0,
        'strava_max_heartrate': 175.0,
        'strava_suffer_score': 28.0,
        'strava_calories': 320.0,
        'strava_average_watts': 180.0,
        'strava_max_watts': 250.0,
        'strava_weighted_average_watts': 185.0,
        'strava_kilojoules': 324.0,
        'strava_device_watts': 1,
        'strava_has_heartrate': 1
    }
    
    try:
        predicted_class, probabilities = predictor.predict_single(sample_data)
        
        print(f"📊 Hasil Prediksi: {predicted_class}")
        print("📈 Probabilitas:")
        for risk_level, prob in probabilities.items():
            print(f"   • {risk_level}: {prob:.4f} ({prob*100:.2f}%)")
        
    except Exception as e:
        print(f"❌ Error in single prediction: {str(e)}")
        print("💡 Tip: Pastikan semua fitur yang dibutuhkan tersedia")
    
    # Example 2: Batch prediction from existing data
    print("\n🔮 CONTOH 2: PREDIKSI BATCH DARI DATA EXISTING")
    print("-"*40)
    
    try:
        # Load some existing data for demo
        data_path = "dataset/processed/safe_ml_fatigue_dataset.csv"
        if Path(data_path).exists():
            # Load first 5 rows for demo
            demo_data = pd.read_csv(data_path).head(5)
            
            # Remove target column if exists
            if 'fatigue_risk' in demo_data.columns:
                actual_labels = demo_data['fatigue_risk'].tolist()
                demo_data = demo_data.drop('fatigue_risk', axis=1)
            else:
                actual_labels = None
            
            # Make predictions
            results = predictor.predict_batch(demo_data)
            
            print("📊 Hasil Prediksi Batch:")
            for i, (_, row) in enumerate(results.iterrows()):
                predicted = row['predicted_fatigue_risk']
                prob_high = row['prob_high_risk']
                prob_medium = row['prob_medium_risk'] 
                prob_low = row['prob_low_risk']
                
                print(f"   Sample {i+1}: {predicted} (H:{prob_high:.3f}, M:{prob_medium:.3f}, L:{prob_low:.3f})")
                if actual_labels:
                    actual = actual_labels[i]
                    status = "✅" if predicted == actual else "❌"
                    print(f"             Actual: {actual} {status}")
        else:
            print("⚠️  Demo data not found. Silakan gunakan data Anda sendiri.")
            
    except Exception as e:
        print(f"❌ Error in batch prediction: {str(e)}")

def main():
    """Main function"""
    print("🤖 FATIGUE RISK PREDICTION SYSTEM")
    print("="*50)
    
    try:
        # Run demo
        demo_prediction()
        
        print("\n" + "="*50)
        print("💡 CARA PENGGUNAAN:")
        print("1. Siapkan data dengan fitur yang dibutuhkan")
        print("2. Gunakan FatigueRiskPredictor class")
        print("3. Panggil predict_single() atau predict_batch()")
        print("4. Hasil berupa: low_risk, medium_risk, atau high_risk")
        
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        print("\n❌ Demo gagal. Pastikan model sudah dilatih dan disimpan.")
        print("   Jalankan: python main1.py --ml-only")

if __name__ == "__main__":
    main()
