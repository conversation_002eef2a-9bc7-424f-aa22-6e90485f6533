#!/usr/bin/env python3
"""
Script untuk membuat template CSV untuk prediksi
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append('src')
from utils.data_utils import load_model_artifacts

def create_template():
    """Membuat template CSV untuk input prediksi"""
    
    print("📝 CREATING PREDICTION TEMPLATE")
    print("="*40)
    
    try:
        # Load model to get required features
        artifacts = load_model_artifacts("results/clean_production_model")
        features = artifacts['features']
        metadata = artifacts['metadata']
        
        print(f"✅ Model loaded: {metadata.get('algorithm_name')}")
        print(f"📋 Required features: {len(features)}")
        
        # Create template DataFrame
        template_data = {}
        
        # Add example values for each feature
        for feature in features:
            if 'points' in feature.lower():
                template_data[feature] = [85.5, 92.3, 78.1]
            elif 'count' in feature.lower():
                template_data[feature] = [12, 8, 15]
            elif 'balance' in feature.lower():
                template_data[feature] = [150.0, 200.5, 120.8]
            elif 'distance' in feature.lower():
                template_data[feature] = [5.2, 8.1, 3.7]
            elif 'time' in feature.lower():
                template_data[feature] = [1800, 2400, 1200]
            elif 'elevation' in feature.lower():
                template_data[feature] = [45.0, 120.5, 25.3]
            elif 'speed' in feature.lower():
                template_data[feature] = [10.4, 12.8, 8.9]
            elif 'heartrate' in feature.lower():
                if 'max' in feature.lower():
                    template_data[feature] = [175.0, 185.2, 168.5]
                else:
                    template_data[feature] = [145.0, 155.8, 138.2]
            elif 'suffer' in feature.lower():
                template_data[feature] = [28.0, 45.2, 18.5]
            elif 'calories' in feature.lower():
                template_data[feature] = [320.0, 450.8, 280.5]
            elif 'watts' in feature.lower():
                if 'max' in feature.lower():
                    template_data[feature] = [250.0, 280.5, 220.8]
                else:
                    template_data[feature] = [180.0, 200.3, 165.7]
            elif 'kilojoules' in feature.lower():
                template_data[feature] = [324.0, 420.8, 285.2]
            elif 'device' in feature.lower() or 'has_' in feature.lower():
                template_data[feature] = [1, 0, 1]  # Binary features
            else:
                # Default numeric values
                template_data[feature] = [100.0, 150.5, 80.2]
        
        # Create DataFrame
        template_df = pd.DataFrame(template_data)
        
        # Save template
        template_path = "prediction_template.csv"
        template_df.to_csv(template_path, index=False)
        
        print(f"✅ Template created: {template_path}")
        print(f"📊 Template contains {len(template_df)} sample rows")
        
        # Show template structure
        print(f"\n📋 Template structure:")
        print(f"   Rows: {len(template_df)}")
        print(f"   Columns: {len(template_df.columns)}")
        
        print(f"\n📝 First few columns:")
        for col in template_df.columns[:5]:
            print(f"   • {col}: {template_df[col].iloc[0]}")
        
        # Create empty template too
        empty_template = pd.DataFrame(columns=features)
        empty_path = "prediction_template_empty.csv"
        empty_template.to_csv(empty_path, index=False)
        
        print(f"\n📄 Empty template also created: {empty_path}")
        print(f"   Use this to fill with your own data")
        
        # Show usage instructions
        print(f"\n💡 USAGE INSTRUCTIONS:")
        print(f"   1. Open {template_path} to see example data")
        print(f"   2. Use {empty_path} to input your own data")
        print(f"   3. Fill all {len(features)} columns with appropriate values")
        print(f"   4. Save and use with prediction scripts")
        
        return template_path, empty_path
        
    except Exception as e:
        print(f"❌ Failed to create template: {e}")
        print("💡 Make sure model is trained first: python main1.py --ml-only")
        return None, None

def show_feature_descriptions():
    """Menampilkan deskripsi fitur"""
    
    print(f"\n📖 FEATURE DESCRIPTIONS:")
    print("="*50)
    
    descriptions = {
        'productivity_points': 'Poin produktivitas pengguna',
        'strava_title_count': 'Jumlah aktivitas Strava',
        'gamification_balance': 'Saldo gamifikasi',
        'strava_distance': 'Jarak aktivitas (km)',
        'strava_moving_time': 'Waktu bergerak (detik)',
        'strava_total_elevation_gain': 'Total kenaikan elevasi (m)',
        'strava_average_speed': 'Kecepatan rata-rata (km/h)',
        'strava_max_speed': 'Kecepatan maksimum (km/h)',
        'strava_average_heartrate': 'Detak jantung rata-rata (bpm)',
        'strava_max_heartrate': 'Detak jantung maksimum (bpm)',
        'strava_suffer_score': 'Skor penderitaan Strava',
        'strava_calories': 'Kalori yang terbakar',
        'strava_average_watts': 'Daya rata-rata (watts)',
        'strava_max_watts': 'Daya maksimum (watts)',
        'strava_weighted_average_watts': 'Daya rata-rata tertimbang (watts)',
        'strava_kilojoules': 'Energi total (kilojoules)',
        'strava_device_watts': 'Apakah menggunakan power meter (1/0)',
        'strava_has_heartrate': 'Apakah ada data detak jantung (1/0)'
    }
    
    try:
        # Load actual features from model
        artifacts = load_model_artifacts("results/clean_production_model")
        features = artifacts['features']
        
        for feature in features:
            desc = descriptions.get(feature, 'Deskripsi tidak tersedia')
            print(f"• {feature:30} : {desc}")
            
    except Exception as e:
        print(f"❌ Could not load features: {e}")

def main():
    """Main function"""
    print("📝 PREDICTION TEMPLATE CREATOR")
    print("="*50)
    
    # Create templates
    template_path, empty_path = create_template()
    
    if template_path:
        # Show feature descriptions
        show_feature_descriptions()
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. Edit {empty_path} with your data")
        print(f"   2. Run: python predict_with_model.py")
        print(f"   3. Or use the FatigueRiskPredictor class in your code")

if __name__ == "__main__":
    main()
