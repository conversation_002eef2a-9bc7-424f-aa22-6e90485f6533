identity,year_week,total_distance_km,avg_distance_km,activity_days,total_time_minutes,avg_time_minutes,strava_activities_titles,total_cycles,avg_cycles,work_days,pomokit_activities_titles,consistency_score,weekly_efficiency,strava_title_count,strava_title_length,strava_unique_words,pomokit_title_count,pomokit_title_length,pomokit_unique_words,combined_titles,total_title_diversity,title_balance_ratio,activity_points,productivity_points,achievement_rate,gamification_balance
user-1,202512,3.5,3.5,1,14,14.0,Run,1,1.0,1,Belajar tentang Artificial Intelligence,0.35,1.0,1,3,1,1,39,4,Run | Belajar tentang Artificial Intelligence,5,0.07692307692307693,58.333333333333336,20.0,0.3916666666666667,38.333333333333336
user-1,202519,3.6,3.6,1,15,15.0,Afternoon Run,2,1.0,2,<PERSON><PERSON><PERSON><PERSON> | <PERSON><PERSON><PERSON><PERSON>l Youtube DT & RF,0.45,1.0,1,13,2,2,69,9,Afternoon Run | Mengerjakan Tugas <PERSON> | <PERSON>gerjak<PERSON> Tugas Tutorial Youtube DT & RF,11,0.18840579710144928,60.0,40.0,0.5,20.0
user-1,202520,3.6,3.6,1,24,24.0,Evening <PERSON>,2,1.0,2,Chapter 1 <PERSON>ajar Prediksi Lewat Data Siswa: Decision Tree dan <PERSON>luasi<PERSON> | Youtube Chapter 1 <PERSON>ajar <PERSON><PERSON>ksi Lewat Data Siswa: Decision Tree dan Evaluasinya,0.45,1.0,1,11,2,2,159,13,Evening Run | Chapter 1 Belajar Prediksi Lewat Data Siswa: Decision Tree dan Evaluasinya | Youtube Chapter 1 Belajar Prediksi Lewat Data Siswa: Decision Tree dan Evaluasinya,15,0.06918238993710692,60.0,40.0,0.5,20.0
user-1,202521,18.2,9.1,2,62,31.0,Afternoon Run | Afternoon Run,2,1.0,2,Chapter 2: Mengenal Random Forest - Prediksi Spesies Burung dengan Akurasi Tinggi | Youtube Chapter 2: Mengenal Random Forest - Prediksi Spesies Burung dengan Akurasi Tinggi,0.7,1.0,2,29,3,2,173,14,Afternoon Run | Afternoon Run | Chapter 2: Mengenal Random Forest - Prediksi Spesies Burung dengan Akurasi Tinggi | Youtube Chapter 2: Mengenal Random Forest - Prediksi Spesies Burung dengan Akurasi Tinggi,17,0.1676300578034682,100.0,40.0,0.7,60.0
user-2,202521,3.9,3.9,1,40,40.0,W,2,1.0,2,mengerjakan medium chapter 1 | Mengerjakan Video YT Chapter 1,0.45,1.0,1,1,1,2,61,7,W | mengerjakan medium chapter 1 | Mengerjakan Video YT Chapter 1,8,0.01639344262295082,65.0,40.0,0.525,25.0
user-2,202522,20.9,10.45,2,76,38.0,W2 | W3,2,1.0,2,Membuat artikel medium chapter 2 | Mengerjakan video youtube chapter 2,0.7,1.0,2,7,3,2,70,9,W2 | W3 | Membuat artikel medium chapter 2 | Mengerjakan video youtube chapter 2,12,0.1,100.0,40.0,0.7,60.0
user-3,202511,6.4,3.2,2,68,34.0,jalan jalan sore | joging nunggu buka,4,1.0,4,membuat portofolio | lanjutan membuat portofolio | melanjutkan membuat website | melanjutkan membuat website,0.9,1.0,2,37,6,4,108,6,jalan jalan sore | joging nunggu buka | membuat portofolio | lanjutan membuat portofolio | melanjutkan membuat website | melanjutkan membuat website,12,0.3425925925925926,100.0,80.0,0.9,20.0
user-4,202511,3.7,3.7,1,34,34.0,Afternoon Run,1,1.0,1,pretest-kecerdasan-buata,0.35,1.0,1,13,2,1,24,1,Afternoon Run | pretest-kecerdasan-buata,3,0.5416666666666666,61.66666666666667,20.0,0.4083333333333334,41.66666666666667
user-4,202512,10.6,3.533333333333333,3,121,40.333333333333336,Ngabuburit Walk | Evening Run | Afternoon Run,1,1.0,1,belajar bersama dosen praktisi ai pak rully,0.6,1.0,3,45,6,1,43,7,Ngabuburit Walk | Evening Run | Afternoon Run | belajar bersama dosen praktisi ai pak rully,13,1.0465116279069768,100.0,20.0,0.6,80.0
user-4,202521,2.0,2.0,1,35,35.0,Afternoon Walk,2,1.0,2,tugas 2 medium decision tree dan random forest | Tugas 2 Youtube Decision tree dan random forest,0.45,1.0,1,14,2,2,96,10,Afternoon Walk | tugas 2 medium decision tree dan random forest | Tugas 2 Youtube Decision tree dan random forest,12,0.14583333333333334,33.33333333333333,40.0,0.36666666666666664,6.666666666666671
user-5,202520,11.0,11.0,1,107,107.0,Berjalan Pagi,2,1.0,2,Mmebuat Tutorial Hello World di YouTube | Mmebuat Tutorial Hello Wolrd dengan GoFiber di Medium,0.45,1.0,1,13,2,2,95,11,Berjalan Pagi | Mmebuat Tutorial Hello World di YouTube | Mmebuat Tutorial Hello Wolrd dengan GoFiber di Medium,13,0.1368421052631579,100.0,40.0,0.7,60.0
user-5,202521,7.5,7.5,1,91,91.0,Morning walk with repa 🫶🏻,2,1.0,2,Mmebuat Tutorial Fungsi Struct & Return cjson di Go dan koneksi Go dengan Postman | Membuat Tutorial Fungsi Struct & Return cjson dan Koneksi Go dengan Postman di Medium,0.45,1.0,1,25,5,2,169,16,Morning walk with repa 🫶🏻 | Mmebuat Tutorial Fungsi Struct & Return cjson di Go dan koneksi Go dengan Postman | Membuat Tutorial Fungsi Struct & Return cjson dan Koneksi Go dengan Postman di Medium,21,0.14792899408284024,100.0,40.0,0.7,60.0
user-6,202511,6.5,3.25,2,85,42.5,Lunch Walk | Morning Walk,7,1.0,7,edit personal branding | melakukan mining coin | edit personal branding | mining microbitcoin | melanjutkan website dan lain lain | mining microbit coin dan lain lain | membuat web dan cari referensi buku,1.0,1.0,2,25,4,7,204,18,Lunch Walk | Morning Walk | edit personal branding | melakukan mining coin | edit personal branding | mining microbitcoin | melanjutkan website dan lain lain | mining microbit coin dan lain lain | membuat web dan cari referensi buku,22,0.12254901960784313,100.0,100.0,1.0,0.0
user-6,202512,3.0,3.0,1,45,45.0,Afternoon Walk,3,1.0,3,mining microbit coin | melanjutkan website client | melanjutkan website,0.55,1.0,1,14,2,3,71,7,Afternoon Walk | mining microbit coin | melanjutkan website client | melanjutkan website,9,0.19718309859154928,50.0,60.0,0.55,10.0
user-7,202511,11.2,5.6,2,151,75.5,lari 2 | Afternoon Run,9,1.0,9,"membuat web portfolio | edit web portofolio,dll | mengedit web portofolio | lanjutkan web dan lain lain | mengedit web dan lain lain | mengedit tampilan web ,dan tugas lainnya | melanjutkan web dan lain lain | melanjutkan tugas lagi | MELANJUTKAN LAGI YA",1.0,1.0,2,22,5,9,254,18,"lari 2 | Afternoon Run | membuat web portfolio | edit web portofolio,dll | mengedit web portofolio | lanjutkan web dan lain lain | mengedit web dan lain lain | mengedit tampilan web ,dan tugas lainnya | melanjutkan web dan lain lain | melanjutkan tugas lagi | MELANJUTKAN LAGI YA",23,0.08661417322834646,100.0,100.0,1.0,0.0
user-7,202512,7.800000000000001,3.9000000000000004,2,39,19.5,jalan lagj | cari takjil,1,1.0,1,mengerjakan lagi dan lagi,0.6,1.0,2,24,5,1,25,3,jalan lagj | cari takjil | mengerjakan lagi dan lagi,8,0.96,100.0,20.0,0.6,80.0
user-7,202520,3.0,3.0,1,10,10.0,asoy,1,1.0,1,untuk isi table domy,0.35,1.0,1,4,1,1,20,4,asoy | untuk isi table domy,5,0.2,50.0,20.0,0.35,30.0
user-7,202521,7.5,3.75,2,28,14.0,lari sore | ddem,1,1.0,1,mengisi tabel pomokit dan gtmetrix,0.6,1.0,2,16,4,1,34,5,lari sore | ddem | mengisi tabel pomokit dan gtmetrix,9,0.47058823529411764,100.0,20.0,0.6,80.0
user-8,202511,6.4,3.2,2,91,45.5,Afternoon Walk | Afternoon Walk,4,1.0,4,Melanjutkan pengerjaan website | mining dan edit web | lanjut mengerjaka assesment | lanjutkan assesment,0.9,1.0,2,31,3,4,104,12,Afternoon Walk | Afternoon Walk | Melanjutkan pengerjaan website | mining dan edit web | lanjut mengerjaka assesment | lanjutkan assesment,15,0.2980769230769231,100.0,80.0,0.9,20.0
user-8,202512,7.8999999999999995,3.9499999999999997,2,28,14.0,Morning Run | Afternoon Run,4,1.0,4,lanjutkan mining dari kemaren | melanjutkan kerjaan | melanjutkan pengerjaan website | lanjutkan kerjaan yang belum beres,0.9,1.0,2,27,4,4,121,12,Morning Run | Afternoon Run | lanjutkan mining dari kemaren | melanjutkan kerjaan | melanjutkan pengerjaan website | lanjutkan kerjaan yang belum beres,16,0.2231404958677686,100.0,80.0,0.9,20.0
user-9,202521,6.3,6.3,1,21,21.0,Pulanh dari rumah teman,2,1.0,2,mengerjakan tutorial membuat hello world menggunakan golang untuk di youtube | membuat tutorial hello world di medium,0.45,1.0,1,23,4,2,117,12,Pulanh dari rumah teman | mengerjakan tutorial membuat hello world menggunakan golang untuk di youtube | membuat tutorial hello world di medium,16,0.19658119658119658,100.0,40.0,0.7,60.0
user-9,202522,5.4,5.4,1,81,81.0,Muter lembang,2,1.0,2,membuat struk & mrnyambungkan postman | bikin artikel di medium,0.45,1.0,1,13,2,2,63,10,Muter lembang | membuat struk & mrnyambungkan postman | bikin artikel di medium,12,0.20634920634920634,90.0,40.0,0.65,50.0
user-10,202512,3.1,3.1,1,41,41.0,Jalan sore 3KM,4,1.0,4,Ngerjain yang bisa dikerjain | terus dan tetap melanjutkan | hiya hiya hiya hiya hiya | mencoba podomoro baru ru ru,0.65,1.0,1,14,3,4,115,14,Jalan sore 3KM | Ngerjain yang bisa dikerjain | terus dan tetap melanjutkan | hiya hiya hiya hiya hiya | mencoba podomoro baru ru ru,17,0.12173913043478261,51.66666666666667,80.0,0.6583333333333334,28.33333333333333
user-10,202522,6.1,3.05,2,19,9.5,Jalan Jalan | jjm,1,1.0,1,Menambah Poin Poin Poin,0.6,1.0,2,17,3,1,23,2,Jalan Jalan | jjm | Menambah Poin Poin Poin,5,0.7391304347826086,100.0,20.0,0.6,80.0
user-11,202512,13.7,6.85,2,54,27.0,Jjs minggu pertama | Jjs week 2,2,1.0,2,Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan - AI Predicting The Future - Bagus Rully Muttaqien,0.7,1.0,2,31,6,2,137,20,Jjs minggu pertama | Jjs week 2 | Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan - AI Predicting The Future - Bagus Rully Muttaqien,26,0.22627737226277372,100.0,40.0,0.7,60.0
user-11,202519,10.2,3.4,3,93,31.0,Berjalan Pagi | Berjalan Pagi | Berjalan Pagi,5,1.0,5,"Membuat video youtube dan artikel medium Decision Tree, Information Gain & Entropy Random Forest | Membuat video yt Decision Tree, Entropy & Information Gain, dan Random Forest | Mengerjakan Artikel Di Medium | Membuat tutorial yt | https://gtmetrix.com/reports/medium.com/y2qcVU70/",1.0,1.0,3,45,3,5,282,21,"Berjalan Pagi | Berjalan Pagi | Berjalan Pagi | Membuat video youtube dan artikel medium Decision Tree, Information Gain & Entropy Random Forest | Membuat video yt Decision Tree, Entropy & Information Gain, dan Random Forest | Mengerjakan Artikel Di Medium | Membuat tutorial yt | https://gtmetrix.com/reports/medium.com/y2qcVU70/",24,0.1595744680851064,100.0,100.0,1.0,0.0
user-11,202521,18.5,9.25,2,50,25.0,Berjalan Pagi | Berjalan Sore,2,1.0,2,Mengerjakan Artikel Medium Chapter 2 | Mengerjakan Video Youtube Chapter 2,0.7,1.0,2,29,4,2,74,8,Berjalan Pagi | Berjalan Sore | Mengerjakan Artikel Medium Chapter 2 | Mengerjakan Video Youtube Chapter 2,12,0.3918918918918919,100.0,40.0,0.7,60.0
user-12,202521,5.3,2.65,2,19,9.5,Berlari Sore | Berlari Malam,2,1.0,2,membuat tutorial hello world with go fiber in medium stories | tutorial membuat hello world with fiber di youtube,0.7,1.0,2,28,4,2,113,13,Berlari Sore | Berlari Malam | membuat tutorial hello world with go fiber in medium stories | tutorial membuat hello world with fiber di youtube,17,0.24778761061946902,88.33333333333333,40.0,0.6416666666666666,48.33333333333333
user-13,202512,6.7,3.35,2,81,40.5,Hari ke 2 | Hari ke 3,2,1.0,2,Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan AI Predicting The Future bersama Bagus Rully Muttaqien,0.7,1.0,2,21,5,2,141,20,Hari ke 2 | Hari ke 3 | Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan AI Predicting The Future bersama Bagus Rully Muttaqien,25,0.14893617021276595,100.0,40.0,0.7,60.0
user-13,202522,30.5,7.625,4,133,33.25,Lari ke 5 | Lari ke 6 | Lari ke 7 | Lari ke 8,9,1.0,9,"mengerjakan artikel medium chapter2 | video Tutorial Decision Tree, Information Gain, Entropy & Random Forest | medium Decision Tree, Information Gain, Entropy & Random Forest | medium Decision Tree cp1 | video Decision Tree cp1 | video Random Forest cp2 | medium Random Forest cp2 | tugas medium Comment Classification cp3 | tugas video Comment Classification chapter3",1.0,1.0,4,45,7,9,369,23,"Lari ke 5 | Lari ke 6 | Lari ke 7 | Lari ke 8 | mengerjakan artikel medium chapter2 | video Tutorial Decision Tree, Information Gain, Entropy & Random Forest | medium Decision Tree, Information Gain, Entropy & Random Forest | medium Decision Tree cp1 | video Decision Tree cp1 | video Random Forest cp2 | medium Random Forest cp2 | tugas medium Comment Classification cp3 | tugas video Comment Classification chapter3",30,0.12195121951219512,100.0,100.0,1.0,0.0
user-14,202511,5.0,5.0,1,54,54.0,Afternoon Run,4,1.0,4,lanjutin personal-branding | melanjutkan website personal saya | mengerjakan tugas buku | melanjutkan pengerjaan wibu,0.65,1.0,1,13,2,4,117,12,Afternoon Run | lanjutin personal-branding | melanjutkan website personal saya | mengerjakan tugas buku | melanjutkan pengerjaan wibu,14,0.1111111111111111,83.33333333333334,80.0,0.8166666666666668,3.333333333333343
user-14,202515,18.0,9.0,2,160,80.0,Lunch Run | Morning Run,1,1.0,1,Melanjutkan apa saja,0.6,1.0,2,23,4,1,20,3,Lunch Run | Morning Run | Melanjutkan apa saja,7,1.15,100.0,20.0,0.6,80.0
user-15,202512,5.0,5.0,1,40,40.0,Afternoon Run,1,1.0,1,Kuliah matakuliah AI bersama dengan pak bagus rully muttaqien,0.35,1.0,1,13,2,1,61,9,Afternoon Run | Kuliah matakuliah AI bersama dengan pak bagus rully muttaqien,11,0.21311475409836064,83.33333333333334,20.0,0.5166666666666667,63.33333333333334
user-15,202520,3.6,3.6,1,21,21.0,Evening Run,2,1.0,2,mengerjakan artikel medium decision tree dan random forest | membuat artikel algoritma SVR (Support Vector Regression),0.45,1.0,1,11,2,2,118,15,Evening Run | mengerjakan artikel medium decision tree dan random forest | membuat artikel algoritma SVR (Support Vector Regression),17,0.09322033898305085,60.0,40.0,0.5,20.0
user-15,202522,9.0,4.5,2,34,17.0,Afternoon Ride | Lunch Run,2,1.0,2,membuat artikel medium decision tree dan random forest untuk tugas ke 2 | membuat video tutorial youtube untuk memprediksi dataset menggunakan decision tree dan random forest,0.7,1.0,2,26,5,2,174,19,Afternoon Ride | Lunch Run | membuat artikel medium decision tree dan random forest untuk tugas ke 2 | membuat video tutorial youtube untuk memprediksi dataset menggunakan decision tree dan random forest,24,0.14942528735632185,100.0,40.0,0.7,60.0
user-15,202523,34.0,11.333333333333334,3,101,33.666666666666664,Afternoon Ride | Afternoon Ride | Evening Ride,4,1.0,4,mengerjakan chapter 1 tentang decision tree untuk dataser purchase | membuat youtube tentang model desicion tree dengan dataset purchase | mengerjakan artikel medium chapter 2 tentang model random forest dan dataset purchase | mengerjakan medium chapter 5,0.9,1.0,3,46,4,4,255,23,Afternoon Ride | Afternoon Ride | Evening Ride | mengerjakan chapter 1 tentang decision tree untuk dataser purchase | membuat youtube tentang model desicion tree dengan dataset purchase | mengerjakan artikel medium chapter 2 tentang model random forest dan dataset purchase | mengerjakan medium chapter 5,27,0.1803921568627451,100.0,80.0,0.9,20.0
user-16,202511,7.2,3.6,2,116,58.0,Afternoon Walk | Afternoon Walk,8,1.0,8,"add portofolio, add project page | edit bagian project | lanjut add animation | cari refrensi buku cuy | lanjut cari refrensi buku | lanjut cari refrensi buku | lanjut ngerjain buku | lanjut menyusun buku",1.0,1.0,2,31,3,8,204,15,"Afternoon Walk | Afternoon Walk | add portofolio, add project page | edit bagian project | lanjut add animation | cari refrensi buku cuy | lanjut cari refrensi buku | lanjut cari refrensi buku | lanjut ngerjain buku | lanjut menyusun buku",18,0.15196078431372548,100.0,100.0,1.0,0.0
user-16,202512,3.7,3.7,1,17,17.0,Afternoon Run,1,1.0,1,bbelajar tipis tipis,0.35,1.0,1,13,2,1,20,2,Afternoon Run | bbelajar tipis tipis,4,0.65,61.66666666666667,20.0,0.4083333333333334,41.66666666666667
user-16,202515,9.0,3.0,3,68,22.666666666666668,Afternoon Run | Afternoon Run | Morning Run,1,1.0,1,icip pomokit with learning by doing,0.6,1.0,3,43,4,1,35,6,Afternoon Run | Afternoon Run | Morning Run | icip pomokit with learning by doing,10,1.2285714285714286,100.0,20.0,0.6,80.0
user-17,202511,6.6,3.3,2,84,42.0,Ngabuburit | Afternoon Walk,7,1.0,7,membuat website personal branding | Perbaiki website personal branding | ngedesain cover dan isi buku | benerin web personal branding | pengerjaan buku dll | melanjutkan tugas tugasnya | melanjutkan tugas tugas assesment,1.0,1.0,2,27,4,7,220,19,Ngabuburit | Afternoon Walk | membuat website personal branding | Perbaiki website personal branding | ngedesain cover dan isi buku | benerin web personal branding | pengerjaan buku dll | melanjutkan tugas tugasnya | melanjutkan tugas tugas assesment,23,0.12272727272727273,100.0,100.0,1.0,0.0
user-17,202512,3.8,3.8,1,41,41.0,Afternoon Walk,2,1.0,2,melanjutkan mining mining | melanjutkan yang tadi belum beres,0.45,1.0,1,14,2,2,61,7,Afternoon Walk | melanjutkan mining mining | melanjutkan yang tadi belum beres,9,0.22950819672131148,63.33333333333333,40.0,0.5166666666666666,23.33333333333333
user-17,202519,4.1,4.1,1,58,58.0,Jalan jalan aja,2,1.0,2,mengerjakan tugas tugasnya | mengerjakan saja seperti biasa,0.45,1.0,1,15,2,2,59,7,Jalan jalan aja | mengerjakan tugas tugasnya | mengerjakan saja seperti biasa,9,0.2542372881355932,68.33333333333333,40.0,0.5416666666666666,28.33333333333333
user-17,202520,6.9,3.45,2,102,51.0,Berjalan Pagi | Berjalan Sore,2,1.0,2,Belajar aja besok apa? besok rabu | melengkapi yang belum dilengkapi,0.7,1.0,2,29,4,2,68,10,Berjalan Pagi | Berjalan Sore | Belajar aja besok apa? besok rabu | melengkapi yang belum dilengkapi,14,0.4264705882352941,100.0,40.0,0.7,60.0
user-17,202521,4.2,4.2,1,14,14.0,Jalan jalan,1,1.0,1,ngerjain tugas tugas saja,0.35,1.0,1,11,1,1,25,3,Jalan jalan | ngerjain tugas tugas saja,4,0.44,70.0,20.0,0.45,50.0
user-18,202512,4.1,4.1,1,18,18.0,JJS Minggu Pertama,2,1.0,2,Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan,0.45,1.0,1,18,3,2,86,12,JJS Minggu Pertama | Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan,15,0.20930232558139536,68.33333333333333,40.0,0.5416666666666666,28.33333333333333
user-18,202519,2.2,2.2,1,9,9.0,jalan santai,3,1.0,3,"Mengerjakan Tugas Artikel Medium | Mengerjakan Video Youtube Algoritma Machine Learning; Decision Tree, Information Gain & Entropy, dan Random Forest | Mengerjakan Artikel medium DT dan RF",0.55,1.0,1,12,2,3,188,21,"jalan santai | Mengerjakan Tugas Artikel Medium | Mengerjakan Video Youtube Algoritma Machine Learning; Decision Tree, Information Gain & Entropy, dan Random Forest | Mengerjakan Artikel medium DT dan RF",23,0.06382978723404255,36.66666666666667,60.0,0.****************4,23.33333333333333
user-18,202520,5.0,5.0,1,15,15.0,Berlari Pagi,3,1.0,3,Membuat medium Regresi Linear dan SVM | Mengerjakan Artikel Medium Chapter 1: Memprediksi Kelulusan Siswa dengan Model Decision Tree Menggunakan Python dan Dataset Student Performance | Mengerjakan Video Youtube Chapter 1,0.55,1.0,1,12,2,3,221,26,Berlari Pagi | Membuat medium Regresi Linear dan SVM | Mengerjakan Artikel Medium Chapter 1: Memprediksi Kelulusan Siswa dengan Model Decision Tree Menggunakan Python dan Dataset Student Performance | Mengerjakan Video Youtube Chapter 1,28,0.05429864253393665,83.33333333333334,60.0,0.7166666666666667,23.333333333333343
user-18,202521,2.9,2.9,1,20,20.0,Berlari Sore,3,1.0,3,Mengerjakan medium regresi linear dan svm | Tugas Chapter 2: Mengerjakan Medium Random Forest | Mengerjakan Video Youtube Chapter 2,0.55,1.0,1,12,2,3,131,15,Berlari Sore | Mengerjakan medium regresi linear dan svm | Tugas Chapter 2: Mengerjakan Medium Random Forest | Mengerjakan Video Youtube Chapter 2,17,0.0916030534351145,48.333333333333336,60.0,0.5416666666666667,11.666666666666664
user-18,202522,28.3,14.15,2,107,53.5,Berlari Pagi | Berlari Sore,4,1.0,4,Mengerjakan Medium Chapter 3 | Mengerjakan Video Youtube Chapter 3 | Medium Regresi Linear dan SVM | Mengerjakan Video Youtube Regresi Linear dan SVM,0.9,1.0,2,27,4,4,149,11,Berlari Pagi | Berlari Sore | Mengerjakan Medium Chapter 3 | Mengerjakan Video Youtube Chapter 3 | Medium Regresi Linear dan SVM | Mengerjakan Video Youtube Regresi Linear dan SVM,15,0.18120805369127516,100.0,80.0,0.9,20.0
user-19,202511,9.0,3.0,3,145,48.333333333333336,Afternoon Walk | Morning Walk | Lunch Walk,8,1.0,8,memperbaiki tampilan | melanjutkan tampilan web | mengedit tampilan personal branding | membuat tugas buku | melanjut pembuatan buku | melanjutkan pembuatan buku | melanjutkan website | melanjutkan web personal branding,1.0,1.0,3,42,5,8,219,14,Afternoon Walk | Morning Walk | Lunch Walk | memperbaiki tampilan | melanjutkan tampilan web | mengedit tampilan personal branding | membuat tugas buku | melanjut pembuatan buku | melanjutkan pembuatan buku | melanjutkan website | melanjutkan web personal branding,19,0.1917808219178082,100.0,100.0,1.0,0.0
user-20,202512,4.4,4.4,1,21,21.0,Lari malam,1,1.0,1,kuliah-praktisi-ai-d4-ti,0.35,1.0,1,10,2,1,24,1,Lari malam | kuliah-praktisi-ai-d4-ti,3,0.4166666666666667,73.33333333333334,20.0,0.46666666666666673,53.33333333333334
user-20,202521,17.6,8.8,2,44,22.0,Berlari Pagi | Berlari Siang,4,1.0,4,Mengerjakan Artikel Medium Chapter 01 | Membuat Video Chapter 01 Decision Tree | Mengerjakan Artikel Medium Chapter 02 : Prediksi Keberhasilan Peluncuran Smartphone Baru dengan Random Forest | Membuat Video Chapter 02,0.9,1.0,2,28,4,4,217,20,Berlari Pagi | Berlari Siang | Mengerjakan Artikel Medium Chapter 01 | Membuat Video Chapter 01 Decision Tree | Mengerjakan Artikel Medium Chapter 02 : Prediksi Keberhasilan Peluncuran Smartphone Baru dengan Random Forest | Membuat Video Chapter 02,24,0.12903225806451613,100.0,80.0,0.9,20.0
user-21,202512,6.699999999999999,3.3499999999999996,2,29,14.5,Night Run | Afternoon Walk,2,1.0,2,mau mining mmicrobitcoin | melnjutkan mining mbc,0.7,1.0,2,26,5,2,48,6,Night Run | Afternoon Walk | mau mining mmicrobitcoin | melnjutkan mining mbc,11,0.5416666666666666,100.0,40.0,0.7,60.0
user-21,202513,5.1,5.1,1,19,19.0,Afternoon Run,2,1.0,2,melanjutkan pra assesment | melanjutkan kembali,0.45,1.0,1,13,2,2,47,5,Afternoon Run | melanjutkan pra assesment | melanjutkan kembali,7,0.2765957446808511,85.0,40.0,0.625,45.0
user-21,202518,4.7,4.7,1,19,19.0,Afternoon Ride,1,1.0,1,mengerjakan tugas bimbingan,0.35,1.0,1,14,2,1,27,3,Afternoon Ride | mengerjakan tugas bimbingan,5,0.5185185185185185,78.33333333333333,20.0,0.49166666666666664,58.33333333333333
user-21,202519,8.4,8.4,1,25,25.0,Night Ride,4,1.0,4,mengerjakan bimbingan mingguan | nambah poin pomokit | melanjutkan kembali | ulang bang tadi error,0.65,1.0,1,10,2,4,98,13,Night Ride | mengerjakan bimbingan mingguan | nambah poin pomokit | melanjutkan kembali | ulang bang tadi error,15,0.10204081632653061,100.0,80.0,0.9,20.0
user-21,202520,26.8,13.4,2,94,47.0,Evening Ride | Afternoon Ride,3,1.0,3,mau menambah poin pomokit | melanjutkan tugas bimbingann | melanjutkan tugas yang belum,0.8,1.0,2,29,4,3,87,10,Evening Ride | Afternoon Ride | mau menambah poin pomokit | melanjutkan tugas bimbingann | melanjutkan tugas yang belum,14,0.3333333333333333,100.0,60.0,0.8,40.0
user-21,202521,14.5,7.25,2,45,22.5,Night Ride | Evening Ride,2,1.0,2,mau menambah poin pomokit | menambah point pomokit,0.7,1.0,2,25,4,2,50,6,Night Ride | Evening Ride | mau menambah poin pomokit | menambah point pomokit,10,0.5,100.0,40.0,0.7,60.0
user-21,202522,0.4,0.4,1,2,2.0,Night Ride,4,1.0,4,mau menambahkan poin pomokit | mau nambah poin pomokit | nambah poin pomokiy | nambah poin pomokit lagi,0.65,1.0,1,10,2,4,103,8,Night Ride | mau menambahkan poin pomokit | mau nambah poin pomokit | nambah poin pomokiy | nambah poin pomokit lagi,10,0.0970873786407767,6.666666666666667,80.0,0.43333333333333335,73.33333333333333
user-21,202523,6.2,6.2,1,18,18.0,Afternoon Ride,1,1.0,1,nambah poin pomokit mingguan,0.35,1.0,1,14,2,1,28,4,Afternoon Ride | nambah poin pomokit mingguan,6,0.5,100.0,20.0,0.6,80.0
user-22,202512,8.4,4.2,2,64,32.0,Jalan santai ga sih | Just litte bit walk,2,1.0,2,belajar chapter 01 modul python | Kuliah Praktisi D4 TI Kecerdasan Buatan - AI Predicting The Future - Bagus Rully Muttaqien,0.7,1.0,2,41,9,2,124,20,Jalan santai ga sih | Just litte bit walk | belajar chapter 01 modul python | Kuliah Praktisi D4 TI Kecerdasan Buatan - AI Predicting The Future - Bagus Rully Muttaqien,29,0.33064516129032256,100.0,40.0,0.7,60.0
user-22,202519,7.7,7.7,1,40,40.0,Jalan santai anjayy,4,1.0,4,"Membuat artikel medium Decision Tree Information Gain & Entropy random forest | Membuat video Youtube Memahami Decision Tree, Entropy & Information Gain, dan Random Forest | Membuat Tugas Artikel Decision Tree, Entropy & Information Gain, dan Random Forest | Artikel Decision Tree, Entropy & Information Gain, dan Random Forest",0.65,1.0,1,19,3,4,327,19,"Jalan santai anjayy | Membuat artikel medium Decision Tree Information Gain & Entropy random forest | Membuat video Youtube Memahami Decision Tree, Entropy & Information Gain, dan Random Forest | Membuat Tugas Artikel Decision Tree, Entropy & Information Gain, dan Random Forest | Artikel Decision Tree, Entropy & Information Gain, dan Random Forest",22,0.0581039755351682,100.0,80.0,0.9,20.0
user-22,202520,15.5,15.5,1,46,46.0,Long mars,2,1.0,2,Membuat Artikel Medium Chapter 01 : Belajar Prediksi Sentimen Film dengan Decision Tree Berdasarkan Dataset IMDB Movie Reviews | Mengerjakan video Youtube chapter 1,0.45,1.0,1,9,2,2,164,23,Long mars | Membuat Artikel Medium Chapter 01 : Belajar Prediksi Sentimen Film dengan Decision Tree Berdasarkan Dataset IMDB Movie Reviews | Mengerjakan video Youtube chapter 1,25,0.054878048780487805,100.0,40.0,0.7,60.0
user-22,202521,21.8,21.8,1,79,79.0,Jalan hujan hujanan ke kampus 🤣,2,1.0,2,Tugas Chapter 2: Mengerjakan Medium Random Forest | Mengerjakan Video Youtube Chapter 02,0.45,1.0,1,31,6,2,88,11,Jalan hujan hujanan ke kampus 🤣 | Tugas Chapter 2: Mengerjakan Medium Random Forest | Mengerjakan Video Youtube Chapter 02,17,0.3522727272727273,100.0,40.0,0.7,60.0
user-22,202522,35.8,17.9,2,109,54.5,Long mars | Walk i thinkn,2,1.0,2,mengerjakan artikel medium chapter03 | mengerjakan video Youtube chapter 03,0.7,1.0,2,25,6,2,75,9,Long mars | Walk i thinkn | mengerjakan artikel medium chapter03 | mengerjakan video Youtube chapter 03,15,0.3333333333333333,100.0,40.0,0.7,60.0
user-23,202522,3.1,3.1,1,14,14.0,run 2,2,1.0,2,"Membuat struct json response dan koneksi golang dengan postman beserta tutorialnya di medium | Membuat struct, function, c.JSON dan koneksi ke Postman beserta membuat video tutorial YT nya",0.45,1.0,1,5,2,2,188,22,"run 2 | Membuat struct json response dan koneksi golang dengan postman beserta tutorialnya di medium | Membuat struct, function, c.JSON dan koneksi ke Postman beserta membuat video tutorial YT nya",24,0.026595744680851064,51.66666666666667,40.0,0.45833333333333337,11.666666666666671
user-24,202519,4.5,4.5,1,46,46.0,Jalan lari,2,1.0,2,Membuat tutorial YOutube helloworld CLI dan Gofiber | Membuat Tutorial hello world menggunakan bahasa go dengan gofiber melalui medium,0.45,1.0,1,10,2,2,134,16,Jalan lari | Membuat tutorial YOutube helloworld CLI dan Gofiber | Membuat Tutorial hello world menggunakan bahasa go dengan gofiber melalui medium,18,0.07462686567164178,75.0,40.0,0.575,35.0
user-24,202520,9.8,4.9,2,101,50.5,Sip | Jalan cepat,1,1.0,1,Membut tutorial Youtube Helloworld dengan GOlang dan Gofiber,0.6,1.0,2,17,4,1,60,8,Sip | Jalan cepat | Membut tutorial Youtube Helloworld dengan GOlang dan Gofiber,12,0.2833333333333333,100.0,20.0,0.6,80.0
user-24,202521,5.2,5.2,1,49,49.0,Joging santai,1,1.0,1,membuat tutorial helloworld menggunakan golang dan gofiber di medium,0.35,1.0,1,13,2,1,68,9,Joging santai | membuat tutorial helloworld menggunakan golang dan gofiber di medium,11,0.19117647058823528,86.66666666666667,20.0,0.5333333333333333,66.66666666666667
user-24,202522,11.0,11.0,1,146,146.0,Jalan,2,1.0,2,"Membuat Struct, fungsi, return c.Json, serta menghubungkan postman dalam Golang | Tutorial YOutube Struct,func,returnjson, dan postman",0.45,1.0,1,5,1,2,134,15,"Jalan | Membuat Struct, fungsi, return c.Json, serta menghubungkan postman dalam Golang | Tutorial YOutube Struct,func,returnjson, dan postman",16,0.03731343283582089,100.0,40.0,0.7,60.0
user-25,202511,8.1,4.05,2,78,39.0,aftaroon walk1 | MediumRun,7,1.0,7,Melanjutkan perbaikan website | melanjutkan proses | melanjutkan website mengubah tampilan menjadi responsif | melanjutkan website dan mengerjakan tugas lainnya | melanjutkan tugas pra-assesment | melanjutkan tugas assesment | melanjutkan tugas assesment,1.0,1.0,2,26,4,7,254,15,aftaroon walk1 | MediumRun | Melanjutkan perbaikan website | melanjutkan proses | melanjutkan website mengubah tampilan menjadi responsif | melanjutkan website dan mengerjakan tugas lainnya | melanjutkan tugas pra-assesment | melanjutkan tugas assesment | melanjutkan tugas assesment,19,0.10236220472440945,100.0,100.0,1.0,0.0
user-25,202522,7.3999999999999995,3.6999999999999997,2,28,14.0,berlari sore pergi | berlari sore pulang,2,1.0,2,menyelesaikan yang belum | apa yang dikerjakan,0.7,1.0,2,40,5,2,46,6,berlari sore pergi | berlari sore pulang | menyelesaikan yang belum | apa yang dikerjakan,11,0.8695652173913043,100.0,40.0,0.7,60.0
user-26,202512,6.4,3.2,2,37,18.5,Evening Run | Afternoon Run,1,1.0,1,Student Perfomance Python - Artificial Intelligence,0.6,1.0,2,27,4,1,51,6,Evening Run | Afternoon Run | Student Perfomance Python - Artificial Intelligence,10,0.5294117647058824,100.0,20.0,0.6,80.0
user-26,202519,2.6,2.6,1,15,15.0,Berlari Petang,4,1.0,4,"Mengerjakan Artikel & Youtube Decision Tree, Random Forest | Tugas Youtube Decission Tree & Random Forest | Mengerjakan Artikel Decission Tree & Random Forest (Medium) | Tugas Youtube Decission Tree & Random Forest",0.65,1.0,1,14,2,4,214,13,"Berlari Petang | Mengerjakan Artikel & Youtube Decision Tree, Random Forest | Tugas Youtube Decission Tree & Random Forest | Mengerjakan Artikel Decission Tree & Random Forest (Medium) | Tugas Youtube Decission Tree & Random Forest",15,0.06542056074766354,43.333333333333336,80.0,0.6166666666666667,36.666666666666664
user-26,202520,2.8,2.8,1,46,46.0,Berjalan Pagi,1,1.0,1,Medium - Regresi Linier,0.35,1.0,1,13,2,1,23,4,Berjalan Pagi | Medium - Regresi Linier,6,0.5652173913043478,46.666666666666664,20.0,0.33333333333333326,26.666666666666664
user-26,202521,6.3,3.15,2,15,7.5,Berjalan Petang | Berjalan Petang,2,1.0,2,Chapter 1 - Medium | Chapter 1 - Youtube,0.7,1.0,2,33,3,2,40,6,Berjalan Petang | Berjalan Petang | Chapter 1 - Medium | Chapter 1 - Youtube,9,0.825,100.0,40.0,0.7,60.0
user-26,202522,3.5,3.5,1,33,33.0,Berlari Pagi,2,1.0,2,Chapter 2 - Medium | Chapter 2 - Youtube,0.45,1.0,1,12,2,2,40,6,Berlari Pagi | Chapter 2 - Medium | Chapter 2 - Youtube,8,0.3,58.333333333333336,40.0,0.4916666666666667,18.333333333333336
user-27,202512,3.1,3.1,1,23,23.0,Afternoon Run,2,1.0,2,mengerjakan Chapter01 modul AI | Kuliah Praktisi Kecerdasan Buatan - AI Predicting The Future,0.45,1.0,1,13,2,2,93,13,Afternoon Run | mengerjakan Chapter01 modul AI | Kuliah Praktisi Kecerdasan Buatan - AI Predicting The Future,15,0.13978494623655913,51.66666666666667,40.0,0.45833333333333337,11.666666666666671
user-27,202519,3.2,3.2,1,10,10.0,Berlari Sore,4,1.0,4,Membuat artikel medium Decision Tree Information Gain & Entropy Random Forest | Tugas Youtube Decission Tree & Random Forest | Tugas Youtube Decission Tree & Random Forest | Membuat Artikel Medium Decission Tree & Random Forest,0.65,1.0,1,12,2,4,227,15,Berlari Sore | Membuat artikel medium Decision Tree Information Gain & Entropy Random Forest | Tugas Youtube Decission Tree & Random Forest | Tugas Youtube Decission Tree & Random Forest | Membuat Artikel Medium Decission Tree & Random Forest,17,0.05286343612334802,53.333333333333336,80.0,0.6666666666666667,26.666666666666664
user-27,202521,5.8,2.9,2,29,14.5,Berlari Siang | Berlari Malam,4,1.0,4,Artikel Tugas 1 Regresi Linear | Video Youtube Tugas 1 Regresi Linear | Artikel Chapter 01 | Youtube Chapter 01,0.9,1.0,2,29,4,4,111,10,Berlari Siang | Berlari Malam | Artikel Tugas 1 Regresi Linear | Video Youtube Tugas 1 Regresi Linear | Artikel Chapter 01 | Youtube Chapter 01,14,0.26126126126126126,96.66666666666667,80.0,0.8833333333333334,16.66666666666667
user-27,202523,3.7,3.7,1,23,23.0,Berlari Malam,4,1.0,4,Medium Random Forest Chapter 02 | Youtube Random Forest Chapter 02 | Artikel Medium Chapter 03 | Youtube Chapter 03,0.65,1.0,1,13,2,4,115,9,Berlari Malam | Medium Random Forest Chapter 02 | Youtube Random Forest Chapter 02 | Artikel Medium Chapter 03 | Youtube Chapter 03,11,0.11304347826086956,61.66666666666667,80.0,0.7083333333333335,18.33333333333333
user-28,202511,6.699999999999999,3.3499999999999996,2,98,49.0,Afternoon Walk | Afternoon Walk,4,1.0,4,membuat web personal brending | lanjutkan pembuatan web | melanjutkan kembaki | melanjutkan kembali,0.9,1.0,2,31,3,4,99,10,Afternoon Walk | Afternoon Walk | membuat web personal brending | lanjutkan pembuatan web | melanjutkan kembaki | melanjutkan kembali,13,0.31313131313131315,100.0,80.0,0.9,20.0
user-29,202511,4.6,4.6,1,25,25.0,tugas,1,1.0,1,"melakukan pretest AI, dang mengerjakan tampilan card profile",0.35,1.0,1,5,1,1,60,8,"tugas | melakukan pretest AI, dang mengerjakan tampilan card profile",9,0.08333333333333333,76.66666666666666,20.0,0.****************,56.66666666666666
user-29,202521,11.899999999999999,3.9666666666666663,3,81,27.0,Evening Walk | Afternoon Walk | Evening Walk,1,1.0,1,Membuat medium untuk tugas chapter 1 decision tree,0.6,1.0,3,44,4,1,50,8,Evening Walk | Afternoon Walk | Evening Walk | Membuat medium untuk tugas chapter 1 decision tree,12,0.88,100.0,20.0,0.6,80.0
user-30,202512,12.2,4.066666666666666,3,70,23.333333333333332,Afternoon Walk | Evening Walk | Evening Walk,2,1.0,2,mencoba pomokit 0.2.3 | melanjutkan mining,0.7,1.0,3,44,4,2,42,6,Afternoon Walk | Evening Walk | Evening Walk | mencoba pomokit 0.2.3 | melanjutkan mining,10,1.0476190476190477,100.0,40.0,0.7,60.0
user-30,202522,3.0,3.0,1,24,24.0,Berjalan Sore,1,1.0,1,melanjutkan pomodoro,0.35,1.0,1,13,2,1,20,2,Berjalan Sore | melanjutkan pomodoro,4,0.65,50.0,20.0,0.35,30.0
user-31,202520,13.8,6.9,2,22,11.0,Night Run | Night Ride,2,1.0,2,"Mengerjakan Tugas 2: Decision Tree, Entropy, Information Gain, dan Random Forest | Membuat Video Youtube Membahas ""Menjelajahi Decision Tree: Dari Entropy hingga Random Forest""",0.7,1.0,2,22,4,2,176,22,"Night Run | Night Ride | Mengerjakan Tugas 2: Decision Tree, Entropy, Information Gain, dan Random Forest | Membuat Video Youtube Membahas ""Menjelajahi Decision Tree: Dari Entropy hingga Random Forest""",26,0.125,100.0,40.0,0.7,60.0
user-31,202521,6.5,6.5,1,23,23.0,Sepeda Sabtu Sore,2,1.0,2,Tugas 3 Chapter 1: Langkah-langkah Implementasi Algoritma Decision Tree dalam Rekomendasi Produk Sederhana | Tugas 3 Chapter 1: Video Langkah-langkah Implementasi Algoritma Decision Tree dalam Rekomendasi Produk Sederhana,0.45,1.0,1,17,3,2,221,15,Sepeda Sabtu Sore | Tugas 3 Chapter 1: Langkah-langkah Implementasi Algoritma Decision Tree dalam Rekomendasi Produk Sederhana | Tugas 3 Chapter 1: Video Langkah-langkah Implementasi Algoritma Decision Tree dalam Rekomendasi Produk Sederhana,18,0.07692307692307693,100.0,40.0,0.7,60.0
user-31,202522,7.0,1.75,4,78,19.5,Jalan 4-1 | Jalan 4-2 | Jalan 4-3 | Jogging 4-1,2,1.0,2,"Chapter 2: Artikel Medium, ""Penerapan Sistem Rekomendasi Produk E-Commerce Berbasis Model Machine Learning Menggunakan Algoritma Random Forest"" | Tugas 4 Chapter 2: Video YouTube, ""Penerapan Sistem Rekomendasi Produk E-Commerce Berbasis Model Machine Learning Menggunakan Algoritma Random Forest""",0.7,1.0,4,47,6,2,296,22,"Jalan 4-1 | Jalan 4-2 | Jalan 4-3 | Jogging 4-1 | Chapter 2: Artikel Medium, ""Penerapan Sistem Rekomendasi Produk E-Commerce Berbasis Model Machine Learning Menggunakan Algoritma Random Forest"" | Tugas 4 Chapter 2: Video YouTube, ""Penerapan Sistem Rekomendasi Produk E-Commerce Berbasis Model Machine Learning Menggunakan Algoritma Random Forest""",28,0.15878378378378377,100.0,40.0,0.7,60.0
user-31,202523,6.0,1.5,4,57,14.25,Jogging 5-1 | Jalan 5-4 | Jalan 5-1 | Jalan 5-3,2,1.0,2,"Tugas 5 Chapter 3: Artikel Medium, ""Analisis Sentimen Review Produk Amazon: Implementasi Bag of Word (BoW) dalam Pemodelan Machine Learning sebagai Text Vectorization Techniques"" | Tugas 5 Chapter 3: Video YouTube, ""Analisis Sentimen Review Produk Amazon: Implementasi Bag of Word (BoW) dalam Pemodelan Machine Learning sebagai Text Vectorization Techniques""",0.7,1.0,4,47,6,2,358,27,"Jogging 5-1 | Jalan 5-4 | Jalan 5-1 | Jalan 5-3 | Tugas 5 Chapter 3: Artikel Medium, ""Analisis Sentimen Review Produk Amazon: Implementasi Bag of Word (BoW) dalam Pemodelan Machine Learning sebagai Text Vectorization Techniques"" | Tugas 5 Chapter 3: Video YouTube, ""Analisis Sentimen Review Produk Amazon: Implementasi Bag of Word (BoW) dalam Pemodelan Machine Learning sebagai Text Vectorization Techniques""",33,0.13128491620111732,100.0,40.0,0.7,60.0
user-32,202511,7.8,3.9,2,123,61.5,Ngabuburit | Ngabuburit part 2,7,1.0,7,Tambah Project Pages dan Image nya | Tambah List Ongoing Project + Fix Bug | Mining Untuk Progress proyek 1 | Lanjutkan Website dan Mining Lagi | lanjut mining bruh | mining ambek turu | Lanjut Mining Sambil di tinggal tidur,1.0,1.0,2,30,4,7,224,28,Ngabuburit | Ngabuburit part 2 | Tambah Project Pages dan Image nya | Tambah List Ongoing Project + Fix Bug | Mining Untuk Progress proyek 1 | Lanjutkan Website dan Mining Lagi | lanjut mining bruh | mining ambek turu | Lanjut Mining Sambil di tinggal tidur,32,0.13392857142857142,100.0,100.0,1.0,0.0
user-32,202512,7.1,3.55,2,112,56.0,Ngabuburit part 3 | Part4,2,1.0,2,Lanjut Mining Sama website dikit | Lanjut Mengerjakan website,0.7,1.0,2,25,5,2,61,7,Ngabuburit part 3 | Part4 | Lanjut Mining Sama website dikit | Lanjut Mengerjakan website,12,0.4098360655737705,100.0,40.0,0.7,60.0
user-33,202511,6.1,3.05,2,67,33.5,cari takjil ke setdut | easy run,8,1.0,8,"Perbaharuan Web Personal Branding | Melanjutkan Portofolio | revisi navbar dan tambah | penambahan untuk assets about | Mining tambahan untuk html | mining website personal | tambahan css,html, dll | penambahan skills dan bukup php",1.0,1.0,2,32,7,8,231,24,"cari takjil ke setdut | easy run | Perbaharuan Web Personal Branding | Melanjutkan Portofolio | revisi navbar dan tambah | penambahan untuk assets about | Mining tambahan untuk html | mining website personal | tambahan css,html, dll | penambahan skills dan bukup php",31,0.13852813852813853,100.0,100.0,1.0,0.0
user-33,202522,23.7,7.8999999999999995,3,51,17.0,Lari malamm | Lari siang | Lari laris,4,1.0,4,mau belajaar aja yaa | mauu mau belajar belajar | mau otak atik sebentar | mau belajar ajahh deh,0.9,1.0,3,37,5,4,96,12,Lari malamm | Lari siang | Lari laris | mau belajaar aja yaa | mauu mau belajar belajar | mau otak atik sebentar | mau belajar ajahh deh,17,0.3854166666666667,100.0,80.0,0.9,20.0
user-34,202511,7.1,3.55,2,129,64.5,Night Walk | Night Walk,4,1.0,4,membuat web personal branding | melanjutkan yg sebelumnya | melanjutkan website personal branding | melanjutkan website portofolio,0.9,1.0,2,23,3,4,130,10,Night Walk | Night Walk | membuat web personal branding | melanjutkan yg sebelumnya | melanjutkan website personal branding | melanjutkan website portofolio,13,0.17692307692307693,100.0,80.0,0.9,20.0
user-35,202521,8.2,8.2,1,115,115.0,Morning Run,2,1.0,2,membuat tutorial medium hellow word dengan go fiber | mengerjakan golan tungas mingguan,0.45,1.0,1,11,2,2,87,13,Morning Run | membuat tutorial medium hellow word dengan go fiber | mengerjakan golan tungas mingguan,15,0.12643678160919541,100.0,40.0,0.7,60.0
user-35,202522,9.1,9.1,1,60,60.0,Afternoon Run,2,1.0,2,membuat struk dan fungsi | membuat struk dan fungsi pada golang,0.45,1.0,1,13,2,2,63,7,Afternoon Run | membuat struk dan fungsi | membuat struk dan fungsi pada golang,9,0.20634920634920634,100.0,40.0,0.7,60.0
user-36,202511,6.0,3.0,2,104,52.0,Night Walk | Evening Walk,3,1.0,3,buat website personal branding | melanjutkan projek sebelumnya | melanjutkan project,0.8,1.0,2,25,4,3,84,9,Night Walk | Evening Walk | buat website personal branding | melanjutkan projek sebelumnya | melanjutkan project,13,0.2976190476190476,100.0,60.0,0.8,40.0
user-37,202512,8.8,4.4,2,39,19.5,Evening Run | Evening Run,1,1.0,1,Kuliah Praktisi D4 TI Kecerdasan Buatan AI Predicting The Future bersama Bagus Rully Muttaqien,0.6,1.0,2,25,3,1,94,14,Evening Run | Evening Run | Kuliah Praktisi D4 TI Kecerdasan Buatan AI Predicting The Future bersama Bagus Rully Muttaqien,17,0.26595744680851063,100.0,20.0,0.6,80.0
user-37,202519,14.1,7.05,2,49,24.5,Berlari Sore | Berlari Sore,3,1.0,3,"Membuat artikel Decision Tree, Information Gain & Entropy dalam Random Forest | Membuat artikel Decision Tree, Information Gain & Entropy dalam Random Forest | membuat video tutorial di yt",0.8,1.0,2,27,3,3,188,16,"Berlari Sore | Berlari Sore | Membuat artikel Decision Tree, Information Gain & Entropy dalam Random Forest | Membuat artikel Decision Tree, Information Gain & Entropy dalam Random Forest | membuat video tutorial di yt",19,0.14361702127659576,100.0,60.0,0.8,40.0
user-37,202521,13.9,6.95,2,39,19.5,Berlari Sore | Berlari Sore,4,1.0,4,Chapter 1 membuat artikel | Membuat video youtube Decision Tree Chapter 1 | Membuat artikel Chapter 2 | Membuat video youtube chapter 2,0.9,1.0,2,27,3,4,135,10,Berlari Sore | Berlari Sore | Chapter 1 membuat artikel | Membuat video youtube Decision Tree Chapter 1 | Membuat artikel Chapter 2 | Membuat video youtube chapter 2,13,0.2,100.0,80.0,0.9,20.0
user-37,202522,5.8,5.8,1,20,20.0,Berlari Sore,2,1.0,2,Mengerjakan artikel chapter 3 | Membuat video youtube chapter 3,0.45,1.0,1,12,2,2,63,8,Berlari Sore | Mengerjakan artikel chapter 3 | Membuat video youtube chapter 3,10,0.19047619047619047,96.66666666666667,40.0,0.6833333333333335,56.66666666666667
user-38,202520,3.0,3.0,1,31,31.0,Berlari Sore,2,1.0,2,mengumpulkan tugas di yotube membuat hello world di go fiber | upload link medium tugas gofiber,0.45,1.0,1,12,2,2,95,14,Berlari Sore | mengumpulkan tugas di yotube membuat hello world di go fiber | upload link medium tugas gofiber,16,0.12631578947368421,50.0,40.0,0.45,10.0
user-38,202522,2.3,2.3,1,15,15.0,Berlari Sore,2,1.0,2,membuat fungsi struct di golang | membuat tutorial golang di medium,0.45,1.0,1,12,2,2,67,8,Berlari Sore | membuat fungsi struct di golang | membuat tutorial golang di medium,10,0.1791044776119403,38.33333333333333,40.0,0.39166666666666666,1.6666666666666714
user-39,202521,8.1,4.05,2,134,67.0,Jalan/Lari malam hari sambil cari spot terbaik di Bandung. | Olahraga malam. Serta melatih mental O.R.S,2,1.0,2,"Upload tugas membuat ""Hello World"" Golang GoFiber di YouTube | Mengumpulkan Tugas 2 Judul ""Belajar Web Service CRUD dengan Golang dan MongoDB"" di artikel medium.com",0.7,1.0,2,103,16,2,164,22,"Jalan/Lari malam hari sambil cari spot terbaik di Bandung. | Olahraga malam. Serta melatih mental O.R.S | Upload tugas membuat ""Hello World"" Golang GoFiber di YouTube | Mengumpulkan Tugas 2 Judul ""Belajar Web Service CRUD dengan Golang dan MongoDB"" di artikel medium.com",38,0.6280487804878049,100.0,40.0,0.7,60.0
user-40,202512,17.5,5.833333333333333,3,174,58.0,Lari santai | Lari Pagi | Jalan pagi lagi,1,1.0,1,Chapter-1 mengerjakan student performance,0.6,1.0,3,41,6,1,41,4,Lari santai | Lari Pagi | Jalan pagi lagi | Chapter-1 mengerjakan student performance,10,1.0,100.0,20.0,0.6,80.0
user-40,202520,28.8,7.2,4,374,93.5,Jalan Marathon | Berjalan Pagi | Berjalan Pagi | Berlari Pagi,3,1.0,3,"Membuat Artikel Medium Decision Tree, Information Gain, dan Random Forest | Membuat artikel medium regressi linear | Mengerjakan Video Youtube Decision Tree, random Forest dan Information Gain",0.8,1.0,4,61,6,3,192,17,"Jalan Marathon | Berjalan Pagi | Berjalan Pagi | Berlari Pagi | Membuat Artikel Medium Decision Tree, Information Gain, dan Random Forest | Membuat artikel medium regressi linear | Mengerjakan Video Youtube Decision Tree, random Forest dan Information Gain",23,0.3177083333333333,100.0,60.0,0.8,40.0
user-40,202521,3.1,3.1,1,39,39.0,Berjalan Petang,4,1.0,4,Mengerjakan Artikel Medium Chapter 1 | Membuat Video Artikel Medium Regresi Linear | Membuat Video Chapter 1 | Mengerjakan artikel medium regresi linear,0.65,1.0,1,15,2,4,152,10,Berjalan Petang | Mengerjakan Artikel Medium Chapter 1 | Membuat Video Artikel Medium Regresi Linear | Membuat Video Chapter 1 | Mengerjakan artikel medium regresi linear,12,0.09868421052631579,51.66666666666667,80.0,0.6583333333333334,28.33333333333333
user-40,202522,27.2,9.066666666666666,3,413,137.66666666666666,Jalan Seru | Berjalan Pagi | Berjalan Sore,18,1.0,18,"Mengerjakan artikel medium chapter 2 | Decision Tree, Random Forest, Information Gain | Mengerjakan Video Decision Tree,Information Gain, dan Random Forest | Mengerjakan Video Decision Tree, Information Gain, dan Random Forest | Mengerjakan artikel medium | Mengerjakan medium Chapter 01 | Mengerjakan Video Chapter 01 | Mengerjakan Artikel Medium Chapter 01 | Mengerjakan video chapter 01 | Mengerjakan medium chapter 01 | Mengerjakan artikel medium chapter 02 | Mengerjakan Artikel Medium Chapter 02 | Mengerjakan Artikel Medium Chapter 02 | Mengerjakan Video Artikel Chapter 02 | Mengerjakan Video Chapter 02 | Mengerjakan Artikel Medium Chapter 03 | Mengerjakan Artikel Medium Chapter 03 | Mengerjakan Artikel Medium Chapter 03",1.0,1.0,3,42,6,18,731,20,"Jalan Seru | Berjalan Pagi | Berjalan Sore | Mengerjakan artikel medium chapter 2 | Decision Tree, Random Forest, Information Gain | Mengerjakan Video Decision Tree,Information Gain, dan Random Forest | Mengerjakan Video Decision Tree, Information Gain, dan Random Forest | Mengerjakan artikel medium | Mengerjakan medium Chapter 01 | Mengerjakan Video Chapter 01 | Mengerjakan Artikel Medium Chapter 01 | Mengerjakan video chapter 01 | Mengerjakan medium chapter 01 | Mengerjakan artikel medium chapter 02 | Mengerjakan Artikel Medium Chapter 02 | Mengerjakan Artikel Medium Chapter 02 | Mengerjakan Video Artikel Chapter 02 | Mengerjakan Video Chapter 02 | Mengerjakan Artikel Medium Chapter 03 | Mengerjakan Artikel Medium Chapter 03 | Mengerjakan Artikel Medium Chapter 03",26,0.057455540355677154,100.0,100.0,1.0,0.0
user-41,202511,4.2,4.2,1,18,18.0,Jalan pulang kampus,4,1.0,4,"memperbaiki web dan maining | meneruskan web personal branding | melanjutkan web personal branding | meneruskan tugas,maining",0.65,1.0,1,19,3,4,125,10,"Jalan pulang kampus | memperbaiki web dan maining | meneruskan web personal branding | melanjutkan web personal branding | meneruskan tugas,maining",13,0.152,70.0,80.0,0.75,10.0
user-41,202512,4.0,4.0,1,31,31.0,Ngabuburit,6,1.0,6,"gali maining sampe milyader | melanjutkan tugas-tugas,menyelesaikan buku | mencoba yang belum pernah dicoba | melanjutkan yang belum terselesaikan | pomodoro dulu tipis-tipis | cek e kartu digital menggunakan gtmetrix",0.75,1.0,1,10,1,6,217,23,"Ngabuburit | gali maining sampe milyader | melanjutkan tugas-tugas,menyelesaikan buku | mencoba yang belum pernah dicoba | melanjutkan yang belum terselesaikan | pomodoro dulu tipis-tipis | cek e kartu digital menggunakan gtmetrix",24,0.04608294930875576,66.66666666666666,100.0,0.8333333333333333,33.33333333333334
user-41,202520,2.8,2.8,1,18,18.0,Lapar,2,1.0,2,melanjutkan seperti biasa | lanjut sampe mampus,0.45,1.0,1,5,1,2,47,7,Lapar | melanjutkan seperti biasa | lanjut sampe mampus,8,0.10638297872340426,46.666666666666664,40.0,0.4333333333333333,6.666666666666664
user-41,202521,12.7,6.35,2,48,24.0,Lari dari kenyataan | Cape,4,1.0,4,melanjutkan tugas mingguan | melnajutkan tugas mingguan | melanjutkan tugas mingguan | lanjut maining sampe jebollllll,0.9,1.0,2,26,5,4,118,9,Lari dari kenyataan | Cape | melanjutkan tugas mingguan | melnajutkan tugas mingguan | melanjutkan tugas mingguan | lanjut maining sampe jebollllll,14,0.22033898305084745,100.0,80.0,0.9,20.0
user-41,202522,19.6,19.6,1,30,30.0,Ngawadul,3,1.0,3,melanjutkan tugas proyek 1 | menaljutkan tugas mingguan proyek 1 | melanjutkan tugas proyek 1,0.55,1.0,1,8,1,3,93,7,Ngawadul | melanjutkan tugas proyek 1 | menaljutkan tugas mingguan proyek 1 | melanjutkan tugas proyek 1,8,0.08602150537634409,100.0,60.0,0.8,40.0
user-41,202523,2.7,2.7,1,7,7.0,Terlalu cepet,1,1.0,1,"tugas mingguan,menambahkan poto",0.35,1.0,1,13,2,1,31,3,"Terlalu cepet | tugas mingguan,menambahkan poto",5,0.41935483870967744,45.0,20.0,0.325,25.0
user-42,202511,6.1,3.05,2,36,18.0,Afternoon Walk | Afternoon Walk,5,1.0,5,merapihkan website | menambahkan tampilan | melanjutkan website | melanjutkan pembuatan portofolio | finisshing desain waebsite,1.0,1.0,2,31,3,5,127,11,Afternoon Walk | Afternoon Walk | merapihkan website | menambahkan tampilan | melanjutkan website | melanjutkan pembuatan portofolio | finisshing desain waebsite,14,0.2440944881889764,100.0,100.0,1.0,0.0
user-42,202512,16.3,8.15,2,97,48.5,Evening Walk | Afternoon Walk,3,1.0,3,mngerjakan web client | bismillah ngulang tadi | menambahkan web untuk cust,0.8,1.0,2,29,4,3,75,10,Evening Walk | Afternoon Walk | mngerjakan web client | bismillah ngulang tadi | menambahkan web untuk cust,14,0.38666666666666666,100.0,60.0,0.8,40.0
user-43,202511,3.1,3.1,1,41,41.0,Jalan sore,7,1.0,7,"mining, buat website, sambungin webhook, dll | mengerjakan tugas poin ke 2 dan selanjutnya | mengerjakan tugas pra assesment | mengerjakan tugas asesment | melanjutkan tugas asesment | mengerjakan tugas asesment | mengerjakan tugas asesment",0.75,1.0,1,10,2,7,240,18,"Jalan sore | mining, buat website, sambungin webhook, dll | mengerjakan tugas poin ke 2 dan selanjutnya | mengerjakan tugas pra assesment | mengerjakan tugas asesment | melanjutkan tugas asesment | mengerjakan tugas asesment | mengerjakan tugas asesment",20,0.041666666666666664,51.66666666666667,100.0,0.7583333333333334,48.33333333333333
user-43,202512,3.4,3.4,1,16,16.0,Evening Run,5,1.0,5,mengerjakan tugas asesment 1 | melanjutkan pekerjaan | melanjutkan tugas assesment | melanjutkan pekerjaan yg belum | melanjutkan proses buku ke tahap selanjutnya,0.75,1.0,1,11,2,5,162,15,Evening Run | mengerjakan tugas asesment 1 | melanjutkan pekerjaan | melanjutkan tugas assesment | melanjutkan pekerjaan yg belum | melanjutkan proses buku ke tahap selanjutnya,17,0.06790123456790123,56.666666666666664,100.0,0.7833333333333333,43.333333333333336
user-43,202513,15.0,5.0,3,75,25.0,Morning Run | Cari takjil | Afternoon Run,3,1.0,3,"mining, buku,menyelesaikan yg belum selesai | memperbaiki yang salah | mengubah, menghapus, dan mengganti tampilan web",0.8,1.0,3,41,6,3,118,15,"Morning Run | Cari takjil | Afternoon Run | mining, buku,menyelesaikan yg belum selesai | memperbaiki yang salah | mengubah, menghapus, dan mengganti tampilan web",21,0.3474576271186441,100.0,60.0,0.8,40.0
user-43,202518,6.6,6.6,1,26,26.0,Bersepeda Sore,2,1.0,2,Mengerjakan tugas bimbingan minggu ini | Melengkapi penilaian bimbingan 1 cycle ke 2,0.45,1.0,1,14,2,2,84,12,Bersepeda Sore | Mengerjakan tugas bimbingan minggu ini | Melengkapi penilaian bimbingan 1 cycle ke 2,14,0.16666666666666666,100.0,40.0,0.7,60.0
user-43,202519,8.0,2.0,4,95,23.75,tracking gn | Berlari Pagi | Berjalan Siang | Berjalan Siang,5,1.0,5,Menyelesaikan penilaian pomokit | Memenuhi nilai pomokit | Memenuhi nilai pomokit | Memenuhi nilai yg belum lengkap | Mengerjakan pomokit untuk melengkapi penilaian,1.0,1.0,4,60,7,5,164,12,tracking gn | Berlari Pagi | Berjalan Siang | Berjalan Siang | Menyelesaikan penilaian pomokit | Memenuhi nilai pomokit | Memenuhi nilai pomokit | Memenuhi nilai yg belum lengkap | Mengerjakan pomokit untuk melengkapi penilaian,19,0.36585365853658536,100.0,100.0,1.0,0.0
user-43,202521,15.6,3.9,4,55,13.75,Bersepeda Sore | Bersepeda Sore | Bersepeda Sore | Bersepeda Sore,3,1.0,3,memenuhi penilaian pomokit | merapihkan website | merapihkan website,0.8,1.0,4,65,3,3,68,6,Bersepeda Sore | Bersepeda Sore | Bersepeda Sore | Bersepeda Sore | memenuhi penilaian pomokit | merapihkan website | merapihkan website,9,0.9558823529411765,100.0,60.0,0.8,40.0
user-43,202522,10.4,10.4,1,27,27.0,Bersepeda Sore,3,1.0,3,"menambah nilai pomokittt | mengubah, menambah, melengkapi tugas proyek 1 | menambah poin pomokit",0.55,1.0,1,14,2,3,96,12,"Bersepeda Sore | menambah nilai pomokittt | mengubah, menambah, melengkapi tugas proyek 1 | menambah poin pomokit",14,0.14583333333333334,100.0,60.0,0.8,40.0
user-43,202523,9.3,9.3,1,25,25.0,Bersepeda Pagi,2,1.0,2,menambah nilai proyek 1 | menambah nilai pomokit,0.45,1.0,1,14,2,2,48,6,Bersepeda Pagi | menambah nilai proyek 1 | menambah nilai pomokit,8,0.2916666666666667,100.0,40.0,0.7,60.0
user-44,202512,6.9,6.9,1,484,484.0,Berlari Sore,1,1.0,1,Kuliah Praktisi Kecerdasan Buatan,0.35,1.0,1,12,2,1,33,4,Berlari Sore | Kuliah Praktisi Kecerdasan Buatan,6,0.36363636363636365,100.0,20.0,0.6,80.0
user-44,202520,4.3,4.3,1,86,86.0,Berlari Sore,3,1.0,3,Mengerjakan artikel medium chapter 1 | Chapter1 Membuat Video YT | Mengerjakan Tes IQ,0.55,1.0,1,12,2,3,85,12,Berlari Sore | Mengerjakan artikel medium chapter 1 | Chapter1 Membuat Video YT | Mengerjakan Tes IQ,14,0.1411764705882353,71.66666666666667,60.0,0.6583333333333334,11.666666666666671
user-44,202521,9.1,4.55,2,288,144.0,Berlari Sore | Berlari Sore,2,1.0,2,Chapter2: Mengerjakan Artikel Medium | Chapter02: Membuat Video YouTube,0.7,1.0,2,27,3,2,71,9,Berlari Sore | Berlari Sore | Chapter2: Mengerjakan Artikel Medium | Chapter02: Membuat Video YouTube,12,0.38028169014084506,100.0,40.0,0.7,60.0
user-45,202512,3.2,3.2,1,23,23.0,Afternoon Run,2,1.0,2,Mengerjakan chapter 1 student performance | Kuliah bersama praktisi AI,0.45,1.0,1,13,2,2,70,10,Afternoon Run | Mengerjakan chapter 1 student performance | Kuliah bersama praktisi AI,12,0.18571428571428572,53.333333333333336,40.0,0.46666666666666673,13.333333333333336
user-45,202519,0.9,0.9,1,20,20.0,Berjalan Sore,2,1.0,2,"Mengerjakan tugas medium Decision Tree, Information gain & entropy, dan Random forest | Mengerjakan Video Youtube Algoritma Machine Learning; Decision Tree, Information Gain & Entropy, dan Random Forest",0.45,1.0,1,13,2,2,202,18,"Berjalan Sore | Mengerjakan tugas medium Decision Tree, Information gain & entropy, dan Random forest | Mengerjakan Video Youtube Algoritma Machine Learning; Decision Tree, Information Gain & Entropy, dan Random Forest",20,0.06435643564356436,15.0,40.0,0.275,25.0
user-45,202521,12.9,4.3,3,54,18.0,Berjalan Pagi | Berjalan Sore | Berjalan Petang,2,1.0,2,Mengerjakan Medium Chapter 2 | Mengerjakan Video Youtube Chapter 2,0.7,1.0,3,47,5,2,66,7,Berjalan Pagi | Berjalan Sore | Berjalan Petang | Mengerjakan Medium Chapter 2 | Mengerjakan Video Youtube Chapter 2,12,0.7121212121212122,100.0,40.0,0.7,60.0
user-46,202512,11.600000000000001,5.800000000000001,2,54,27.0,Keliling | Afternoon Run,1,1.0,1,Belajar tentang Artificial Intelligence,0.6,1.0,2,24,4,1,39,4,Keliling | Afternoon Run | Belajar tentang Artificial Intelligence,8,0.6153846153846154,100.0,20.0,0.6,80.0
user-46,202519,2.1,2.1,1,23,23.0,Jogging malam,2,1.0,2,"membuat artikel medium Decision Tree,Information Gain & entropy Random Forest :studi kasus Customer Churn | Membuat Video Youtube DT dan RF",0.45,1.0,1,13,2,2,139,20,"Jogging malam | membuat artikel medium Decision Tree,Information Gain & entropy Random Forest :studi kasus Customer Churn | Membuat Video Youtube DT dan RF",22,0.09352517985611511,35.0,40.0,0.375,5.0
user-46,202520,15.4,15.4,1,229,229.0,Tracking tahura,2,1.0,2,Chapter 1 belajar prediksi dengan dataset Customer chum | Chapter 1 mengerjakan tugas Toutube dengan dataset customer chum,0.45,1.0,1,15,2,2,122,12,Tracking tahura | Chapter 1 belajar prediksi dengan dataset Customer chum | Chapter 1 mengerjakan tugas Toutube dengan dataset customer chum,14,0.12295081967213115,100.0,40.0,0.7,60.0
user-46,202521,22.0,7.333333333333333,3,275,91.66666666666667,Jalan kaki | Jalan jalan | Morning Walk,4,1.0,4,tugas 1 mengerjakan medium garis linear | Tugas 1 mengerjakan video youtube | Chapter 2 mengerjakan medium random forest | Chapter 2 mengerjakan tugas youtube random forest,0.9,1.0,3,39,5,4,172,13,Jalan kaki | Jalan jalan | Morning Walk | tugas 1 mengerjakan medium garis linear | Tugas 1 mengerjakan video youtube | Chapter 2 mengerjakan medium random forest | Chapter 2 mengerjakan tugas youtube random forest,18,0.22674418604651161,100.0,80.0,0.9,20.0
user-47,202521,13.3,6.65,2,100,50.0,Sepeda sore | Lari sore podomoro,4,1.0,4,Mengerjakan Chapter 1 | Mengerjakan Video Chapter 01 | Mengerjakan Chapter 02 | Membuat video Chapter 02,0.9,1.0,2,32,5,4,104,8,Sepeda sore | Lari sore podomoro | Mengerjakan Chapter 1 | Mengerjakan Video Chapter 01 | Mengerjakan Chapter 02 | Membuat video Chapter 02,13,0.3076923076923077,100.0,80.0,0.9,20.0
user-48,202511,5.7,5.7,1,16,16.0,Afternoon Walk,1,1.0,1,ngulanggg gaesssss,0.35,1.0,1,14,2,1,18,2,Afternoon Walk | ngulanggg gaesssss,4,0.7777777777777778,95.0,20.0,0.575,75.0
user-48,202519,5.8,5.8,1,83,83.0,Morning Walk,2,1.0,2,lanjutkann semunya pokoknya | revisiannn semuaaanyaaaa,0.45,1.0,1,12,2,2,54,6,Morning Walk | lanjutkann semunya pokoknya | revisiannn semuaaanyaaaa,8,0.2222222222222222,96.66666666666667,40.0,0.6833333333333335,56.66666666666667
user-48,202520,2.7,2.7,1,11,11.0,Morning Walk,1,1.0,1,pokoknya ngerjain semuanyaa dehh sambil belajar,0.35,1.0,1,12,2,1,47,6,Morning Walk | pokoknya ngerjain semuanyaa dehh sambil belajar,8,0.2553191489361702,45.0,20.0,0.325,25.0
user-48,202521,4.5,2.25,2,78,39.0,Morning Walk | Morning Walk,1,1.0,1,beresin progres mingguan,0.6,1.0,2,27,3,1,24,3,Morning Walk | Morning Walk | beresin progres mingguan,6,1.125,75.0,20.0,0.475,55.0
user-48,202522,7.800000000000001,3.9000000000000004,2,55,27.5,Afternoon Walk | Afternoon Walk,2,1.0,2,lanjutin proyek yagesya | pengen semuanya beres ya allahhh,0.7,1.0,2,31,3,2,58,9,Afternoon Walk | Afternoon Walk | lanjutin proyek yagesya | pengen semuanya beres ya allahhh,12,0.5344827586206896,100.0,40.0,0.7,60.0
user-49,202511,3.6,3.6,1,54,54.0,Walk 1,6,1.0,6,melanjutkan sebelmnya | melanjutkan kembali | melanjutkan kembaliii | melanjutkan yang gagal | melanjutkan kembaliii | melanjutkan kembalii,0.75,1.0,1,6,2,6,139,8,Walk 1 | melanjutkan sebelmnya | melanjutkan kembali | melanjutkan kembaliii | melanjutkan yang gagal | melanjutkan kembaliii | melanjutkan kembalii,10,0.04316546762589928,60.0,100.0,0.8,40.0
user-50,202511,6.6,3.3,2,41,20.5,Night Run | Afternoon Run,2,1.0,2,"memperbaiki tampilan web | melanjutkan buku, web, mining",0.7,1.0,2,25,4,2,56,8,"Night Run | Afternoon Run | memperbaiki tampilan web | melanjutkan buku, web, mining",12,0.44642857142857145,100.0,40.0,0.7,60.0
user-50,202512,8.100000000000001,4.050000000000001,2,90,45.0,Morning Run | Evening Walk,2,1.0,2,"melanjutkan website, dan buku | website, dan buku fundamental",0.7,1.0,2,26,5,2,61,6,"Morning Run | Evening Walk | melanjutkan website, dan buku | website, dan buku fundamental",11,0.4262295081967213,100.0,40.0,0.7,60.0
user-51,202512,11.5,5.75,2,49,24.5,Morning Run | Afternoon Run,1,1.0,1,Belajar Artificial Intelligence,0.6,1.0,2,27,4,1,31,3,Morning Run | Afternoon Run | Belajar Artificial Intelligence,7,0.8709677419354839,100.0,20.0,0.6,80.0
user-51,202519,7.5,3.75,2,31,15.5,Berjalan Sore | Berlari Pagi,3,1.0,3,Membuat Video Tutorial YouTube | Mengerjakan Tugas Medium | Mengerjakan Tugas Video Tutorial YouTube,0.8,1.0,2,28,5,3,100,8,Berjalan Sore | Berlari Pagi | Membuat Video Tutorial YouTube | Mengerjakan Tugas Medium | Mengerjakan Tugas Video Tutorial YouTube,13,0.28,100.0,60.0,0.8,40.0
user-51,202520,5.4,5.4,1,23,23.0,Berlari Sore,2,1.0,2,Chapter 1 Mengerjakan Tugas Medium | Chapter 1 Mengerjakan Tugas Video YouTube,0.45,1.0,1,12,2,2,78,8,Berlari Sore | Chapter 1 Mengerjakan Tugas Medium | Chapter 1 Mengerjakan Tugas Video YouTube,10,0.15384615384615385,90.0,40.0,0.65,50.0
user-51,202521,4.8,4.8,1,14,14.0,Berlari Sore,2,1.0,2,Chapter 2 Mengerjakan Tugas Medium | Chapter 2 Mengerjakan Tugas Video YouTube,0.45,1.0,1,12,2,2,78,8,Berlari Sore | Chapter 2 Mengerjakan Tugas Medium | Chapter 2 Mengerjakan Tugas Video YouTube,10,0.15384615384615385,80.0,40.0,0.6,40.0
user-51,202522,7.7,7.7,1,20,20.0,Berlari Sore,2,1.0,2,Chapter 3 Mengerjakan Tugas Medium | Chapter 3 Mengerjakan Tugas YouTube,0.45,1.0,1,12,2,2,72,7,Berlari Sore | Chapter 3 Mengerjakan Tugas Medium | Chapter 3 Mengerjakan Tugas YouTube,9,0.16666666666666666,100.0,40.0,0.7,60.0
user-52,202511,3.2,3.2,1,40,40.0,Daily,5,1.0,5,"make personal branding website | Lanjut Website, cicil buku | Lanjut website, nyicil buku | Lanjut ngerjain buku | lanjut buku, improve website",0.75,1.0,1,5,1,5,143,13,"Daily | make personal branding website | Lanjut Website, cicil buku | Lanjut website, nyicil buku | Lanjut ngerjain buku | lanjut buku, improve website",14,0.03496503496503497,53.333333333333336,100.0,0.7666666666666667,46.666666666666664
user-52,202522,3.0,3.0,1,38,38.0,Jalan Jalan,1,1.0,1,mengerjakan yang belum,0.35,1.0,1,11,1,1,22,3,Jalan Jalan | mengerjakan yang belum,4,0.5,50.0,20.0,0.35,30.0
user-52,202523,3.2,3.2,1,36,36.0,Capek,1,1.0,1,Mengerjakan yang tidak bisa di kerjakan :),0.35,1.0,1,5,1,1,42,7,Capek | Mengerjakan yang tidak bisa di kerjakan :),8,0.11904761904761904,53.333333333333336,20.0,0.3666666666666667,33.333333333333336
user-53,202511,3.0,3.0,1,58,58.0,Afternoon Walk,7,1.0,7,membuat halaman utama | melanjutkan desain utama | menambahkan detail | menyelesaikan website | Input buku dibukupedia | mengerjakan agar bisa terselesaikan apa yg perlu diselesaikan EA | mengerjakan finishing buat buku pemograman dasar,0.75,1.0,1,14,2,7,236,26,Afternoon Walk | membuat halaman utama | melanjutkan desain utama | menambahkan detail | menyelesaikan website | Input buku dibukupedia | mengerjakan agar bisa terselesaikan apa yg perlu diselesaikan EA | mengerjakan finishing buat buku pemograman dasar,28,0.059322033898305086,50.0,100.0,0.75,50.0
user-54,202519,6.5,6.5,1,22,22.0,Tugas Ai,2,1.0,2,"TUGAS MEMBUAT ARTIKEL MEDIUM Decision Tree, Information Gain & Entropy, serta Random Forest | TUGAS TUTORIAL YOUTUBE Memahami Decision Tree, Information Gain & Entropy, serta Random Forest: Konsep dan Implementasinya",0.45,1.0,1,8,2,2,216,21,"Tugas Ai | TUGAS MEMBUAT ARTIKEL MEDIUM Decision Tree, Information Gain & Entropy, serta Random Forest | TUGAS TUTORIAL YOUTUBE Memahami Decision Tree, Information Gain & Entropy, serta Random Forest: Konsep dan Implementasinya",23,0.037037037037037035,100.0,40.0,0.7,60.0
user-54,202520,24.2,24.2,1,146,146.0,Berlari Sore,2,1.0,2,Tugas Chapter 1 Yutube | tugas chapter 1 medium,0.45,1.0,1,12,2,2,47,6,Berlari Sore | Tugas Chapter 1 Yutube | tugas chapter 1 medium,8,0.2553191489361702,100.0,40.0,0.7,60.0
user-54,202521,31.6,10.533333333333333,3,92,30.666666666666668,Berlari Sore | Berlari Pagi | Berlari Pagi,5,1.0,5,Tugas 1 Persamaan Garis Linear | tugas satu artikel gaeris linear | Tugas chapter 2 madium | tugas video chapter 2 | tugas artikel medium web service,1.0,1.0,3,42,4,5,149,16,Berlari Sore | Berlari Pagi | Berlari Pagi | Tugas 1 Persamaan Garis Linear | tugas satu artikel gaeris linear | Tugas chapter 2 madium | tugas video chapter 2 | tugas artikel medium web service,20,0.28187919463087246,100.0,100.0,1.0,0.0
user-55,202513,9.5,4.75,2,50,25.0,Selasa run | kamis,6,1.0,6,melanjutkan kembali | melanjutkan aja sih | melanjutkan aja sih | 70 mining 30 melanjutkan | melanjutkan lagi aja | melanjutkan untuk hari ini,1.0,1.0,2,18,4,6,142,12,Selasa run | kamis | melanjutkan kembali | melanjutkan aja sih | melanjutkan aja sih | 70 mining 30 melanjutkan | melanjutkan lagi aja | melanjutkan untuk hari ini,16,0.1267605633802817,100.0,100.0,1.0,0.0
user-55,202520,9.6,4.8,2,21,10.5,tau ah | ntahlah,1,1.0,1,ngulang karena ke close,0.6,1.0,2,16,4,1,23,4,tau ah | ntahlah | ngulang karena ke close,8,0.6956521739130435,100.0,20.0,0.6,80.0
user-55,202521,6.2,6.2,1,13,13.0,bingung,1,1.0,1,mengawali pagi ini,0.35,1.0,1,7,1,1,18,3,bingung | mengawali pagi ini,4,0.3888888888888889,100.0,20.0,0.6,80.0
user-55,202522,8.8,4.4,2,23,11.5,lari | mumpung,1,1.0,1,windtail css dan mining,0.6,1.0,2,14,3,1,23,4,lari | mumpung | windtail css dan mining,7,0.6086956521739131,100.0,20.0,0.6,80.0
user-56,202519,3.0,3.0,1,34,34.0,Joging malam,2,1.0,2,"mengerjakan medium, membuat hello word berbasis wwebsite menggunakan go fiber | vidio yucup, membuat hello word menggunakan goFiber",0.45,1.0,1,12,2,2,131,14,"Joging malam | mengerjakan medium, membuat hello word berbasis wwebsite menggunakan go fiber | vidio yucup, membuat hello word menggunakan goFiber",16,0.0916030534351145,50.0,40.0,0.45,10.0
user-56,202520,3.0,3.0,1,6,6.0,Berlari Pagi,3,1.0,3,vidio youtube goFiber hello world | membuat tutorial go fiber hello world di medium | membuat tutorial go fiber di youtube,0.55,1.0,1,12,2,3,122,12,Berlari Pagi | vidio youtube goFiber hello world | membuat tutorial go fiber hello world di medium | membuat tutorial go fiber di youtube,14,0.09836065573770492,50.0,60.0,0.55,10.0
user-56,202522,3.0,3.0,1,8,8.0,Bersepeda Malam,2,1.0,2,"medium tutorial struct, c.json, postman golang | membuat struct, c.json dan postman golang tugas ke2",0.45,1.0,1,15,2,2,100,12,"Bersepeda Malam | medium tutorial struct, c.json, postman golang | membuat struct, c.json dan postman golang tugas ke2",14,0.15,50.0,40.0,0.45,10.0
user-57,202512,11.5,3.8333333333333335,3,100,33.333333333333336,Afternoon Run | Afternoon Run | Afternoon Run,1,1.0,1,Kuliah Praktisi Kecerdasan Buatan,0.6,1.0,3,45,3,1,33,4,Afternoon Run | Afternoon Run | Afternoon Run | Kuliah Praktisi Kecerdasan Buatan,7,1.3636363636363635,100.0,20.0,0.6,80.0
user-57,202521,3.7,3.7,1,29,29.0,Berlari Sore,2,1.0,2,mengerjakan chp 1 medium | video youtube tugas chp 1,0.45,1.0,1,12,2,2,52,8,Berlari Sore | mengerjakan chp 1 medium | video youtube tugas chp 1,10,0.23076923076923078,61.66666666666667,40.0,0.5083333333333333,21.66666666666667
user-57,202522,4.4,4.4,1,16,16.0,Evening Ride,2,1.0,2,mengerjakan medium chp 2 | membuat video yt chap 2,0.45,1.0,1,12,2,2,50,9,Evening Ride | mengerjakan medium chp 2 | membuat video yt chap 2,11,0.24,73.33333333333334,40.0,0.5666666666666668,33.33333333333334
user-58,202511,6.2,3.1,2,65,32.5,mental health | mental health2,7,1.0,7,web personal branding | perbaikan website dan mining | perbikan website dan mining | perbaikan proyek dan mining | pengerjaan assesment and mining | mining and continue the assesment | continue the assesment,1.0,1.0,2,30,4,7,207,15,mental health | mental health2 | web personal branding | perbaikan website dan mining | perbikan website dan mining | perbaikan proyek dan mining | pengerjaan assesment and mining | mining and continue the assesment | continue the assesment,19,0.14492753623188406,100.0,100.0,1.0,0.0
user-58,202519,7.9,3.95,2,19,9.5,Morning Ride | Morning Ride,5,1.0,5,want to learn something | work the task before | lets finish the task | learn something to finished the task | lets finish the pomokit point,1.0,1.0,2,27,3,5,140,14,Morning Ride | Morning Ride | want to learn something | work the task before | lets finish the task | learn something to finished the task | lets finish the pomokit point,17,0.19285714285714287,100.0,100.0,1.0,0.0
user-58,202520,8.2,2.733333333333333,3,28,9.333333333333334,Lunch Run | Afternoon Run | Lunch Run,5,1.0,5,do th etask from lecture | do the task from lecture | do the task from lecture | do the task from lecture | its just like usual,1.0,1.0,3,37,4,5,127,12,Lunch Run | Afternoon Run | Lunch Run | do th etask from lecture | do the task from lecture | do the task from lecture | do the task from lecture | its just like usual,16,0.29133858267716534,100.0,100.0,1.0,0.0
user-58,202521,13.200000000000001,6.6000000000000005,2,28,14.0,Morning Run | Afternoon Run,3,1.0,3,do the task from lecture | do the task from lecture | complete the pomokit task,0.8,1.0,2,27,4,3,79,8,Morning Run | Afternoon Run | do the task from lecture | do the task from lecture | complete the pomokit task,12,0.34177215189873417,100.0,60.0,0.8,40.0
user-58,202522,14.2,4.733333333333333,3,39,13.0,Lunch Run | Morning Run | Afternoon Run,2,1.0,2,do the task from lecture | do teh task from lecture,0.7,1.0,3,39,5,2,51,7,Lunch Run | Morning Run | Afternoon Run | do the task from lecture | do teh task from lecture,12,0.7647058823529411,100.0,40.0,0.7,60.0
user-59,202512,3.0,3.0,1,50,50.0,Night Walk,1,1.0,1,Belajar tentang Artificial Intelligence,0.35,1.0,1,10,2,1,39,4,Night Walk | Belajar tentang Artificial Intelligence,6,0.2564102564102564,50.0,20.0,0.35,30.0
user-59,202519,2.2,2.2,1,13,13.0,Berlari Petang,2,1.0,2,"Tugas Medium Konsep dan Contoh Decision Tree, Information Gain & Entropy, Random Forest dengan Python | Tugas Youtube Contoh soal DT dan RF",0.45,1.0,1,14,2,2,139,20,"Berlari Petang | Tugas Medium Konsep dan Contoh Decision Tree, Information Gain & Entropy, Random Forest dengan Python | Tugas Youtube Contoh soal DT dan RF",22,0.10071942446043165,36.66666666666667,40.0,0.38333333333333336,3.3333333333333286
user-59,202520,3.7,3.7,1,52,52.0,Berjalan Sore,1,1.0,1,Chapter 1 Memprediksi Kelulusan Siswa Menggunakan Decision Tree: Teori dan Implementasi Python,0.35,1.0,1,13,2,1,94,12,Berjalan Sore | Chapter 1 Memprediksi Kelulusan Siswa Menggunakan Decision Tree: Teori dan Implementasi Python,14,0.13829787234042554,61.66666666666667,20.0,0.4083333333333334,41.66666666666667
user-59,202521,2.1,2.1,1,11,11.0,Berlari Pagi,3,1.0,3,Chapter 1 Youtube | mengerjakan medium chapter 2 | Chapter 2 Youtube,0.55,1.0,1,12,2,3,68,7,Berlari Pagi | Chapter 1 Youtube | mengerjakan medium chapter 2 | Chapter 2 Youtube,9,0.17647058823529413,35.0,60.0,0.475,25.0
user-59,202522,12.2,12.2,1,178,178.0,Berjalan Pagi,2,1.0,2,Chapter 3 mengerjakan medium | Chapter 3 mengerjakan yt,0.45,1.0,1,13,2,2,55,6,Berjalan Pagi | Chapter 3 mengerjakan medium | Chapter 3 mengerjakan yt,8,0.23636363636363636,100.0,40.0,0.7,60.0
user-60,202521,16.6,8.3,2,226,113.0,Salam sehat | Malam yang Sehat,2,1.0,2,Mengunggah tutorial hello world dengan GoFiber di Youtube | Tugas 2 membuat tutorial dalam artikel medium,0.7,1.0,2,30,5,2,105,15,Salam sehat | Malam yang Sehat | Mengunggah tutorial hello world dengan GoFiber di Youtube | Tugas 2 membuat tutorial dalam artikel medium,20,0.2857142857142857,100.0,40.0,0.7,60.0
user-61,202511,6.8,3.4,2,106,53.0,Afternoon Walk | Morning Walk,4,1.0,4,lanjut mengerjakan website personal branding | lanjut mengerjakan website personal branding | membuat tugas buku | lanjut mengerjakan tugas website dan mining,0.9,1.0,2,29,4,4,158,11,Afternoon Walk | Morning Walk | lanjut mengerjakan website personal branding | lanjut mengerjakan website personal branding | membuat tugas buku | lanjut mengerjakan tugas website dan mining,15,0.18354430379746836,100.0,80.0,0.9,20.0
user-62,202511,6.6,3.3,2,50,25.0,tugas 1 | tugas 2.,7,1.0,7,"membuat website dan maining | web personal, maining | lanjut proses web dan maining | maining dan melanjutkan web | website dan mining | lanjut personal dan buku | lanjut website dan buku",1.0,1.0,2,18,4,7,187,13,"tugas 1 | tugas 2. | membuat website dan maining | web personal, maining | lanjut proses web dan maining | maining dan melanjutkan web | website dan mining | lanjut personal dan buku | lanjut website dan buku",17,0.0962566844919786,100.0,100.0,1.0,0.0
user-62,202512,15.5,7.75,2,72,36.0,tiga | empat,2,1.0,2,mengerjakan progres tugas | melanjutkan tugas kemarin,0.7,1.0,2,12,3,2,53,6,tiga | empat | mengerjakan progres tugas | melanjutkan tugas kemarin,9,0.22641509433962265,100.0,40.0,0.7,60.0
user-62,202520,19.0,6.333333333333333,3,77,25.666666666666668,lari 3 | lari 4 | lari 5,1,1.0,1,melanjutkan progres,0.6,1.0,3,24,5,1,19,2,lari 3 | lari 4 | lari 5 | melanjutkan progres,7,1.263157894736842,100.0,20.0,0.6,80.0
user-63,202511,7.1,3.55,2,109,54.5,Dirty first-time | Second Disaster Recovery,7,1.0,7,"Pengerjaan style.css, Penambahan assets | Penambahan assets, css, js, images, dan fonts | Melanjutkan pengerjaan assets index website | Melanjutkan pengerjaan website (lagi) | Melanjutkan pengerjaan website, penambahan assets fonts | Melanjutkan pengerjaan index website | Melanjutkan pengerjaan index website",1.0,1.0,2,43,6,7,309,16,"Dirty first-time | Second Disaster Recovery | Pengerjaan style.css, Penambahan assets | Penambahan assets, css, js, images, dan fonts | Melanjutkan pengerjaan assets index website | Melanjutkan pengerjaan website (lagi) | Melanjutkan pengerjaan website, penambahan assets fonts | Melanjutkan pengerjaan index website | Melanjutkan pengerjaan index website",22,0.13915857605177995,100.0,100.0,1.0,0.0
user-63,202512,8.1,4.05,2,127,63.5,Third time the charm | Four 🍀,1,1.0,1,Perbaikan index Website,0.6,1.0,2,29,7,1,23,3,Third time the charm | Four 🍀 | Perbaikan index Website,10,1.2608695652173914,100.0,20.0,0.6,80.0
user-64,202521,9.0,4.5,2,39,19.5,Evening Ride | Afternoon Ride,2,1.0,2,UPLOAD TUDAS DI MEDIUM | Upload Tugas Youtube minggu pertama,0.7,1.0,2,29,4,2,60,9,Evening Ride | Afternoon Ride | UPLOAD TUDAS DI MEDIUM | Upload Tugas Youtube minggu pertama,13,0.****************4,100.0,40.0,0.7,60.0
user-64,202522,6.6,6.6,1,19,19.0,Night Ride,1,1.0,1,MEMBUAT TUTORIAL STRUCK FUNGSI DAN JSON RESPONSE DI GOLANG SERTA MENGHUBUNGKAN DENGAN POSTMAN,0.35,1.0,1,10,2,1,93,13,Night Ride | MEMBUAT TUTORIAL STRUCK FUNGSI DAN JSON RESPONSE DI GOLANG SERTA MENGHUBUNGKAN DENGAN POSTMAN,15,0.10752688172043011,100.0,20.0,0.6,80.0
user-65,202512,4.5,4.5,1,23,23.0,Only run,1,1.0,1,Belajar tentang Artificial Intelligence,0.35,1.0,1,8,2,1,39,4,Only run | Belajar tentang Artificial Intelligence,6,0.20512820512820512,75.0,20.0,0.475,55.0
user-65,202519,4.4,4.4,1,18,18.0,Burnnn,2,1.0,2,"Membuat tutorial youtube tugas AI untuk DT, IG, Entropy dan RF | Melanjutkan tugas Medium DT, IG, Entropy dan RF",0.45,1.0,1,6,1,2,112,14,"Burnnn | Membuat tutorial youtube tugas AI untuk DT, IG, Entropy dan RF | Melanjutkan tugas Medium DT, IG, Entropy dan RF",15,0.05357142857142857,73.33333333333334,40.0,0.5666666666666668,33.33333333333334
user-65,202520,2.8,2.8,1,11,11.0,Sore1,2,1.0,2,Mengerjakan Artikel Medium Chapter 1: Implementasi Decission Tree pada Dataset Wine | Mengerjakan Tutorial Youtube Chapter 1:dengan model Decission Tree pada Dataset Wine,0.45,1.0,1,5,1,2,170,16,Sore1 | Mengerjakan Artikel Medium Chapter 1: Implementasi Decission Tree pada Dataset Wine | Mengerjakan Tutorial Youtube Chapter 1:dengan model Decission Tree pada Dataset Wine,17,0.029411764705882353,46.666666666666664,40.0,0.4333333333333333,6.666666666666664
user-65,202521,2.7,2.7,1,39,39.0,Ke pasar,2,1.0,2,Membuat artikel medium chapter 2 dengan model random forest pada dataset titanic | Chapter 2 mengerjakan tugas youtube dengan dataset titanic pada model RF,0.45,1.0,1,8,2,2,155,17,Ke pasar | Membuat artikel medium chapter 2 dengan model random forest pada dataset titanic | Chapter 2 mengerjakan tugas youtube dengan dataset titanic pada model RF,19,0.05161290322580645,45.0,40.0,0.425,5.0
user-65,202522,2.6,0.8666666666666667,3,46,15.333333333333334,Ke wrg1 | Ke warung lagi | Ke pasar lagi,2,1.0,2,Mengerjakan artikel medium Chapter 3 teks klasifikasi dengan Random Forest pada dataset 20 newsgrup | Mengerjakan Chapter 3 tutorial youtube dengan model random forest pada dataset 20 newsgrups,0.7,1.0,3,40,6,2,193,19,Ke wrg1 | Ke warung lagi | Ke pasar lagi | Mengerjakan artikel medium Chapter 3 teks klasifikasi dengan Random Forest pada dataset 20 newsgrup | Mengerjakan Chapter 3 tutorial youtube dengan model random forest pada dataset 20 newsgrups,25,0.20725388601036268,43.333333333333336,40.0,0.41666666666666674,3.3333333333333357
user-66,202511,7.6,3.8,2,124,62.0,Jalan jalan | Jalan santai,7,1.0,7,permulaan tampilan awal | penambahan tampilan website | melanjutkan pembuatan website | melanjutkan progress website | merapihkan tampilan website | melanjutkan pembuatan web | melanjutkan tugas proyek 1,1.0,1.0,2,26,3,7,203,14,Jalan jalan | Jalan santai | permulaan tampilan awal | penambahan tampilan website | melanjutkan pembuatan website | melanjutkan progress website | merapihkan tampilan website | melanjutkan pembuatan web | melanjutkan tugas proyek 1,17,0.12807881773399016,100.0,100.0,1.0,0.0
user-67,202511,6.4,3.2,2,41,20.5,Lari dari kenyataan | Jalan sore,3,1.0,3,mengulang lagi yang error tadi | ngelanjutin yang kemaren lagi | ngerjain buku untuk project golang,0.8,1.0,2,32,6,3,99,13,Lari dari kenyataan | Jalan sore | mengulang lagi yang error tadi | ngelanjutin yang kemaren lagi | ngerjain buku untuk project golang,19,0.32323232323232326,100.0,60.0,0.8,40.0
user-67,202519,10.2,10.2,1,37,37.0,THE LEGEND OF ZAHRA,3,1.0,3,merubah website saya | mengerjakan website saya | upgrade lagi lah kan belum bagus,0.55,1.0,1,19,4,3,82,11,THE LEGEND OF ZAHRA | merubah website saya | mengerjakan website saya | upgrade lagi lah kan belum bagus,15,0.23170731707317074,100.0,60.0,0.8,40.0
user-67,202522,7.2,7.2,1,18,18.0,Nightfinity,3,1.0,3,fixing website yang sudah lama tidak tersentuh | merombak ulang website saya | uulang lagi hadehhhh internet mati kocak,0.55,1.0,1,11,1,3,119,17,Nightfinity | fixing website yang sudah lama tidak tersentuh | merombak ulang website saya | uulang lagi hadehhhh internet mati kocak,18,0.09243697478991597,100.0,60.0,0.8,40.0
user-68,202512,7.2,7.2,1,34,34.0,Afternoon Walk,1,1.0,1,belajar python artifical,0.35,1.0,1,14,2,1,24,3,Afternoon Walk | belajar python artifical,5,0.5833333333333334,100.0,20.0,0.6,80.0
user-68,202521,10.2,3.4,3,32,10.666666666666666,Berlari Sore | Berlari Sore | Afternoon Run,4,1.0,4,Mengerjakan Regresi Linear | Mengerjakan Youtube linear | artikel tugas 2 decision tree | mengerjakan artikel chapter 1,0.9,1.0,3,43,5,4,119,12,Berlari Sore | Berlari Sore | Afternoon Run | Mengerjakan Regresi Linear | Mengerjakan Youtube linear | artikel tugas 2 decision tree | mengerjakan artikel chapter 1,17,0.36134453781512604,100.0,80.0,0.9,20.0
user-69,202512,5.5,2.75,2,27,13.5,Keliling keliling | Evening Run,1,1.0,1,Belajar Artificial Intelligence,0.6,1.0,2,31,4,1,31,3,Keliling keliling | Evening Run | Belajar Artificial Intelligence,7,1.0,91.66666666666666,20.0,0.5583333333333332,71.66666666666666
user-69,202519,3.1,3.1,1,41,41.0,Persib juara,2,1.0,2,"Tugas Tutorial Youtube Memahami Decision Tree, Entropy, Information Gain & Random Forest | Tugas Medium Memahami Decision Tree, Entropy, Information Gain & Random Forest",0.45,1.0,1,12,2,2,169,14,"Persib juara | Tugas Tutorial Youtube Memahami Decision Tree, Entropy, Information Gain & Random Forest | Tugas Medium Memahami Decision Tree, Entropy, Information Gain & Random Forest",16,0.07100591715976332,51.66666666666667,40.0,0.45833333333333337,11.666666666666671
user-69,202521,5.3,5.3,1,18,18.0,Lari Pagi,4,1.0,4,Chapter 1 Tutorial YouTube: Membangun Model Machine Learning Sederhana Untuk Klasifikasi Buah dengan Decision Tree | Chapter 1 Tutorial YouTube: Membangun Model Machine Learning Sederhana Untuk Klasifikasi Buah dengan Decision Tree | Chapter 2 Medium: Membangun Pipeline Random Forest dengan Python: Dari Teori ke Praktik | Chapter 2 YouTube: Membangun Pipeline Random Forest dengan Python: Dari Teori ke Praktik,0.65,1.0,1,9,2,4,412,26,Lari Pagi | Chapter 1 Tutorial YouTube: Membangun Model Machine Learning Sederhana Untuk Klasifikasi Buah dengan Decision Tree | Chapter 1 Tutorial YouTube: Membangun Model Machine Learning Sederhana Untuk Klasifikasi Buah dengan Decision Tree | Chapter 2 Medium: Membangun Pipeline Random Forest dengan Python: Dari Teori ke Praktik | Chapter 2 YouTube: Membangun Pipeline Random Forest dengan Python: Dari Teori ke Praktik,28,0.021844660194174758,88.33333333333333,80.0,0.8416666666666666,8.333333333333329
user-69,202522,2.3,2.3,1,10,10.0,Berlari Sore,2,1.0,2,Chapter 3 Medium: Deteksi Pesan SPAM Otomatis dengan Python & Random Forest | Chapter 3 YouTube Deteksi Pesan SPAM Otomatis dengan Python & Random Forest,0.45,1.0,1,12,2,2,153,14,Berlari Sore | Chapter 3 Medium: Deteksi Pesan SPAM Otomatis dengan Python & Random Forest | Chapter 3 YouTube Deteksi Pesan SPAM Otomatis dengan Python & Random Forest,16,0.0784313725490196,38.33333333333333,40.0,0.39166666666666666,1.6666666666666714
user-70,202521,11.0,5.5,2,41,20.5,Berlari Malam | Berlari Sore,3,1.0,3,Membuat Tutorial Youtube Hello World Menggunakan Golang | Membuat Tutorial Hello World Menggunakan GOLANG | Membuat Medium Tutorial Hello World Menggunakan GOLANG,0.8,1.0,2,28,4,3,162,9,Berlari Malam | Berlari Sore | Membuat Tutorial Youtube Hello World Menggunakan Golang | Membuat Tutorial Hello World Menggunakan GOLANG | Membuat Medium Tutorial Hello World Menggunakan GOLANG,13,0.1728395061728395,100.0,60.0,0.8,40.0
user-71,202511,9.9,3.3000000000000003,3,149,49.666666666666664,Jalan 3Km Selasa | Monumen Sabtu Pagi | Jalan Minggu 3Km,8,1.0,8,"inisiallisasi web personal branding | lanjut project portofolio web | add section project | lengkapin komponen, lanjut mining | nambah dark mode, nyari referensi buku | lanjut ngerjain buku pra assement | mining sambil turu | lanjut mining, perbaikan web kecil2 an",1.0,1.0,3,56,8,8,264,28,"Jalan 3Km Selasa | Monumen Sabtu Pagi | Jalan Minggu 3Km | inisiallisasi web personal branding | lanjut project portofolio web | add section project | lengkapin komponen, lanjut mining | nambah dark mode, nyari referensi buku | lanjut ngerjain buku pra assement | mining sambil turu | lanjut mining, perbaikan web kecil2 an",36,0.21212121212121213,100.0,100.0,1.0,0.0
user-71,202512,16.0,8.0,2,68,34.0,senin 7 maret | 18 Mar,1,1.0,1,nambah section website,0.6,1.0,2,22,6,1,22,3,senin 7 maret | 18 Mar | nambah section website,9,1.0,100.0,20.0,0.6,80.0
user-71,202522,6.6,3.3,2,29,14.5,3Km 26 Mei | 29 May,1,1.0,1,setup project + ngisi buku,0.6,1.0,2,19,6,1,26,5,3Km 26 Mei | 29 May | setup project + ngisi buku,11,0.7307692307692307,100.0,20.0,0.6,80.0
user-72,202522,6.0,6.0,1,28,28.0,Savana lagi Lari,2,1.0,2,"tugas struct,return cjson,koneksi ke postman di youtube | tugas struct,return cjson,koneksi ke postman di medium",0.45,1.0,1,16,3,2,112,9,"Savana lagi Lari | tugas struct,return cjson,koneksi ke postman di youtube | tugas struct,return cjson,koneksi ke postman di medium",12,0.14285714285714285,100.0,40.0,0.7,60.0
user-73,202511,6.1,3.05,2,74,37.0,lari dari kenyataan | jalan sehat,5,1.0,5,120363022595651310 https://daparamadhan.github.io/Portofolio/ | ngelanjutin web niie | lanjutannn kemarinnnn | pembuatan buku dan lain lain | melanjutkan melanjutkanmelanjutkan,1.0,1.0,2,33,6,5,176,14,lari dari kenyataan | jalan sehat | 120363022595651310 https://daparamadhan.github.io/Portofolio/ | ngelanjutin web niie | lanjutannn kemarinnnn | pembuatan buku dan lain lain | melanjutkan melanjutkanmelanjutkan,20,0.1875,100.0,100.0,1.0,0.0
user-74,202512,7.9,3.95,2,42,21.0,Night Run | Evening Run,1,1.0,1,Kuliah Praktisi Artifical Intelligence,0.6,1.0,2,23,4,1,38,4,Night Run | Evening Run | Kuliah Praktisi Artifical Intelligence,8,0.6052631578947368,100.0,20.0,0.6,80.0
user-74,202519,11.6,5.8,2,54,27.0,Evening Run | Evening Run,2,1.0,2,"Membuat artikel medium Decision Tree Information Gain & Entropy random forest | Membuat medium Decision Tree, Information Gain & Entropy, serta Random Forest",0.7,1.0,2,25,3,2,157,15,"Evening Run | Evening Run | Membuat artikel medium Decision Tree Information Gain & Entropy random forest | Membuat medium Decision Tree, Information Gain & Entropy, serta Random Forest",18,0.1592356687898089,100.0,40.0,0.7,60.0
user-74,202521,5.6,5.6,1,16,16.0,Evening Run,3,1.0,3,Chapter 1: Pembuatan Medium Penerapan Algoritma Decision Tree pada Dataset Evaluasi Mobil | Youtube Chapter 1: Penerapan Algoritma Decision Tree pada Dataset Evaluasi Mobil | Chapter 2: Mengerjakan Artikel Medium Prediksi Kategori Penjualan Global Video Game Menggunakan Algoritma Random Forest,0.55,1.0,1,11,2,3,294,26,Evening Run | Chapter 1: Pembuatan Medium Penerapan Algoritma Decision Tree pada Dataset Evaluasi Mobil | Youtube Chapter 1: Penerapan Algoritma Decision Tree pada Dataset Evaluasi Mobil | Chapter 2: Mengerjakan Artikel Medium Prediksi Kategori Penjualan Global Video Game Menggunakan Algoritma Random Forest,28,0.03741496598639456,93.33333333333333,60.0,0.7666666666666666,33.33333333333333
user-74,202522,10.399999999999999,5.199999999999999,2,32,16.0,Afternoon Run | Evening Run,3,1.0,3,Membuat video youtube chapter 2: Prediksi Kategori Penjualan Global Video Game Menggunakan Random Forest | Chapter 03 : Mengerjakan Artikel Medium Klasifikasi Sentimen Ulasan Film Menggunakan Representasi Teks dan Algoritma Random Forest | Chapter 03: Mengerjakan video,0.8,1.0,2,27,4,3,269,28,Afternoon Run | Evening Run | Membuat video youtube chapter 2: Prediksi Kategori Penjualan Global Video Game Menggunakan Random Forest | Chapter 03 : Mengerjakan Artikel Medium Klasifikasi Sentimen Ulasan Film Menggunakan Representasi Teks dan Algoritma Random Forest | Chapter 03: Mengerjakan video,32,0.10037174721189591,100.0,60.0,0.8,40.0
user-75,202520,4.4,4.4,1,18,18.0,Berlari Malam,1,1.0,1,Medium Tugas 1 Regresi Linear,0.35,1.0,1,13,2,1,29,5,Berlari Malam | Medium Tugas 1 Regresi Linear,7,0.4482758620689655,73.33333333333334,20.0,0.46666666666666673,53.33333333333334
user-75,202521,3.1,3.1,1,11,11.0,Bersepeda Sore,5,1.0,5,"Mengerjakan Tugas 2 Medium Decision Tree, Random Forest, dan Information Gain | Chapter 1 : Medium Membangun Model Prediksi Biaya Asuransi Kesehatan Menggunakan Decision Tree di Python | Tugas 1 Video, Persamaan Linear, Regresi linear, dan SVM | Tugas 2 Video : Penjelasan singkat tentang Decision Tree dan Random Forest | Tugas 3 Chapter 1 : Video Membangun model prediksi biaya asuransi kesehatan dengan decision tree",0.75,1.0,1,14,2,5,419,37,"Bersepeda Sore | Mengerjakan Tugas 2 Medium Decision Tree, Random Forest, dan Information Gain | Chapter 1 : Medium Membangun Model Prediksi Biaya Asuransi Kesehatan Menggunakan Decision Tree di Python | Tugas 1 Video, Persamaan Linear, Regresi linear, dan SVM | Tugas 2 Video : Penjelasan singkat tentang Decision Tree dan Random Forest | Tugas 3 Chapter 1 : Video Membangun model prediksi biaya asuransi kesehatan dengan decision tree",39,0.03341288782816229,51.66666666666667,100.0,0.7583333333333334,48.33333333333333
user-75,202522,4.1,4.1,1,24,24.0,Sepeda hujan2an,2,1.0,2,tugas 2 decision tree dan random forest (pomokit ulang) | Tugas 2 YT (pomokit ulang),0.45,1.0,1,15,2,2,84,11,Sepeda hujan2an | tugas 2 decision tree dan random forest (pomokit ulang) | Tugas 2 YT (pomokit ulang),13,0.17857142857142858,68.33333333333333,40.0,0.5416666666666666,28.33333333333333
user-76,202520,4.3,4.3,1,36,36.0,?,4,1.0,4,Mengerjakan Tugas Medium | Mengerjakan tugas 1 Persamaan Linear | Membuat Youtube tutorial Decision tree | Mengerjakan youtube tutorial Persamaan Linear,0.65,1.0,1,1,1,4,152,12,? | Mengerjakan Tugas Medium | Mengerjakan tugas 1 Persamaan Linear | Membuat Youtube tutorial Decision tree | Mengerjakan youtube tutorial Persamaan Linear,13,0.006578947368421052,71.66666666666667,80.0,0.7583333333333334,8.333333333333329
user-77,202520,9.600000000000001,4.800000000000001,2,49,24.5,Malam Suweger | Lari Malam Sarijadi,2,1.0,2,"Mengerjakan Artikel Tutorial Sederhana di Medium Tentang DT, RF, IG, dan Entropy | Mengerjakan Video Pemhaman Konsep Decision Tree IG, Entropy, dan Random Forest",0.7,1.0,2,35,5,2,161,21,"Malam Suweger | Lari Malam Sarijadi | Mengerjakan Artikel Tutorial Sederhana di Medium Tentang DT, RF, IG, dan Entropy | Mengerjakan Video Pemhaman Konsep Decision Tree IG, Entropy, dan Random Forest",26,0.21739130434782608,100.0,40.0,0.7,60.0
user-77,202521,5.8,5.8,1,17,17.0,Sepedaan ke Pasteur Malah Macet Kena Konvoi 🤣,2,1.0,2,Menerjakan Chapter 1 artikel medium | Mengejrkan ulang Youtube Implemnetasi Decision Tree KArena Sbelumnya laptop Bluescreen,0.45,1.0,1,45,8,2,124,16,Sepedaan ke Pasteur Malah Macet Kena Konvoi 🤣 | Menerjakan Chapter 1 artikel medium | Mengejrkan ulang Youtube Implemnetasi Decision Tree KArena Sbelumnya laptop Bluescreen,24,0.3629032258064516,96.66666666666667,40.0,0.6833333333333335,56.66666666666667
user-78,202512,7.8,3.9,2,51,25.5,Evening Run | Afternoon Run,1,1.0,1,Belajar tentang Artificial Intelligence,0.6,1.0,2,27,4,1,39,4,Evening Run | Afternoon Run | Belajar tentang Artificial Intelligence,8,0.6923076923076923,100.0,20.0,0.6,80.0
user-78,202519,2.7,2.7,1,34,34.0,Morning Run,2,1.0,2,Membuat Tutorial DT dan RF di Youtube | mMembuat tutorial DT dan RF di medium,0.45,1.0,1,11,2,2,77,10,Morning Run | Membuat Tutorial DT dan RF di Youtube | mMembuat tutorial DT dan RF di medium,12,0.14285714285714285,45.0,40.0,0.425,5.0
user-78,202521,2.3,2.3,1,22,22.0,Evening Run,2,1.0,2,membuat medium Prediksi Berat Badanmu Sendiri dengan Decision Tree | Tugas youtube Chapter 1,0.45,1.0,1,11,2,2,92,14,Evening Run | membuat medium Prediksi Berat Badanmu Sendiri dengan Decision Tree | Tugas youtube Chapter 1,16,0.11956521739130435,38.33333333333333,40.0,0.39166666666666666,1.6666666666666714
user-78,202522,2.0,2.0,1,24,24.0,Morning Run,2,1.0,2,Chapter 2 : Membuat Medium Random Forest | Chapter 2 : Membuat Video Random Forest Youtube,0.45,1.0,1,11,2,2,90,10,Morning Run | Chapter 2 : Membuat Medium Random Forest | Chapter 2 : Membuat Video Random Forest Youtube,12,0.12222222222222222,33.33333333333333,40.0,0.36666666666666664,6.666666666666671
user-79,202511,6.0,3.0,2,98,49.0,Jalan ke Setra Duta | Ngabuburit asoy,7,1.0,7,"Membuat web portofolio IT | Perbaharuan web personal branding | Menambahkan detail | Menambahkan detail lain | Melanjutkan yang belum selesai | buat buku, lanjut site | melanjutkan buku, melanjutkan site",1.0,1.0,2,37,7,7,203,19,"Jalan ke Setra Duta | Ngabuburit asoy | Membuat web portofolio IT | Perbaharuan web personal branding | Menambahkan detail | Menambahkan detail lain | Melanjutkan yang belum selesai | buat buku, lanjut site | melanjutkan buku, melanjutkan site",26,0.18226600985221675,100.0,100.0,1.0,0.0
user-79,202512,6.2,3.1,2,54,27.0,berjalan jalan | ngabuburit lagie,2,1.0,2,rapihin buku dan beberapa yang belum selesai | melanjutkan web kembali,0.7,1.0,2,33,5,2,70,11,berjalan jalan | ngabuburit lagie | rapihin buku dan beberapa yang belum selesai | melanjutkan web kembali,16,0.4714285714285714,100.0,40.0,0.7,60.0
user-79,202513,3.1,3.1,1,47,47.0,berjalan jalan,1,1.0,1,melanjutkan tugas lagi,0.35,1.0,1,14,2,1,22,3,berjalan jalan | melanjutkan tugas lagi,5,0.6363636363636364,51.66666666666667,20.0,0.35833333333333334,31.66666666666667
user-80,202517,3.2,3.2,1,19,19.0,Lari sore,1,1.0,1,cek grade website branding,0.35,1.0,1,9,2,1,26,4,Lari sore | cek grade website branding,6,0.34615384615384615,53.333333333333336,20.0,0.3666666666666667,33.333333333333336
user-80,202518,23.2,11.6,2,31,15.5,Lari dimix jalan | Morning run,1,1.0,1,melanjutkan progress tugas utk bimbingan minggu ini,0.6,1.0,2,30,6,1,51,7,Lari dimix jalan | Morning run | melanjutkan progress tugas utk bimbingan minggu ini,13,0.5882352941176471,100.0,20.0,0.6,80.0
user-80,202520,3.6,3.6,1,22,22.0,Jogging judulnya,4,1.0,4,lanjut ngerjain proyek 1 | yang penting lanjut proyek | pokoknya lanjut dulu dah | lanjuutin aja pokoknya,0.65,1.0,1,16,2,4,105,12,Jogging judulnya | lanjut ngerjain proyek 1 | yang penting lanjut proyek | pokoknya lanjut dulu dah | lanjuutin aja pokoknya,14,0.1523809523809524,60.0,80.0,0.7,20.0
user-80,202521,3.5,3.5,1,52,52.0,Jogging,4,1.0,4,lanjut progres mingguan | lanjutin proyek lagi | yang penting lanjut | mining dlu lah lengkapin,0.65,1.0,1,7,1,4,95,13,Jogging | lanjut progres mingguan | lanjutin proyek lagi | yang penting lanjut | mining dlu lah lengkapin,14,0.07368421052631578,58.333333333333336,80.0,0.6916666666666668,21.666666666666664
user-81,202519,3.1,3.1,1,8,8.0,Bersepeda Pagi,2,1.0,2,Membuat HelloWord dengan Gofiber dan Membuat Tutorialnya di YT | Membuat HelloWord dengan Gofiber dan Membuat Tutorialnya di Medium,0.45,1.0,1,14,2,2,131,10,Bersepeda Pagi | Membuat HelloWord dengan Gofiber dan Membuat Tutorialnya di YT | Membuat HelloWord dengan Gofiber dan Membuat Tutorialnya di Medium,12,0.10687022900763359,51.66666666666667,40.0,0.45833333333333337,11.666666666666671
user-81,202522,7.5,7.5,1,23,23.0,Bersepeda Sore,1,1.0,1,Membuat tutorial di YT,0.35,1.0,1,14,2,1,22,4,Bersepeda Sore | Membuat tutorial di YT,6,0.6363636363636364,100.0,20.0,0.6,80.0
user-82,202521,7.9,7.9,1,35,35.0,lari aja,1,1.0,1,membuat artikel medium,0.35,1.0,1,8,2,1,22,3,lari aja | membuat artikel medium,5,0.36363636363636365,100.0,20.0,0.6,80.0
user-82,202522,6.0,6.0,1,25,25.0,lari,2,1.0,2,membuat artikel medium | membuat tutorial di yt,0.45,1.0,1,4,1,2,47,7,lari | membuat artikel medium | membuat tutorial di yt,8,0.0851063829787234,100.0,40.0,0.7,60.0
user-83,202511,4.3,4.3,1,55,55.0,alhamdulillah,5,1.0,5,"mengedit, memperbaiki, dan menambah | mengubah, mengerjakan tugas lain | melanjutkan pengerjaan | Melanjutkan, menambah, mengerjakan buku | mengerjakan, melanjutkan buku, dkk",0.75,1.0,1,13,1,5,174,17,"alhamdulillah | mengedit, memperbaiki, dan menambah | mengubah, mengerjakan tugas lain | melanjutkan pengerjaan | Melanjutkan, menambah, mengerjakan buku | mengerjakan, melanjutkan buku, dkk",18,0.07471264367816093,71.66666666666667,100.0,0.8583333333333334,28.33333333333333
user-83,202513,13.7,4.566666666666666,3,188,62.666666666666664,NICE TRY | NT | Last R,1,1.0,1,"melanjutkan, mengubah, memperbaiki, dan menambahkan",0.6,1.0,3,22,6,1,51,5,"NICE TRY | NT | Last R | melanjutkan, mengubah, memperbaiki, dan menambahkan",11,0.43137254901960786,100.0,20.0,0.6,80.0
user-83,202515,13.0,4.333333333333333,3,214,71.33333333333333,AP | Mendaki Pagi | mt2,1,1.0,1,"mengubah, melanjutkan, dan memperbaiki",0.6,1.0,3,23,5,1,38,4,"AP | Mendaki Pagi | mt2 | mengubah, melanjutkan, dan memperbaiki",9,0.6052631578947368,100.0,20.0,0.6,80.0
user-83,202518,4.6,4.6,1,19,19.0,tried,1,1.0,1,"mengerjakan anu, anunya dikerjain",0.35,1.0,1,5,1,1,33,4,"tried | mengerjakan anu, anunya dikerjain",5,0.15151515151515152,76.66666666666666,20.0,0.****************,56.66666666666666
user-83,202519,3.1,3.1,1,21,21.0,Nt,2,1.0,2,"mencoba lagi yang kemarin | resume, new, finishing",0.45,1.0,1,2,1,2,50,8,"Nt | mencoba lagi yang kemarin | resume, new, finishing",9,0.04,51.66666666666667,40.0,0.45833333333333337,11.666666666666671
user-83,202520,7.1,3.55,2,109,54.5,RN | Sore,2,1.0,2,mengulangi lagi yang tadi | melanjutkan saja seperti sebelum-sebelumnya,0.7,1.0,2,9,3,2,71,9,RN | Sore | mengulangi lagi yang tadi | melanjutkan saja seperti sebelum-sebelumnya,12,0.1267605633802817,100.0,40.0,0.7,60.0
user-84,202522,2.7,2.7,1,35,35.0,Jogging beli cimol,2,1.0,2,"membuat Tutorial Struct, fungsi dan c.JSON dengan Postman di medium | membuat tutorial struct, fungsi, dan c.JSON di youtube",0.45,1.0,1,18,3,2,124,13,"Jogging beli cimol | membuat Tutorial Struct, fungsi dan c.JSON dengan Postman di medium | membuat tutorial struct, fungsi, dan c.JSON di youtube",16,0.14516129032258066,45.0,40.0,0.425,5.0
user-85,202511,6.2,3.1,2,94,47.0,Afternoon Walk | gini gini aja friend,2,1.0,2,update tampilan web | update tampilan web,0.7,1.0,2,37,6,2,41,4,Afternoon Walk | gini gini aja friend | update tampilan web | update tampilan web,10,0.9024390243902439,100.0,40.0,0.7,60.0
user-85,202512,6.2,3.1,2,57,28.5,"lagi lagi, gini gini aja friend | gini gini aja fren",1,1.0,1,melanjutkan website,0.6,1.0,2,52,7,1,19,2,"lagi lagi, gini gini aja friend | gini gini aja fren | melanjutkan website",9,2.736842105263158,100.0,20.0,0.6,80.0
user-85,202521,3.1,3.1,1,38,38.0,Berjalan Sore,1,1.0,1,"pomodoro ulang, menyelamatkan manchester united",0.35,1.0,1,13,2,1,47,5,"Berjalan Sore | pomodoro ulang, menyelamatkan manchester united",7,0.2765957446808511,51.66666666666667,20.0,0.35833333333333334,31.66666666666667
user-86,202511,7.0,3.5,2,113,56.5,Mental Health | Mental Health #2,8,1.0,8,"edit content web portofolio | update konten personal-branding | continuing to improve personal-brand | revamping web personal-brand | improving web personal-branding | finishing touches, web and book | something to pass time with | ngabuburit in my way",1.0,1.0,2,32,4,8,252,26,"Mental Health | Mental Health #2 | edit content web portofolio | update konten personal-branding | continuing to improve personal-brand | revamping web personal-brand | improving web personal-branding | finishing touches, web and book | something to pass time with | ngabuburit in my way",30,0.12698412698412698,100.0,100.0,1.0,0.0
user-86,202520,2.6,2.6,1,21,21.0,Rain run,1,1.0,1,mencoba progresssssss,0.35,1.0,1,8,2,1,21,2,Rain run | mencoba progresssssss,4,0.38095238095238093,43.333333333333336,20.0,0.31666666666666665,23.333333333333336
user-86,202521,2.1,2.1,1,4,4.0,For points ahh run.,1,1.0,1,lanjut minggu ini buat bimbingan,0.35,1.0,1,19,4,1,32,5,For points ahh run. | lanjut minggu ini buat bimbingan,9,0.59375,35.0,20.0,0.275,15.0
user-86,202522,4.1,2.05,2,8,4.0,Monday activity | Flying,1,1.0,1,lanjut progress minggu ini,0.6,1.0,2,24,4,1,26,4,Monday activity | Flying | lanjut progress minggu ini,8,0.9230769230769231,68.33333333333333,20.0,0.44166666666666665,48.33333333333333
user-87,202519,7.4,3.7,2,63,31.5,Afternoon Run | Afternoon Run,1,1.0,1,Membuat artikel di medium,0.6,1.0,2,29,3,1,25,4,Afternoon Run | Afternoon Run | Membuat artikel di medium,7,1.16,100.0,20.0,0.6,80.0
user-87,202521,3.5,3.5,1,13,13.0,Evening Run,2,1.0,2,mengerjakan tugas artikel dan video youtube | Mengerjakan tugas video youtube,0.45,1.0,1,11,2,2,77,7,Evening Run | mengerjakan tugas artikel dan video youtube | Mengerjakan tugas video youtube,9,0.14285714285714285,58.333333333333336,40.0,0.4916666666666667,18.333333333333336
user-87,202522,7.4,3.7,2,16,8.0,Morning Run | Morning Run,2,1.0,2,Mengerjakan artikel medium untuk chapter 2 | Mengerjakan tugas video chapter 2,0.7,1.0,2,25,3,2,78,9,Morning Run | Morning Run | Mengerjakan artikel medium untuk chapter 2 | Mengerjakan tugas video chapter 2,12,0.32051282051282054,100.0,40.0,0.7,60.0
user-88,202511,3.4,3.4,1,15,15.0,Morning Run,5,1.0,5,lanjutin kerjaan sebelumnya | menambahkan file dan mengubah tampilan web | mengerjakan buku dasar-dasar fundamental | bekerja keras sampai tuntaas | lanjutin kerjaan semalam,0.75,1.0,1,11,2,5,173,19,Morning Run | lanjutin kerjaan sebelumnya | menambahkan file dan mengubah tampilan web | mengerjakan buku dasar-dasar fundamental | bekerja keras sampai tuntaas | lanjutin kerjaan semalam,21,0.06358381502890173,56.666666666666664,100.0,0.7833333333333333,43.333333333333336
user-89,202511,6.1,3.05,2,55,27.5,Afternoon Run | Afternoon Walk,7,1.0,7,perbaikan web portofolio | melanjutkan pembuatan website | melanjutkan website personal branding | melanjutkan website | mengerjakan buku pra assesment | melanjutkan buku fundamental | melanjutkan pembuatan website,1.0,1.0,2,30,4,7,214,14,Afternoon Run | Afternoon Walk | perbaikan web portofolio | melanjutkan pembuatan website | melanjutkan website personal branding | melanjutkan website | mengerjakan buku pra assesment | melanjutkan buku fundamental | melanjutkan pembuatan website,18,0.14018691588785046,100.0,100.0,1.0,0.0
user-89,202512,10.4,3.466666666666667,3,104,34.666666666666664,Afternoon Run | Afternoon Run | Afternoon Walk,2,1.0,2,website dan mining | melanjutkan website,0.7,1.0,3,46,4,2,40,5,Afternoon Run | Afternoon Run | Afternoon Walk | website dan mining | melanjutkan website,9,1.15,100.0,40.0,0.7,60.0
user-89,202522,4.5,4.5,1,31,31.0,Afternoon Walk,1,1.0,1,aku mau nambah poin,0.35,1.0,1,14,2,1,19,4,Afternoon Walk | aku mau nambah poin,6,0.7368421052631579,75.0,20.0,0.475,55.0
user-90,202511,3.7,3.7,1,51,51.0,Lunch Walk,8,1.0,8,membuat website personal branding | lanjut mengerjakan | menambahkan biodata | melanjutkan portofolio | menambahkan fitur | membuat tugas tugas tugas | merapihkan web portofolio | membuat web web web web,0.75,1.0,1,10,2,8,203,15,Lunch Walk | membuat website personal branding | lanjut mengerjakan | menambahkan biodata | melanjutkan portofolio | menambahkan fitur | membuat tugas tugas tugas | merapihkan web portofolio | membuat web web web web,17,0.04926108374384237,61.66666666666667,100.0,0.8083333333333335,38.33333333333333
user-91,202520,3.3,3.3,1,13,13.0,Sepeda Pagi,2,1.0,2,Membuat Tugas Hello World dengan Go dan GoFiber diupload di Youtube | Mengumpulkan Tugas Medium Untuk Membuat Hello World dengan Go dan GoFiber,0.45,1.0,1,11,2,2,143,15,Sepeda Pagi | Membuat Tugas Hello World dengan Go dan GoFiber diupload di Youtube | Mengumpulkan Tugas Medium Untuk Membuat Hello World dengan Go dan GoFiber,17,0.07692307692307693,54.99999999999999,40.0,0.475,14.999999999999993
user-91,202522,4.0,4.0,1,14,14.0,Sepeda ke 2,2,1.0,2,"Mengumpulkan Tugas Medium Membuat Struct, Fungsi, dan JSON Response di Golang serta Menguji dengan Postman | Mengumpulkan Tugas Youtube Membuat Struct, Fungsi, dan JSON Response di Golang serta Menguji dengan Postman",0.45,1.0,1,11,3,2,216,17,"Sepeda ke 2 | Mengumpulkan Tugas Medium Membuat Struct, Fungsi, dan JSON Response di Golang serta Menguji dengan Postman | Mengumpulkan Tugas Youtube Membuat Struct, Fungsi, dan JSON Response di Golang serta Menguji dengan Postman",20,0.05092592592592592,66.66666666666666,40.0,0.5333333333333333,26.666666666666657
user-92,202511,6.1,3.05,2,81,40.5,Afternoon walk | Two,4,1.0,4,keyword kaya sebelumnya | Melanjutkan web personal branding | Penambahan css yang belum di tambahkan | penyempurnaan website personal branding,0.9,1.0,2,20,4,4,142,16,Afternoon walk | Two | keyword kaya sebelumnya | Melanjutkan web personal branding | Penambahan css yang belum di tambahkan | penyempurnaan website personal branding,20,0.14084507042253522,100.0,80.0,0.9,20.0
user-92,202521,3.1,3.1,1,13,13.0,Jalan sore,2,1.0,2,Proyek 11111111111111111111111111111111111111111111111111 | melanjutkan yang belum,0.45,1.0,1,10,2,2,82,6,Jalan sore | Proyek 11111111111111111111111111111111111111111111111111 | melanjutkan yang belum,8,0.12195121951219512,51.66666666666667,40.0,0.45833333333333337,11.666666666666671
user-93,202511,6.1,3.05,2,85,42.5,Morning Walk | Afternoon Walk,5,1.0,5,melanjutkan website portofolio | lanjut website portofolio | melanjukan website dan mining | melanjutkam website branding | melanjutkan buku dan website,1.0,1.0,2,29,4,5,152,11,Morning Walk | Afternoon Walk | melanjutkan website portofolio | lanjut website portofolio | melanjukan website dan mining | melanjutkam website branding | melanjutkan buku dan website,15,0.19078947368421054,100.0,100.0,1.0,0.0
user-93,202520,24.9,4.9799999999999995,5,338,67.6,Morning Walk | Afternoon Walk | Morning Walk | Afternoon Walk | Afternoon Walk,1,1.0,1,melanjutkan tugass,0.6,1.0,5,78,4,1,18,2,Morning Walk | Afternoon Walk | Morning Walk | Afternoon Walk | Afternoon Walk | melanjutkan tugass,6,4.333333333333333,100.0,20.0,0.6,80.0
user-93,202521,5.1,5.1,1,82,82.0,Morning Walk,1,1.0,1,melakukan pomodoro,0.35,1.0,1,12,2,1,18,2,Morning Walk | melakukan pomodoro,4,0.6666666666666666,85.0,20.0,0.525,65.0
user-93,202522,13.0,6.5,2,198,99.0,Morning Walk | Morning Walk,1,1.0,1,melanjutkan pomodoro,0.6,1.0,2,27,3,1,20,2,Morning Walk | Morning Walk | melanjutkan pomodoro,5,1.35,100.0,20.0,0.6,80.0
user-94,202511,7.3,3.65,2,58,29.0,mental HEALTH | mental health,7,1.0,7,Membuat personal Branding | Melanjutkan Personal branding | lanjut personal branding | Melanjutkan website personal | melanjutkan personal branding | lanjut  personal branding | melanjutkan website dan buku,1.0,1.0,2,29,3,7,206,9,mental HEALTH | mental health | Membuat personal Branding | Melanjutkan Personal branding | lanjut personal branding | Melanjutkan website personal | melanjutkan personal branding | lanjut  personal branding | melanjutkan website dan buku,12,0.1407766990291262,100.0,100.0,1.0,0.0
user-95,202512,3.0,3.0,1,23,23.0,Afternoon Run,2,1.0,2,Chapter-1 mengerjakan student performance | Kuliah Praktisi Kecerdasan Buatan,0.45,1.0,1,13,2,2,77,9,Afternoon Run | Chapter-1 mengerjakan student performance | Kuliah Praktisi Kecerdasan Buatan,11,0.16883116883116883,50.0,40.0,0.45,10.0
user-95,202519,5.9,2.95,2,24,12.0,Berlari Pagi | Berlari Siang,4,1.0,4,"Mengerjakan Tugas Artikel Medium Decision Tree, Information Gain & Entrophy Random Forest | Tutorial Youtube DT dan RF | Mengerjakan Artikel medium DT dan RF | Tugas tutorial Youtube DT dan RF",0.9,1.0,2,28,4,4,192,18,"Berlari Pagi | Berlari Siang | Mengerjakan Tugas Artikel Medium Decision Tree, Information Gain & Entrophy Random Forest | Tutorial Youtube DT dan RF | Mengerjakan Artikel medium DT dan RF | Tugas tutorial Youtube DT dan RF",22,0.14583333333333334,98.33333333333334,80.0,0.8916666666666667,18.333333333333343
user-95,202521,8.600000000000001,4.300000000000001,2,28,14.0,Berlari Sore | Berlari Sore,2,1.0,2,Medium Chapter 2 :Memprediksi Spesies Bunga dengan Random Forest | mengerjakan youtube chapter 2,0.7,1.0,2,27,3,2,96,12,Berlari Sore | Berlari Sore | Medium Chapter 2 :Memprediksi Spesies Bunga dengan Random Forest | mengerjakan youtube chapter 2,15,0.28125,100.0,40.0,0.7,60.0
user-96,202512,10.8,5.4,2,59,29.5,Afternoon Walk | Afternoon Run,2,1.0,2,Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan,0.7,1.0,2,30,4,2,86,12,Afternoon Walk | Afternoon Run | Belajar modul python artificial intelligence | Kuliah Praktisi D4 TI Kecerdasan Buatan,16,0.3488372093023256,100.0,40.0,0.7,60.0
user-96,202519,2.4,2.4,1,10,10.0,Evening Ride,1,1.0,1,Membuat tutorial DT dan RF,0.35,1.0,1,12,2,1,26,5,Evening Ride | Membuat tutorial DT dan RF,7,0.46153846153846156,40.0,20.0,0.3,20.0
user-96,202520,2.8,2.8,1,49,49.0,Berjalan Pagi,1,1.0,1,Membuat Medium dan Tutorial YT regresi linear,0.35,1.0,1,13,2,1,45,7,Berjalan Pagi | Membuat Medium dan Tutorial YT regresi linear,9,0.28888888888888886,46.666666666666664,20.0,0.33333333333333326,26.666666666666664
user-96,202521,5.5,2.75,2,20,10.0,Bersepeda Siang | Bersepeda Sore,2,1.0,2,Mengerjakan medium Chapter 1 | Membuat Video Tutorial Chapter 01,0.7,1.0,2,32,4,2,64,9,Bersepeda Siang | Bersepeda Sore | Mengerjakan medium Chapter 1 | Membuat Video Tutorial Chapter 01,13,0.5,91.66666666666666,40.0,0.6583333333333333,51.66666666666666
user-97,202519,7.3,3.65,2,78,39.0,Tugas AI | Tugas AI pt2,2,1.0,2,"Membuat Artikel Medium Decision Tree, Information Gain & Entrophy Random Forest | Membuat Tutorial Youtube Decission Tree, Information Gain & Entropy, dan Random Forest",0.7,1.0,2,23,4,2,168,17,"Tugas AI | Tugas AI pt2 | Membuat Artikel Medium Decision Tree, Information Gain & Entrophy Random Forest | Membuat Tutorial Youtube Decission Tree, Information Gain & Entropy, dan Random Forest",21,0.13690476190476192,100.0,40.0,0.7,60.0
user-97,202521,10.1,5.05,2,413,206.5,Strava chapter 2 | Chapter 2,2,1.0,2,Mengerjakan Tugas Chapter 2 Artikel | Mengerjakan Tugas Chapter 2 Youtube,0.7,1.0,2,28,4,2,73,7,Strava chapter 2 | Chapter 2 | Mengerjakan Tugas Chapter 2 Artikel | Mengerjakan Tugas Chapter 2 Youtube,11,0.3835616438356164,100.0,40.0,0.7,60.0
user-97,202522,7.0,7.0,1,45,45.0,Chapter 3 part 2,2,1.0,2,Mengerjakan Chapter 3: Random Forest untuk Prediksi Penyakit | Mengerjakan Youtube Chapter 3,0.45,1.0,1,16,4,2,92,11,Chapter 3 part 2 | Mengerjakan Chapter 3: Random Forest untuk Prediksi Penyakit | Mengerjakan Youtube Chapter 3,15,0.17391304347826086,100.0,40.0,0.7,60.0
user-98,202511,6.1,3.05,2,83,41.5,Morning walk | Afternoon walk,7,1.0,7,membuat tampilan lainya | melanjutkan kembali edit tampilan | melanjutkan mining | melanjutkan pekerjaan | melanjutkan pekerjaan-pekerjaan | mengerjakan pekerjaan pekerjaan pekerjaan | melanjutkan lagi pekerjaan,1.0,1.0,2,29,4,7,211,12,Morning walk | Afternoon walk | membuat tampilan lainya | melanjutkan kembali edit tampilan | melanjutkan mining | melanjutkan pekerjaan | melanjutkan pekerjaan-pekerjaan | mengerjakan pekerjaan pekerjaan pekerjaan | melanjutkan lagi pekerjaan,16,0.13744075829383887,100.0,100.0,1.0,0.0
user-98,202520,10.7,5.35,2,73,36.5,Walk in the morning | walk again,1,1.0,1,melanjutkan tugas proyek 1,0.6,1.0,2,32,6,1,26,4,Walk in the morning | walk again | melanjutkan tugas proyek 1,10,1.2307692307692308,100.0,20.0,0.6,80.0
user-99,202512,3.5,3.5,1,35,35.0,Afternoon Run,1,1.0,1,Kuliah Praktisi Artificial Intelligence,0.35,1.0,1,13,2,1,39,4,Afternoon Run | Kuliah Praktisi Artificial Intelligence,6,0.3333333333333333,58.333333333333336,20.0,0.3916666666666667,38.333333333333336
user-99,202520,8.4,4.2,2,46,23.0,Night Run | Evening Run,3,1.0,3,"Mengerjakan Tugas 2 Decision Tree dan Random Forest medium | Mengerjakan Tugas 2 Medium Decision Tree, Random Forest, dan Information Gain | Membuat Tutorial Youtube Machine Learning dengan Model Decision Tree dan Random Forest",0.8,1.0,2,23,4,3,227,21,"Night Run | Evening Run | Mengerjakan Tugas 2 Decision Tree dan Random Forest medium | Mengerjakan Tugas 2 Medium Decision Tree, Random Forest, dan Information Gain | Membuat Tutorial Youtube Machine Learning dengan Model Decision Tree dan Random Forest",25,0.1013215859030837,100.0,60.0,0.8,40.0
user-100,202511,3.1,3.1,1,32,32.0,afternoon work,1,1.0,1,edit tampilan awal portofolio,0.35,1.0,1,14,2,1,29,4,afternoon work | edit tampilan awal portofolio,6,0.4827586206896552,51.66666666666667,20.0,0.35833333333333334,31.66666666666667
user-101,202511,6.300000000000001,3.1500000000000004,2,96,48.0,Jalan sambil nyari takjil | Walking at evening,5,1.0,5,melanjutkan website | tugas assesment sebelum projek | Lanjut tugas pomodoro | Lanjut pomodoro nih | Lanjut lah pomodoro nya,1.0,1.0,2,46,8,5,124,12,Jalan sambil nyari takjil | Walking at evening | melanjutkan website | tugas assesment sebelum projek | Lanjut tugas pomodoro | Lanjut pomodoro nih | Lanjut lah pomodoro nya,20,0.3709677419354839,100.0,100.0,1.0,0.0
user-101,202513,4.5,4.5,1,72,72.0,Evening evening with crowded,1,1.0,1,POMODORO tugaskan lagi cuuyyy,0.35,1.0,1,28,3,1,29,4,Evening evening with crowded | POMODORO tugaskan lagi cuuyyy,7,0.9655172413793104,75.0,20.0,0.475,55.0
user-102,202519,4.9,4.9,1,9,9.0,Evening Run,2,1.0,2,Mengerjakan tugas membuat artikel medium | Pengerjaan vidio youtube tentang RF dan DT,0.45,1.0,1,11,2,2,85,13,Evening Run | Mengerjakan tugas membuat artikel medium | Pengerjaan vidio youtube tentang RF dan DT,15,0.12941176470588237,81.66666666666667,40.0,0.6083333333333334,41.66666666666667
user-102,202521,22.7,11.35,2,54,27.0,Morning Ride | Night Ride,3,1.0,3,"Chapter 1 : pembuatan vidio yt dan penggunaan dataset cuaca menggunaan DT | Chapter 2 : membuat fungsi testing tambahkan di bagian bawah file test_app.py, fungsi test dengan nama : test_03_nama_npm, yang berisi untuk melakukan testing tiga fungsi dari dataset cuaca pada artikel medium | Chapter 02: Membuat vidio youtube menggunakan algoritma RF dan dataset cuaca di negara Australia",0.8,1.0,2,25,4,3,384,43,"Morning Ride | Night Ride | Chapter 1 : pembuatan vidio yt dan penggunaan dataset cuaca menggunaan DT | Chapter 2 : membuat fungsi testing tambahkan di bagian bawah file test_app.py, fungsi test dengan nama : test_03_nama_npm, yang berisi untuk melakukan testing tiga fungsi dari dataset cuaca pada artikel medium | Chapter 02: Membuat vidio youtube menggunakan algoritma RF dan dataset cuaca di negara Australia",47,0.06510416666666667,100.0,60.0,0.8,40.0
user-102,202522,10.9,10.9,1,37,37.0,Morning Ride,2,1.0,2,Chapter 3 : Mengerjakan artikel medium tentang bag of word | Chapter 3 : mengerjakan pembuatan vidio yt tentang Bag of Word,0.45,1.0,1,12,2,2,123,14,Morning Ride | Chapter 3 : Mengerjakan artikel medium tentang bag of word | Chapter 3 : mengerjakan pembuatan vidio yt tentang Bag of Word,16,0.0975609756097561,100.0,40.0,0.7,60.0
user-103,202519,5.0,5.0,1,9,9.0,Berlari Sore,1,1.0,1,Mengerjakan tugas artikel di medium,0.35,1.0,1,12,2,1,35,5,Berlari Sore | Mengerjakan tugas artikel di medium,7,0.34285714285714286,83.33333333333334,20.0,0.5166666666666667,63.33333333333334
user-103,202521,10.399999999999999,5.199999999999999,2,32,16.0,Bersepeda Sore | Bersepeda Sore,4,1.0,4,Mengerjakan tugas ke-1 mengenai regresi linier dan svm | Video yt tugas 1 mengenai RegresiLinier dan SVM | Artikel Medium Chapter 1 | Membuat Video Chapter 1,0.9,1.0,2,31,3,4,157,17,Bersepeda Sore | Bersepeda Sore | Mengerjakan tugas ke-1 mengenai regresi linier dan svm | Video yt tugas 1 mengenai RegresiLinier dan SVM | Artikel Medium Chapter 1 | Membuat Video Chapter 1,20,0.19745222929936307,100.0,80.0,0.9,20.0
user-103,202523,5.6,5.6,1,16,16.0,Bersepeda Sore,2,1.0,2,Membuat artikel medium chapter 1 | Membuat video youtube chapter 1,0.45,1.0,1,14,2,2,66,8,Bersepeda Sore | Membuat artikel medium chapter 1 | Membuat video youtube chapter 1,10,0.21212121212121213,93.33333333333333,40.0,0.6666666666666665,53.33333333333333
user-104,202511,3.4,3.4,1,44,44.0,Afternoon Walk,3,1.0,3,Melanjutkan portofolio | Melanjutkan Portofolio | Menambahkan yang sudah di kerjakan,0.55,1.0,1,14,2,3,84,8,Afternoon Walk | Melanjutkan portofolio | Melanjutkan Portofolio | Menambahkan yang sudah di kerjakan,10,0.16666666666666666,56.666666666666664,60.0,0.5833333333333333,3.3333333333333357
user-105,202511,4.4,4.4,1,53,53.0,Walking in the afternoon,8,1.0,8,"Melanjutkan website | melanjutkan website | Melanjutkan website | Melanjutkan website, buku | mencoba apapun itu | menambang koin, mengerjakan apapun itu | mining tanpa batas | mining sampai jebol",0.75,1.0,1,24,4,8,196,16,"Walking in the afternoon | Melanjutkan website | melanjutkan website | Melanjutkan website | Melanjutkan website, buku | mencoba apapun itu | menambang koin, mengerjakan apapun itu | mining tanpa batas | mining sampai jebol",20,0.12244897959183673,73.33333333333334,100.0,0.8666666666666667,26.666666666666657
user-105,202513,4.2,4.2,1,67,67.0,Ngabuburit,1,1.0,1,melakukan kegiatan menyenangkan,0.35,1.0,1,10,1,1,31,3,Ngabuburit | melakukan kegiatan menyenangkan,4,0.3225806451612903,70.0,20.0,0.45,50.0
user-106,202520,4.8,4.8,1,31,31.0,Berlari Sore,1,1.0,1,"Membuat Artikel medium, random forest dan decision tree",0.35,1.0,1,12,2,1,55,8,"Berlari Sore | Membuat Artikel medium, random forest dan decision tree",10,0.21818181818181817,80.0,20.0,0.5,60.0
user-106,202521,3.6,3.6,1,28,28.0,Berlari Sore,3,1.0,3,Video yt DT dan RF | menyelesaikan medium chapter 1 | Mengerjakan Chapter 1 Video yt,0.55,1.0,1,12,2,3,84,11,Berlari Sore | Video yt DT dan RF | menyelesaikan medium chapter 1 | Mengerjakan Chapter 1 Video yt,13,0.14285714285714285,60.0,60.0,0.6,0.0
user-106,202522,4.5,4.5,1,18,18.0,Bersepeda Sore,2,1.0,2,medium chapter 2 random forest | youtube chapter 2 random forest,0.45,1.0,1,14,2,2,64,7,Bersepeda Sore | medium chapter 2 random forest | youtube chapter 2 random forest,9,0.21875,75.0,40.0,0.575,35.0
