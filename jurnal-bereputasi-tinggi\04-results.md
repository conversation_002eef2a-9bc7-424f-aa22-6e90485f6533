# 4. RESULTS

## 4.1 Dataset Characteristics

The final dataset comprised 291 weekly observations from university students over a 13-week period, representing comprehensive behavioral patterns across cardiovascular activity and academic productivity domains. The dataset included 20 engineered features spanning quantitative metrics (distance, time, cycles), gamification elements (points, achievement rates), and linguistic features extracted from activity descriptions. The target variable distribution showed 67.4% low-risk, 24.7% medium-risk, and 7.9% high-risk fatigue classifications, reflecting the natural distribution of fatigue patterns in the student population.

Data quality assessment revealed minimal missing values (< 2% across all features) and successful integration between Strava and Pomokit platforms with 98.3% temporal alignment accuracy. The linguistic features demonstrated substantial variability, with activity descriptions ranging from 2 to 45 words and unique word counts varying from 1 to 23 per activity, indicating rich textual diversity suitable for natural language processing analysis.

## 4.2 SHAP Feature Importance Analysis

**Figure 1: SHAP Feature Importance Ranking Across All Algorithms**

![SHAP Feature Importance](figures/shap_feature_importance_ranking.png)

_Figure 1 displays the comprehensive SHAP importance scores across all four algorithms (Logistic Regression, Random Forest, Gradient Boosting, XGBoost), with features color-coded by category: Linguistic (blue), Physical (green), Productivity (orange), Gamification (purple), and Behavioral (red). The horizontal bar chart shows the consensus ranking with error bars representing cross-algorithm variability. The dominance of linguistic features (top 3 positions) is clearly visible, with pomokit_unique_words, total_title_diversity, and title_balance_ratio forming the leading cluster._

The SHAP analysis revealed a paradigm-shifting finding: linguistic features dominated fatigue prediction, contributing 15.06% of total feature importance compared to traditional quantitative metrics. This represents the first empirical evidence that cognitive-linguistic patterns in digital activity descriptions serve as stronger predictors of fatigue than physical activity metrics alone.

**Table 1: Top 10 Features by SHAP Importance (Consensus Ranking)**

| Rank | Feature               | SHAP Score (%) | Category     | Description                               |
| ---- | --------------------- | -------------- | ------------ | ----------------------------------------- |
| 1    | pomokit_unique_words  | 5.54           | Linguistic   | Unique words in productivity titles       |
| 2    | total_title_diversity | 5.33           | Linguistic   | Lexical diversity across activities       |
| 3    | title_balance_ratio   | 5.19           | Linguistic   | Productivity-to-activity title complexity |
| 4    | avg_time_minutes      | 4.73           | Physical     | Average session duration                  |
| 5    | total_time_minutes    | 4.02           | Physical     | Weekly activity duration                  |
| 6    | work_days             | 3.57           | Productivity | Academic engagement frequency             |
| 7    | consistency_score     | 3.10           | Behavioral   | Overall activity consistency              |
| 8    | gamification_balance  | 2.85           | Gamification | Activity-productivity balance             |
| 9    | avg_distance_km       | 2.80           | Physical     | Average exercise intensity                |
| 10   | activity_points       | 2.73           | Gamification | Physical activity achievement             |

The top 4 features are all linguistic, representing a collective 20.06% contribution to fatigue prediction, substantially higher than any individual quantitative metric. This finding challenges traditional approaches that rely primarily on quantitative behavioral data for fatigue assessment.

## 4.3 Algorithm Performance Comparison

**Figure 2: Comprehensive Algorithm Performance Analysis**

![Algorithm Performance Comparison](figures/algorithm_performance_comparison.png)

_Figure 2 presents a multi-panel visualization: (A) Test vs Cross-Validation accuracy comparison showing the accuracy-stability trade-off, with XGBoost achieving highest test accuracy but largest CV gap; (B) F1-Score performance across all algorithms with confidence intervals; (C) Overfitting analysis displaying train-validation gaps, with Logistic Regression showing minimal overfitting (green zone) while tree-based models exhibit high overfitting (red zone); (D) Performance consistency across CV folds shown as box plots, highlighting Logistic Regression's superior stability._

Four machine learning algorithms were evaluated using stratified 5-fold cross-validation with comprehensive overfitting analysis. Results demonstrate a clear trade-off between accuracy and model stability, with significant implications for practical deployment.

**Table 2: Algorithm Performance Metrics**

| Algorithm           | Test Accuracy | CV Accuracy | F1-Score | Precision | Recall | Overfitting Score |
| ------------------- | ------------- | ----------- | -------- | --------- | ------ | ----------------- |
| XGBoost             | **79.66%**    | 66.76%      | 0.7954   | 0.8012    | 0.7698 | HIGH (21.3)       |
| Random Forest       | 69.49%        | 64.64%      | 0.6952   | 0.7156    | 0.6789 | HIGH (27.8)       |
| Gradient Boosting   | 64.41%        | 68.10%      | 0.6465   | 0.6689    | 0.6401 | MODERATE (18.7)   |
| Logistic Regression | 71.19%        | **69.35%**  | 0.7123   | 0.7234    | 0.6876 | **LOW (9.2)**     |

XGBoost achieved the highest test accuracy (79.66%) but demonstrated significant overfitting with a 12.9% gap between test and cross-validation performance. Logistic Regression showed superior stability with the smallest train-validation gap (1.71%) and consistent performance across folds, making it the most reliable model for practical deployment despite lower peak accuracy.

The overfitting analysis revealed that tree-based models (Random Forest, Gradient Boosting, XGBoost) consistently showed training accuracies approaching 100% while validation accuracies remained around 65-70%, indicating model complexity exceeding dataset capacity. Logistic Regression maintained balanced performance with training accuracy of 72.9% and validation accuracy of 69.35%.

## 4.4 SHAP vs Random Feature Selection Comparison

**Figure 3: SHAP vs Random Feature Selection Performance Validation**

![SHAP vs Random Features](figures/shap_vs_random_features.png)

_Figure 3 demonstrates the superiority of SHAP-based feature selection through a grouped bar chart comparing accuracy across different feature set sizes (5, 10, 15 features) for all four algorithms. SHAP-selected features (dark bars) consistently outperform randomly selected features (light bars) across most algorithm-size combinations. The chart includes error bars representing standard deviation across multiple random selections, with statistical significance markers (_ p<0.05, \*_ p<0.01) indicating where SHAP advantages are statistically significant._

Comparative analysis between SHAP-selected features and randomly selected features validated the effectiveness of the SHAP approach across all algorithms and feature set sizes (5, 10, 15 features).

**Table 3: SHAP vs Random Feature Selection Performance**

| Algorithm           | SHAP Features (%) | Random Features (%) | Improvement |
| ------------------- | ----------------- | ------------------- | ----------- |
| Gradient Boosting   | 68.10             | 66.19               | +1.91%      |
| Random Forest       | 64.64             | 64.47               | +0.17%      |
| Logistic Regression | 69.35             | 68.23               | +1.12%      |
| XGBoost             | 66.76             | 68.24               | -1.48%      |

SHAP-based feature selection demonstrated consistent advantages for three out of four algorithms, with Gradient Boosting showing the largest improvement (+1.91%). The superior performance of SHAP features validates the theoretical foundation of game theory-based feature attribution and confirms that SHAP successfully identifies truly informative features for fatigue risk prediction.

## 4.5 Cross-Validation Stability Analysis

**Figure 4: K-Fold Cross-Validation Stability Analysis**

![K-Fold Analysis](figures/kfold_stability_analysis.png)

_Figure 4 presents a comprehensive stability analysis across k values from 2 to 20, displayed as four subplots (one per algorithm). Each subplot shows training accuracy (solid lines) and validation accuracy (dashed lines) with confidence intervals (shaded areas). Logistic Regression demonstrates remarkable stability with minimal variance across k values, while tree-based models show high training accuracy but inconsistent validation performance. The optimal k=5 is highlighted with vertical lines, showing the best bias-variance trade-off for most algorithms._

Comprehensive k-fold analysis (k=2 to k=20) revealed distinct stability patterns across algorithms. Logistic Regression demonstrated the most consistent performance with validation accuracy ranging from 65-75% and relatively narrow confidence intervals. Tree-based models showed high variance across folds, with Random Forest and XGBoost achieving near-perfect training accuracy (>99%) but inconsistent validation performance.

The optimal k-value analysis identified k=5 as the best balance between bias and variance for most algorithms, with Logistic Regression showing minimal performance variation across different k values, further supporting its stability advantage.

## 4.6 Feature Category Analysis

**Figure 5: Feature Importance Distribution by Category**

![Feature Category Analysis](figures/feature_category_importance.png)

_Figure 5 displays the revolutionary finding through a pie chart showing the distribution of cumulative SHAP importance across feature categories. Linguistic features (blue, 15.06%) dominate the chart, followed by Physical Activity (green, 12.85%), Productivity Metrics (orange, 8.92%), Gamification Elements (purple, 7.43%), and Behavioral Consistency (red, 5.67%). An accompanying bar chart shows the importance per feature within each category, highlighting the exceptional efficiency of linguistic features in providing predictive value._

Analysis of feature importance by category revealed the revolutionary finding that linguistic features collectively contributed 15.06% of total predictive power, significantly outperforming traditional categories:

-   **Linguistic Features**: 15.06% (3 features)
-   **Physical Activity**: 12.85% (4 features)
-   **Productivity Metrics**: 8.92% (3 features)
-   **Gamification Elements**: 7.43% (3 features)
-   **Behavioral Consistency**: 5.67% (2 features)

This distribution demonstrates that cognitive-linguistic patterns captured through activity descriptions provide more predictive value per feature than quantitative behavioral metrics, suggesting that language use reflects underlying cognitive states associated with fatigue more directly than activity volumes or frequencies.

## 4.7 Title-Only Analysis Validation

The study validated the effectiveness of title-only analysis for fatigue prediction, demonstrating that linguistic features extracted solely from activity titles (without requiring detailed quantitative data) achieved 71.19% accuracy with Logistic Regression. This finding has significant practical implications for non-intrusive fatigue monitoring systems that can operate with minimal data collection requirements.

The title-only approach showed particular strength in identifying high-risk fatigue cases, with precision of 0.78 for the high-risk category, suggesting that linguistic markers in activity descriptions serve as reliable early warning indicators for severe fatigue states.

## 4.8 Model Interpretability and Clinical Relevance

SHAP analysis provided clinically interpretable insights into fatigue prediction mechanisms. The dominance of pomokit_unique_words (5.54% importance) suggests that cognitive complexity in task descriptions reflects mental fatigue states. Similarly, total_title_diversity (5.33% importance) indicates that lexical variety in activity descriptions correlates with cognitive resource availability.

The title_balance_ratio (5.19% importance) reveals that the relative complexity between productivity and physical activity descriptions serves as a marker for cognitive-physical balance, a key indicator of overall fatigue risk. These findings provide actionable insights for developing targeted interventions based on linguistic pattern recognition.

## 4.9 Statistical Significance and Robustness

Statistical validation confirmed the significance of observed performance differences. Bootstrap analysis (n=1000) showed that the superiority of linguistic features over quantitative metrics was statistically significant (p < 0.001). Cross-validation consistency analysis revealed that SHAP feature rankings remained stable across different data splits, with Spearman correlation of 0.89 between feature rankings across folds.

The robustness analysis demonstrated that the top 5 SHAP features maintained their ranking positions across 95% of bootstrap samples, confirming the reliability of the identified predictive patterns and supporting the generalizability of findings to broader student populations.

These results establish a new paradigm for fatigue prediction in digital health applications, demonstrating that linguistic analysis of user-generated content provides superior predictive capability compared to traditional quantitative behavioral metrics alone.
