# 🖥️ Spesifikasi Input untuk Tampilan Prediksi

Panduan lengkap untuk membuat tampilan/interface prediksi risiko kelelahan.

## 📋 Input Fields yang Dibutuhkan

### **18 Field Input Wajib:**

| No | Field Name | Type | Range | Default | Description | UI Component |
|----|------------|------|-------|---------|-------------|--------------|
| 1 | `productivity_points` | Float | 0-200 | 85.5 | Poin produktivitas pengguna | Number Input |
| 2 | `total_time_minutes` | Integer | 0-10000 | 1800 | Total waktu aktivitas (menit) | Number Input |
| 3 | `strava_unique_words` | Integer | 0-50 | 12 | Jumlah kata unik dalam judul Strava | Number Input |
| 4 | `achievement_rate` | Float | 0.0-1.0 | 0.85 | Tingkat pencapaian | Slider |
| 5 | `avg_cycles` | Float | 0-20 | 3.2 | Rata-rata siklus aktivitas | Number Input |
| 6 | `avg_distance_km` | Float | 0-100 | 5.4 | Rata-rata jarak (km) | Number Input |
| 7 | `total_title_diversity` | Float | 0-50 | 8.5 | Keragaman judul aktivitas | Number Input |
| 8 | `pomokit_title_count` | Integer | 0-100 | 15 | Jumlah judul Pomokit | Number Input |
| 9 | `activity_points` | Float | 0-500 | 120.0 | Poin aktivitas | Number Input |
| 10 | `pomokit_title_length` | Float | 0-100 | 25.3 | Panjang rata-rata judul Pomokit | Number Input |
| 11 | `title_balance_ratio` | Float | 0.0-2.0 | 0.75 | Rasio keseimbangan judul | Slider |
| 12 | `strava_title_count` | Integer | 0-100 | 8 | Jumlah judul Strava | Number Input |
| 13 | `total_distance_km` | Float | 0-1000 | 45.2 | Total jarak (km) | Number Input |
| 14 | `strava_title_length` | Float | 0-100 | 18.5 | Panjang rata-rata judul Strava | Number Input |
| 15 | `pomokit_unique_words` | Integer | 0-50 | 10 | Jumlah kata unik Pomokit | Number Input |
| 16 | `weekly_efficiency` | Float | 0.0-1.0 | 0.82 | Efisiensi mingguan | Slider |
| 17 | `gamification_balance` | Float | 0-1000 | 150.0 | Saldo gamifikasi | Number Input |
| 18 | `avg_time_minutes` | Integer | 0-10000 | 45 | Rata-rata waktu (menit) | Number Input |

## 🎨 Rekomendasi UI Design

### **1. Layout Struktur**
```
┌─────────────────────────────────────────┐
│ Header: Fatigue Risk Prediction System  │
├─────────────────────────────────────────┤
│ Model Info Sidebar │ Main Input Area    │
│ - Algorithm        │ ┌─────────────────┐ │
│ - Accuracy         │ │ Tab 1: Produktiv│ │
│ - Features         │ │ Tab 2: Waktu    │ │
│                    │ │ Tab 3: Strava   │ │
│                    │ │ Tab 4: Pomokit  │ │
│                    │ │ Tab 5: Lainnya  │ │
│                    │ └─────────────────┘ │
│                    │ [Predict Button]   │
├─────────────────────────────────────────┤
│ Results Area                            │
│ - Risk Level                            │
│ - Probability Chart                     │
│ - Recommendations                       │
└─────────────────────────────────────────┘
```

### **2. Kategorisasi Input (Tabs/Sections)**

#### **Tab 1: 🎯 Produktivitas**
- `productivity_points` (Number Input: 0-200)
- `activity_points` (Number Input: 0-500)

#### **Tab 2: ⏰ Waktu & Efisiensi**
- `total_time_minutes` (Number Input: 0-10000)
- `avg_time_minutes` (Number Input: 0-10000)
- `weekly_efficiency` (Slider: 0.0-1.0)

#### **Tab 3: 🏃 Strava**
- `strava_title_count` (Number Input: 0-100)
- `strava_unique_words` (Number Input: 0-50)
- `strava_title_length` (Number Input: 0-100)

#### **Tab 4: 📚 Pomokit**
- `pomokit_title_count` (Number Input: 0-100)
- `pomokit_unique_words` (Number Input: 0-50)
- `pomokit_title_length` (Number Input: 0-100)

#### **Tab 5: 📊 Lainnya**
- `achievement_rate` (Slider: 0.0-1.0)
- `avg_cycles` (Number Input: 0-20)
- `avg_distance_km` (Number Input: 0-100)
- `total_title_diversity` (Number Input: 0-50)
- `title_balance_ratio` (Slider: 0.0-2.0)
- `total_distance_km` (Number Input: 0-1000)
- `gamification_balance` (Number Input: 0-1000)

## 🔧 Implementasi Backend

### **Data Processing Function**
```python
def process_prediction_input(form_data):
    """
    Process form data for prediction
    
    Args:
        form_data: Dictionary with 18 required fields
        
    Returns:
        tuple: (predicted_class, probabilities, success)
    """
    required_fields = [
        'productivity_points', 'total_time_minutes', 'strava_unique_words',
        'achievement_rate', 'avg_cycles', 'avg_distance_km',
        'total_title_diversity', 'pomokit_title_count', 'activity_points',
        'pomokit_title_length', 'title_balance_ratio', 'strava_title_count',
        'total_distance_km', 'strava_title_length', 'pomokit_unique_words',
        'weekly_efficiency', 'gamification_balance', 'avg_time_minutes'
    ]
    
    # Validate input
    missing_fields = [field for field in required_fields if field not in form_data]
    if missing_fields:
        return None, None, False, f"Missing fields: {missing_fields}"
    
    # Load model and predict
    try:
        artifacts = load_model_artifacts("results/clean_production_model")
        model = artifacts['pipeline']
        features = artifacts['features']
        label_encoder = artifacts['label_encoder']
        
        # Prepare data
        df = pd.DataFrame([form_data])
        X = df[features]
        
        # Predict
        prediction = model.predict(X)[0]
        probabilities = model.predict_proba(X)[0]
        predicted_class = label_encoder.inverse_transform([prediction])[0]
        
        # Format probabilities
        classes = label_encoder.classes_
        prob_dict = {cls: float(prob) for cls, prob in zip(classes, probabilities)}
        
        return predicted_class, prob_dict, True, "Success"
        
    except Exception as e:
        return None, None, False, str(e)
```

## 📱 Framework-Specific Examples

### **1. Streamlit (Python Web App)**
```bash
# Install: pip install streamlit plotly
# Run: streamlit run web_interface_example.py
```

### **2. Flask (Python Web Framework)**
```python
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('prediction_form.html')

@app.route('/predict', methods=['POST'])
def predict():
    form_data = request.json
    predicted_class, probabilities, success, message = process_prediction_input(form_data)
    
    if success:
        return jsonify({
            'success': True,
            'prediction': predicted_class,
            'probabilities': probabilities
        })
    else:
        return jsonify({'success': False, 'error': message})
```

### **3. HTML Form Template**
```html
<form id="predictionForm">
    <!-- Produktivitas -->
    <fieldset>
        <legend>🎯 Produktivitas</legend>
        <label>Productivity Points (0-200):</label>
        <input type="number" name="productivity_points" min="0" max="200" step="0.1" value="85.5" required>
        
        <label>Activity Points (0-500):</label>
        <input type="number" name="activity_points" min="0" max="500" step="0.1" value="120.0" required>
    </fieldset>
    
    <!-- Waktu -->
    <fieldset>
        <legend>⏰ Waktu & Efisiensi</legend>
        <label>Total Time (minutes):</label>
        <input type="number" name="total_time_minutes" min="0" max="10000" value="1800" required>
        
        <label>Weekly Efficiency (0.0-1.0):</label>
        <input type="range" name="weekly_efficiency" min="0" max="1" step="0.01" value="0.82" required>
    </fieldset>
    
    <!-- Continue for all 18 fields... -->
    
    <button type="submit">🔮 Predict Risk</button>
</form>
```

### **4. React Component (JavaScript)**
```jsx
const PredictionForm = () => {
    const [formData, setFormData] = useState({
        productivity_points: 85.5,
        total_time_minutes: 1800,
        // ... all 18 fields with defaults
    });
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        const response = await fetch('/api/predict', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(formData)
        });
        const result = await response.json();
        // Handle result...
    };
    
    return (
        <form onSubmit={handleSubmit}>
            {/* Input fields for all 18 features */}
        </form>
    );
};
```

## 📊 Output Display

### **Hasil Prediksi yang Ditampilkan:**
1. **Risk Level**: `low_risk`, `medium_risk`, `high_risk`
2. **Confidence**: Probabilitas tertinggi (0-100%)
3. **Probability Distribution**: Chart untuk semua kelas
4. **Recommendations**: Saran berdasarkan hasil prediksi

### **Format Output JSON:**
```json
{
    "success": true,
    "prediction": "medium_risk",
    "probabilities": {
        "low_risk": 0.1234,
        "medium_risk": 0.7456,
        "high_risk": 0.1310
    },
    "confidence": 74.56,
    "recommendations": [
        "Perhatikan pola istirahat dan aktivitas",
        "Lakukan aktivitas ringan untuk recovery"
    ]
}
```

## 🎯 Tips Implementasi

1. **Validasi Input**: Pastikan semua 18 field terisi dan dalam range yang benar
2. **User Experience**: Gunakan tabs/sections untuk mengelompokkan input
3. **Default Values**: Berikan nilai default yang masuk akal
4. **Help Text**: Tambahkan tooltip/help untuk setiap field
5. **Error Handling**: Tangani error dengan pesan yang jelas
6. **Loading State**: Tampilkan loading saat prediksi berlangsung
7. **Responsive Design**: Pastikan tampilan baik di mobile dan desktop

## 🚀 Quick Start

1. **Gunakan Streamlit** (termudah):
   ```bash
   pip install streamlit plotly
   streamlit run web_interface_example.py
   ```

2. **Atau buat custom dengan framework pilihan Anda**
3. **Pastikan model sudah dilatih**: `python main1.py --ml-only`
4. **Test dengan data contoh** sebelum production
