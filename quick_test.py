import sys
sys.path.append('src')
from utils.data_utils import load_model_artifacts
import pandas as pd
from pathlib import Path

print('🧪 Testing Model Loading and Prediction')
print('='*50)

# Load model artifacts
model_dir = Path('results/clean_production_model')

try:
    print('📂 Loading model artifacts...')
    artifacts = load_model_artifacts(model_dir)
    
    # Check what was loaded
    print(f'✅ Loaded artifacts: {list(artifacts.keys())}')
    
    if 'metadata' in artifacts:
        metadata = artifacts['metadata']
        print(f'📊 Model Info:')
        print(f'   • Algorithm: {metadata.get("algorithm_name", "Unknown")}')
        print(f'   • Accuracy: {metadata.get("accuracy", 0.0):.4f} ({metadata.get("accuracy", 0.0)*100:.2f}%)')
        print(f'   • Features: {metadata.get("feature_count", 0)}')
        print(f'   • Is Best Model: {metadata.get("is_best_model", False)}')
    
    # Test prediction
    if 'pipeline' in artifacts and 'features' in artifacts:
        print()
        print('🔮 Testing model prediction...')
        
        # Load test data
        data_path = 'dataset/processed/safe_ml_fatigue_dataset.csv'
        if Path(data_path).exists():
            data = pd.read_csv(data_path)
            
            # Get features
            feature_names = artifacts['features']
            X_test = data[feature_names].head(3)
            y_true = data['fatigue_risk'].head(3)
            
            # Make predictions
            model = artifacts['pipeline']
            y_pred = model.predict(X_test)
            
            # Decode if possible
            if 'label_encoder' in artifacts:
                y_pred_decoded = artifacts['label_encoder'].inverse_transform(y_pred)
                print('✅ Predictions successful!')
                print('   Sample predictions:')
                for i, (true_val, pred_val) in enumerate(zip(y_true, y_pred_decoded)):
                    print(f'     Sample {i+1}: True={true_val}, Predicted={pred_val}')
            else:
                print(f'✅ Predictions successful (encoded): {y_pred}')
        else:
            print(f'⚠️  Test data not found: {data_path}')
    
    print()
    print('🎉 Model loading test completed successfully!')
    
except Exception as e:
    print(f'❌ Error: {str(e)}')
    import traceback
    traceback.print_exc()
